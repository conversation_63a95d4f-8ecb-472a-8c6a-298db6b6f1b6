"""
API Views للنظام الموحد للحصص والمراقبة
"""

import json
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views import View
from django.utils import timezone
from .universal_manager import UniversalLessonManager
from .models import UniversalLessonMonitoring


@method_decorator(login_required, name='dispatch')
@method_decorator(csrf_exempt, name='dispatch')
class UniversalMonitoringAPI(View):
    """API موحد لمراقبة جميع أنواع الحصص"""
    
    def post(self, request):
        """معالجة طلبات POST للمراقبة"""
        try:
            data = json.loads(request.body)
            action = data.get('action')
            
            if action == 'start_monitoring':
                return self.start_lesson_monitoring(request, data)
            elif action == 'heartbeat':
                return self.process_heartbeat(request, data)
            elif action == 'confirm_attendance':
                return self.confirm_attendance(request, data)
            elif action == 'mark_leave':
                return self.mark_leave(request, data)
            elif action == 'get_status':
                return self.get_monitoring_status(request, data)
            else:
                return JsonResponse({'status': 'error', 'message': 'إجراء غير مدعوم'})
                
        except json.JSONDecodeError:
            return JsonResponse({'status': 'error', 'message': 'بيانات JSON غير صحيحة'})
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})
    
    def start_lesson_monitoring(self, request, data):
        """بدء مراقبة الحصة"""
        lesson_type = data.get('lesson_type')
        lesson_id = data.get('lesson_id')
        user_role = data.get('user_role', 'student')
        
        if not lesson_type or not lesson_id:
            return JsonResponse({
                'status': 'error', 
                'message': 'نوع الحصة ومعرف الحصة مطلوبان'
            })
        
        result = UniversalLessonManager.start_lesson_monitoring(
            lesson_type, lesson_id, request.user.id, user_role
        )
        
        return JsonResponse(result)
    
    def process_heartbeat(self, request, data):
        """معالجة heartbeat"""
        monitoring_id = data.get('monitoring_id')
        
        if not monitoring_id:
            return JsonResponse({
                'status': 'error', 
                'message': 'معرف المراقبة مطلوب'
            })
        
        # إضافة معلومات إضافية للـ heartbeat
        heartbeat_data = {
            'timestamp': timezone.now().isoformat(),
            'page_visible': data.get('page_visible', True),
            'browser_info': {
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'screen_resolution': data.get('screen_resolution'),
                'browser_language': data.get('browser_language'),
                'timezone': data.get('timezone')
            },
            'connection_quality': data.get('connection_quality', 'good'),
            'activity_level': data.get('activity_level', 'normal')
        }
        
        result = UniversalLessonManager.process_heartbeat(monitoring_id, heartbeat_data)
        
        return JsonResponse(result)
    
    def confirm_attendance(self, request, data):
        """تأكيد الحضور"""
        monitoring_id = data.get('monitoring_id')
        
        if not monitoring_id:
            return JsonResponse({
                'status': 'error', 
                'message': 'معرف المراقبة مطلوب'
            })
        
        result = UniversalLessonManager.confirm_attendance(monitoring_id)
        
        return JsonResponse(result)
    
    def mark_leave(self, request, data):
        """تسجيل المغادرة"""
        monitoring_id = data.get('monitoring_id')
        
        if not monitoring_id:
            return JsonResponse({
                'status': 'error', 
                'message': 'معرف المراقبة مطلوب'
            })
        
        result = UniversalLessonManager.mark_attendance_leave(monitoring_id)
        
        return JsonResponse(result)
    
    def get_monitoring_status(self, request, data):
        """الحصول على حالة المراقبة"""
        monitoring_id = data.get('monitoring_id')
        
        if not monitoring_id:
            return JsonResponse({
                'status': 'error', 
                'message': 'معرف المراقبة مطلوب'
            })
        
        try:
            monitoring = UniversalLessonMonitoring.objects.get(id=monitoring_id)
            
            return JsonResponse({
                'status': 'success',
                'monitoring_status': monitoring.status,
                'quality_score': monitoring.quality_score,
                'attendance_reliability': monitoring.attendance_reliability,
                'heartbeat_count': monitoring.total_heartbeats_received,
                'warnings_count': monitoring.warnings_issued,
                'fraud_risk_level': monitoring.get_fraud_risk_level(),
                'last_heartbeat': monitoring.last_heartbeat.isoformat() if monitoring.last_heartbeat else None,
                'page_visibility_changes': monitoring.page_visibility_changes
            })
            
        except UniversalLessonMonitoring.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': 'سجل المراقبة غير موجود'})


@login_required
@require_http_methods(["GET"])
def get_lesson_monitoring_data(request, lesson_type, lesson_id):
    """الحصول على بيانات مراقبة الحصة"""
    try:
        # البحث عن سجل المراقبة
        filter_kwargs = {
            'user': request.user,
            f'{lesson_type}_lesson_id': lesson_id
        }
        
        monitoring = UniversalLessonMonitoring.objects.filter(**filter_kwargs).first()
        
        if not monitoring:
            return JsonResponse({
                'status': 'error',
                'message': 'لا يوجد سجل مراقبة لهذه الحصة'
            })
        
        lesson_info = monitoring.get_lesson_info()
        
        return JsonResponse({
            'status': 'success',
            'monitoring_id': monitoring.id,
            'lesson_info': lesson_info,
            'monitoring_status': monitoring.status,
            'quality_score': monitoring.quality_score,
            'attendance_reliability': monitoring.attendance_reliability,
            'heartbeat_count': monitoring.total_heartbeats_received,
            'warnings_count': monitoring.warnings_issued,
            'fraud_risk_level': monitoring.get_fraud_risk_level(),
            'external_session_url': monitoring.external_session_url,
            'heartbeat_interval': UniversalLessonManager.HEARTBEAT_SETTINGS['interval_seconds']
        })
        
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})


@login_required
@require_http_methods(["GET"])
def admin_monitoring_dashboard(request):
    """لوحة مراقبة المدير للحصص النشطة"""
    if not request.user.is_admin():
        return JsonResponse({'status': 'error', 'message': 'غير مصرح'})
    
    try:
        # الحصص النشطة حالياً
        active_monitoring = UniversalLessonMonitoring.objects.filter(
            status__in=['confirmed', 'active', 'warning', 'critical']
        ).select_related('user', 'live_lesson', 'scheduled_lesson')
        
        monitoring_data = []
        for monitoring in active_monitoring:
            lesson_info = monitoring.get_lesson_info()
            
            monitoring_data.append({
                'monitoring_id': monitoring.id,
                'user_name': monitoring.user.get_full_name(),
                'user_role': monitoring.user_role,
                'lesson_title': lesson_info['title'],
                'lesson_type': monitoring.lesson_type,
                'status': monitoring.status,
                'quality_score': monitoring.quality_score,
                'attendance_reliability': monitoring.attendance_reliability,
                'fraud_risk_level': monitoring.get_fraud_risk_level(),
                'warnings_count': monitoring.warnings_issued,
                'heartbeat_count': monitoring.total_heartbeats_received,
                'last_heartbeat': monitoring.last_heartbeat.isoformat() if monitoring.last_heartbeat else None,
                'suspicious_activities_count': len(monitoring.suspicious_activities)
            })
        
        # إحصائيات عامة
        total_active = active_monitoring.count()
        warning_count = active_monitoring.filter(status__in=['warning', 'critical']).count()
        fraud_suspected = active_monitoring.filter(status='suspicious').count()
        fraud_detected = active_monitoring.filter(status='fraud_detected').count()
        
        return JsonResponse({
            'status': 'success',
            'monitoring_data': monitoring_data,
            'statistics': {
                'total_active': total_active,
                'warning_count': warning_count,
                'fraud_suspected': fraud_suspected,
                'fraud_detected': fraud_detected
            }
        })
        
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})


@login_required
@csrf_exempt
@require_http_methods(["POST"])
def admin_monitoring_action(request):
    """إجراءات المدير على المراقبة"""
    if not request.user.is_admin():
        return JsonResponse({'status': 'error', 'message': 'غير مصرح'})
    
    try:
        data = json.loads(request.body)
        action = data.get('action')
        monitoring_id = data.get('monitoring_id')
        
        if not monitoring_id:
            return JsonResponse({'status': 'error', 'message': 'معرف المراقبة مطلوب'})
        
        monitoring = UniversalLessonMonitoring.objects.get(id=monitoring_id)
        
        if action == 'reset_warnings':
            monitoring.warnings_issued = 0
            monitoring.warning_history = []
            monitoring.quality_score = monitoring.calculate_quality_score()
            monitoring.save()
            return JsonResponse({'status': 'success', 'message': 'تم إعادة تعيين التحذيرات'})
            
        elif action == 'mark_fraud':
            monitoring.status = 'fraud_detected'
            monitoring.add_suspicious_activity(
                'admin_marked_fraud',
                'تم وضع علامة تلاعب بواسطة المدير',
                'high'
            )
            return JsonResponse({'status': 'success', 'message': 'تم وضع علامة تلاعب'})
            
        elif action == 'clear_fraud':
            monitoring.status = 'active'
            monitoring.suspicious_activities = []
            monitoring.quality_score = monitoring.calculate_quality_score()
            monitoring.save()
            return JsonResponse({'status': 'success', 'message': 'تم إزالة علامة التلاعب'})
            
        else:
            return JsonResponse({'status': 'error', 'message': 'إجراء غير مدعوم'})
            
    except UniversalLessonMonitoring.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': 'سجل المراقبة غير موجود'})
    except json.JSONDecodeError:
        return JsonResponse({'status': 'error', 'message': 'بيانات JSON غير صحيحة'})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})
