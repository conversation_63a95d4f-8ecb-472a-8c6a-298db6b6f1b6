from django.urls import path
from django.shortcuts import render
from . import views, api_views
from lessons import quality_api

urlpatterns = [
    # Dashboard URLs
    path('', views.dashboard, name='dashboard'),
    path('admin/', views.admin_dashboard, name='admin_dashboard'),
    path('teacher/', views.teacher_dashboard, name='teacher_dashboard'),
    path('student/', views.student_dashboard, name='student_dashboard'),

    # Additional URLs
    path('profile/', views.user_profile, name='profile'),
    path('register/', views.register, name='register'),

    # Admin specific URLs
    path('admin/lessons/', views.admin_lessons, name='admin_lessons'),
    path('admin/lessons/<int:lesson_id>/join/', views.admin_join_lesson, name='admin_join_lesson'),
    path('admin/lessons/<int:lesson_id>/details/', views.admin_lesson_details, name='admin_lesson_details'),
    path('admin/lessons/live/', views.admin_live_lessons, name='admin_live_lessons'),
    path('admin/lessons/live/create/', views.admin_create_live_lesson, name='admin_create_live_lesson'),
    path('admin/quality-monitoring/', views.admin_quality_monitoring, name='admin_quality_monitoring'),
    path('admin/monitoring-dashboard/', views.admin_monitoring_dashboard, name='admin_monitoring_dashboard'),
    path('live-lesson/<int:lesson_id>/', views.live_lesson_room, name='live_lesson_room'),

    # Live lesson pages for teacher and student
    path('teacher/live-lesson/<int:lesson_id>/', views.teacher_live_lesson, name='teacher_live_lesson'),
    path('student/live-lesson/<int:lesson_id>/', views.student_live_lesson, name='student_live_lesson'),

    # Scheduled lesson page for teacher
    path('teacher/scheduled-lesson/<int:lesson_id>/', views.teacher_scheduled_lesson, name='teacher_scheduled_lesson'),

    # API endpoints for admin lesson management
    path('api/admin/start-subscription-lesson/<int:lesson_id>/', views.admin_start_subscription_lesson, name='admin_start_subscription_lesson'),
    path('api/admin/start-live-lesson/<int:lesson_id>/', views.admin_start_live_lesson, name='admin_start_live_lesson'),

    # Teacher calendar API
    path('api/teacher/calendar/', views.teacher_calendar_api, name='teacher_calendar_api'),

    # API endpoints for live lessons
    path('api/live-lessons/<int:lesson_id>/start/', views.api_start_live_lesson, name='api_start_live_lesson'),
    path('api/live-lessons/<int:lesson_id>/end/', api_views.api_end_live_lesson, name='api_end_live_lesson'),
    path('api/live-lessons/<int:lesson_id>/status/', views.api_live_lesson_status, name='api_live_lesson_status'),
    path('api/admin/live-lessons-status/', views.api_admin_live_lessons_status, name='api_admin_live_lessons_status'),

    # API endpoints for quality monitoring
    path('api/quality/lesson/<int:lesson_id>/activity/', quality_api.api_log_lesson_activity, name='api_log_lesson_activity'),
    path('api/quality/lesson/<int:lesson_id>/joined/', quality_api.api_participant_joined, name='api_participant_joined'),
    path('api/quality/lesson/<int:lesson_id>/left/', quality_api.api_participant_left, name='api_participant_left'),
    path('api/quality/lesson/<int:lesson_id>/check-duration/', quality_api.api_check_lesson_duration, name='api_check_lesson_duration'),
    path('api/quality/monitoring/', quality_api.api_real_time_monitoring, name='api_real_time_monitoring'),
    path('api/quality/statistics/', quality_api.api_quality_statistics, name='api_quality_statistics'),
    path('api/quality/alerts/', quality_api.api_quality_alerts, name='api_quality_alerts'),
    path('api/quality/alerts/<int:alert_id>/resolve/', quality_api.api_resolve_alert, name='api_resolve_alert'),
    path('api/quality/alerts/<int:alert_id>/read/', quality_api.api_mark_alert_read, name='api_mark_alert_read'),
    path('api/quality/settings/', quality_api.api_quality_settings, name='api_quality_settings'),
    # تم نقل التقارير إلى تطبيق منفصل - reports app
    # path('admin/reports/', views.admin_reports, name='admin_reports'),
    path('admin/academy-settings/', views.admin_academy_settings, name='admin_academy_settings'),
    path('admin/database-backup/', views.admin_database_backup, name='admin_database_backup'),
    path('admin/database-restore/', views.admin_database_restore, name='admin_database_restore'),

    # New admin URLs

    path('admin/ratings/', views.admin_ratings_view, name='admin_ratings_view'),
    path('admin/ratings/teachers/', views.admin_teacher_ratings, name='admin_teacher_ratings'),
    path('admin/ratings/students/', views.admin_student_ratings, name='admin_student_ratings'),
    path('admin/lesson-report/<int:lesson_id>/', views.admin_lesson_report_view, name='admin_lesson_report_view'),

    # Cross-Dashboard Integration URLs
    path('admin/user/<int:user_id>/', views.admin_user_detail, name='admin_user_detail'),
    path('admin/enrollment/<int:enrollment_id>/', views.admin_enrollment_detail, name='admin_enrollment_detail'),
    path('admin/schedule-lesson/', views.admin_schedule_lesson, name='admin_schedule_lesson'),

    # User Verification URLs
    path('admin/user-verifications/', views.admin_user_verifications, name='admin_user_verifications'),
    path('verification-pending/', views.verification_pending, name='verification_pending'),
    path('verification-rejected/', views.verification_rejected, name='verification_rejected'),
    path('registration-success/<int:user_id>/', views.registration_success, name='registration_success'),
    path('banned/', views.user_banned, name='user_banned'),

    # Teacher specific URLs
    path('teacher/schedule/', views.teacher_schedule, name='teacher_schedule'),
    path('teacher/students/', views.teacher_students, name='teacher_students'),
    path('teacher/ratings/', views.teacher_ratings, name='teacher_ratings'),

    # Student specific URLs
    path('student/subscriptions/', views.student_subscriptions, name='student_subscriptions'),
    path('student/subscribe/<int:plan_id>/', views.subscribe_to_plan, name='subscribe_to_plan'),
    path('student/payment/paypal/<int:payment_id>/', views.paypal_payment, name='paypal_payment'),
    path('student/payment/stripe/<int:payment_id>/', views.stripe_payment, name='stripe_payment'),
    path('student/payment/bank-transfer/<int:payment_id>/', views.bank_transfer_payment, name='bank_transfer_payment'),
    path('student/payment/success/<int:payment_id>/', views.payment_success, name='payment_success'),
    path('student/lessons/', views.student_lessons, name='student_lessons'),
    path('student/lessons/calendar-data/', views.student_lessons_calendar_data, name='student_lessons_calendar_data'),

    # Test calendar API (for debugging)
    path('test-calendar-api/', lambda request: render(request, 'test_calendar_api.html'), name='test_calendar_api'),
    path('create-test-lessons/', views.create_test_lessons, name='create_test_lessons'),
    path('delete-test-lessons/', views.delete_test_lessons, name='delete_test_lessons'),
    path('create-test-completed-lessons/', views.create_test_completed_lessons, name='create_test_completed_lessons'),
    path('submit-lesson-rating/', views.submit_lesson_rating, name='submit_lesson_rating'),
    path('submit-unified-rating/', views.submit_unified_rating, name='submit_unified_rating'),
    path('student/progress/', views.student_progress, name='student_progress'),
    path('student/invoice/<int:invoice_id>/', views.student_invoice_view, name='student_invoice_view'),
    path('student/invoice/<int:invoice_id>/print/', views.student_invoice_print, name='student_invoice_print'),
    path('student/invoice/<int:invoice_id>/pdf/', views.student_invoice_pdf, name='student_invoice_pdf'),

    # Admin Subscriptions Management URLs
    path('admin/subscriptions/', views.admin_subscriptions, name='admin_subscriptions'),
    path('admin/subscriptions/plans/', views.admin_plans_list, name='admin_plans_list'),
    path('admin/subscriptions/plans/create/', views.admin_plan_create, name='admin_plan_create'),
    path('admin/subscriptions/plans/<int:plan_id>/', views.admin_plan_detail, name='admin_plan_detail'),
    path('admin/subscriptions/plans/<int:plan_id>/edit/', views.admin_plan_edit, name='admin_plan_edit'),
    path('admin/subscriptions/plans/<int:plan_id>/delete/', views.admin_plan_delete, name='admin_plan_delete'),
    path('admin/subscriptions/approve/<int:subscription_id>/', views.admin_approve_subscription, name='admin_approve_subscription'),
    path('admin/subscriptions/subscriptions/', views.admin_subscriptions_list, name='admin_subscriptions_list'),
    path('admin/subscriptions/subscriptions/<int:subscription_id>/detail/', views.admin_subscription_detail, name='admin_subscription_detail'),
    path('admin/subscriptions/subscriptions/<int:subscription_id>/cancel/', views.admin_cancel_subscription, name='admin_cancel_subscription'),
    path('admin/subscriptions/payments/', views.admin_payments_list, name='admin_payments_list'),
    path('admin/subscriptions/verify-transfer/<int:transfer_id>/', views.admin_verify_transfer, name='admin_verify_transfer'),
    path('admin/subscriptions/lesson-schedules/', views.admin_manage_lesson_schedules, name='admin_manage_lesson_schedules'),
    path('admin/subscriptions/<int:subscription_id>/manual-schedule/', views.admin_manual_schedule_lessons, name='admin_manual_schedule_lessons'),
    path('admin/subscriptions/<int:subscription_id>/schedule-data/', views.admin_get_subscription_schedule_data, name='admin_get_subscription_schedule_data'),
    path('admin/subscriptions/<int:subscription_id>/save-schedule/', views.admin_save_scheduled_lessons, name='admin_save_scheduled_lessons'),

    # URLs للحصص المجدولة في القائمة الجانبية
    path('subscriptions/start-lesson/<int:lesson_id>/', views.start_subscription_lesson, name='start_subscription_lesson'),
    path('subscriptions/join-lesson/<int:lesson_id>/', views.join_subscription_lesson, name='join_subscription_lesson'),

    # صفحة تجريبية للتحقق من الحصص المجدولة
    path('debug/scheduled-lessons/', views.debug_scheduled_lessons, name='debug_scheduled_lessons'),
    path('debug/create-test-lessons/', views.create_test_scheduled_lessons, name='create_test_scheduled_lessons'),

    # Admin Invoices Management URLs
    path('admin/invoices/', views.admin_invoices, name='admin_invoices'),
    path('admin/invoices/<int:invoice_id>/', views.admin_invoice_detail, name='admin_invoice_detail'),
    path('admin/invoices/<int:invoice_id>/print/', views.admin_invoice_print, name='admin_invoice_print'),
    path('admin/invoices/<int:invoice_id>/pdf/', views.admin_invoice_pdf, name='admin_invoice_pdf'),

    # Common URLs
    path('notifications/', views.notifications_redirect, name='notifications'),
    path('messages/', views.messages_view, name='messages'),

    # Static pages
    path('about/', views.about, name='about'),
    path('privacy/', views.privacy, name='privacy'),
    path('terms/', views.terms, name='terms'),

    # API endpoints
    path('api/live-lessons/', api_views.api_live_lessons_sidebar, name='api_get_live_lessons'),
    path('api/rate-live-lesson/<int:lesson_id>/', api_views.api_rate_live_lesson, name='api_rate_live_lesson'),
    path('api/admin/live-lessons-realtime/', api_views.api_admin_live_lessons_realtime, name='api_admin_live_lessons_realtime'),
    path('api/live-lessons/<int:lesson_id>/start/', api_views.api_start_live_lesson, name='api_start_live_lesson'),

    # إعدادات SMTP والإشعارات البريدية
    path('admin/smtp-settings/', views.smtp_settings, name='smtp_settings'),
    path('admin/test-smtp/', views.test_smtp_connection, name='test_smtp_connection'),
    path('admin/send-test-email/', views.send_test_email, name='send_test_email'),
    path('admin/email-notifications/', views.email_notifications_dashboard, name='email_notifications_dashboard'),
    path('email-tracking/<uuid:tracking_id>/', views.email_tracking_pixel, name='email_tracking_pixel'),



    # الإعدادات التقنية
    path('admin/technical-settings/', views.technical_settings, name='technical_settings'),

    # إعدادات بوابات الدفع
    path('admin/payment-gateway-settings/', views.payment_gateway_settings, name='payment_gateway_settings'),
    path('admin/save-payment-gateway-settings/', views.save_payment_gateway_settings, name='save_payment_gateway_settings'),
    path('admin/test-payment-gateway/<str:gateway>/', views.test_payment_gateway, name='test_payment_gateway'),
    path('admin/reset-payment-gateway-settings/', views.reset_payment_gateway_settings, name='reset_payment_gateway_settings'),

    # تحويل الحصص المجدولة
    path('admin/convert-scheduled-lessons/', views.admin_convert_scheduled_lessons, name='admin_convert_scheduled_lessons'),

    # صفحة الشكر بعد الدفع
    path('student/thank-you/<int:payment_id>/', views.thank_you_page, name='thank_you_page'),

    # APIs للدفع
    path('api/check-payment-status/', views.check_payment_status_api, name='check_payment_status_api'),
    path('api/payment-event/', views.payment_event_api, name='payment_event_api'),

    # اختبار إشعارات الاشتراكات
    path('admin/test-subscription-notifications/', views.test_subscription_notifications, name='test_subscription_notifications'),
]
