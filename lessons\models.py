from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid
import secrets
import string

User = get_user_model()


class Lesson(models.Model):
    """نموذج الحصة"""

    STATUS_CHOICES = (
        ('scheduled', _('مجدولة')),
        ('in_progress', _('جارية')),
        ('completed', _('مكتملة')),
        ('cancelled', _('ملغية')),
        ('missed', _('فائتة')),
    )

    # استبدال enrollment بحقول مباشرة
    teacher = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='lessons_as_teacher',
        limit_choices_to={'user_type': 'teacher'},
        verbose_name=_('المعلم')
    )

    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='lessons_as_student',
        limit_choices_to={'user_type': 'student'},
        verbose_name=_('الطالب')
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان الحصة')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('وصف الحصة')
    )

    scheduled_date = models.DateTimeField(
        verbose_name=_('موعد الحصة')
    )

    duration_minutes = models.PositiveIntegerField(
        choices=[(30, '30 دقيقة'), (45, '45 دقيقة'), (60, '60 دقيقة')],
        default=45,
        verbose_name=_('مدة الحصة (بالدقائق)')
    )

    status = models.CharField(
        max_length=15,
        choices=STATUS_CHOICES,
        default='scheduled',
        verbose_name=_('حالة الحصة')
    )

    jitsi_room_id = models.CharField(
        max_length=100,
        unique=True,
        blank=True,
        null=True,
        verbose_name=_('معرف غرفة Jitsi')
    )

    jitsi_password = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('كلمة مرور Jitsi')
    )

    actual_start_time = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('وقت البداية الفعلي')
    )

    actual_end_time = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('وقت النهاية الفعلي')
    )

    teacher_notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات المعلم')
    )

    admin_notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات الإدارة')
    )

    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='created_lessons',
        verbose_name=_('منشئ الحصة')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('حصة')
        verbose_name_plural = _('الحصص')
        ordering = ['scheduled_date']

    def __str__(self):
        return f"{self.title} - {self.scheduled_date.strftime('%Y-%m-%d %H:%M')}"

    def save(self, *args, **kwargs):
        """إنشاء معرف غرفة Jitsi تلقائياً وإرسال إشعارات"""
        is_new = self.pk is None

        if not self.jitsi_room_id:
            self.jitsi_room_id = f"qurania_{uuid.uuid4().hex[:8]}"

        super().save(*args, **kwargs)

        # إرسال إشعار عند إنشاء حصة جديدة
        if is_new:
            try:
                from notifications.utils import NotificationService
                NotificationService.notify_lesson_created(self)
            except Exception as e:
                # تجاهل أخطاء الإشعارات لتجنب فشل إنشاء الحصة
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"فشل في إرسال إشعار إنشاء الحصة: {e}")

    def get_jitsi_url(self):
        """إنشاء رابط غرفة Jitsi مع دعم JaaS"""
        from django.conf import settings

        # استخدام JaaS (Jitsi as a Service) للإنتاج
        if hasattr(settings, 'JITSI_JAAS_DOMAIN') and settings.JITSI_JAAS_DOMAIN:
            domain = settings.JITSI_JAAS_DOMAIN
        else:
            # fallback للخادم المحلي أو العام
            domain = getattr(settings, 'JITSI_DOMAIN', 'meet.jit.si')

        return f"https://{domain}/{self.jitsi_room_id}"

    def get_teacher_jitsi_url(self):
        """رابط Jitsi للمعلم مع صلاحيات المشرف"""
        from .jitsi_service import JitsiService

        # استخدام JaaS مع JWT token إذا كان متاحاً
        if JitsiService.is_jaas_enabled():
            return JitsiService.get_jitsi_url_with_token(
                room_id=self.jitsi_room_id,
                user=self.teacher,
                is_moderator=True
            )
        else:
            # fallback للطريقة القديمة
            base_url = self.get_jitsi_url()
            return f"{base_url}#config.startWithVideoMuted=false&config.startWithAudioMuted=false&userInfo.displayName={self.teacher.get_full_name()}&userInfo.role=moderator"

    def get_student_jitsi_url(self):
        """رابط Jitsi للطالب مع صلاحيات محدودة"""
        from .jitsi_service import JitsiService

        # استخدام JaaS مع JWT token إذا كان متاحاً
        if JitsiService.is_jaas_enabled():
            return JitsiService.get_jitsi_url_with_token(
                room_id=self.jitsi_room_id,
                user=self.student,
                is_moderator=False
            )
        else:
            # fallback للطريقة القديمة
            base_url = self.get_jitsi_url()
            return f"{base_url}#config.startWithVideoMuted=true&config.startWithAudioMuted=true&userInfo.displayName={self.student.get_full_name()}&userInfo.role=participant"

    def is_upcoming(self):
        """التحقق من كون الحصة قادمة"""
        return self.scheduled_date > timezone.now() and self.status == 'scheduled'

    def is_today(self):
        """التحقق من كون الحصة اليوم"""
        return self.scheduled_date.date() == timezone.now().date()

    def can_join(self):
        """التحقق من إمكانية الدخول للحصة"""
        now = timezone.now()
        # يمكن الدخول قبل 15 دقيقة من الموعد
        start_time = self.scheduled_date - timezone.timedelta(minutes=15)
        end_time = self.scheduled_date + timezone.timedelta(minutes=self.duration_minutes + 30)
        return start_time <= now <= end_time and self.status in ['scheduled', 'in_progress']


class LessonAttendance(models.Model):
    """نموذج حضور الحصة"""

    lesson = models.ForeignKey(
        Lesson,
        on_delete=models.CASCADE,
        related_name='attendance_records',
        verbose_name=_('الحصة')
    )

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='lesson_attendance',
        verbose_name=_('المستخدم')
    )

    joined_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('وقت الدخول')
    )

    left_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('وقت الخروج')
    )

    duration_minutes = models.PositiveIntegerField(
        default=0,
        verbose_name=_('مدة الحضور (بالدقائق)')
    )

    class Meta:
        verbose_name = _('حضور الحصة')
        verbose_name_plural = _('حضور الحصص')
        unique_together = ['lesson', 'user']

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.lesson.title}"

    def calculate_duration(self):
        """حساب مدة الحضور"""
        if self.left_at:
            duration = self.left_at - self.joined_at
            self.duration_minutes = int(duration.total_seconds() / 60)
            self.save()


class LiveLessonAttendance(models.Model):
    """نموذج حضور الحصص المباشرة"""

    live_lesson = models.ForeignKey(
        'LiveLesson',
        on_delete=models.CASCADE,
        related_name='attendance_records',
        verbose_name=_('الحصة المباشرة')
    )

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='live_lesson_attendance',
        verbose_name=_('المستخدم')
    )

    joined_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('وقت الدخول')
    )

    left_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('وقت الخروج')
    )

    duration_minutes = models.PositiveIntegerField(
        default=0,
        verbose_name=_('مدة الحضور (بالدقائق)')
    )

    class Meta:
        verbose_name = _('حضور الحصة المباشرة')
        verbose_name_plural = _('حضور الحصص المباشرة')
        unique_together = ['live_lesson', 'user']

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.live_lesson.title}"

    def calculate_duration(self):
        """حساب مدة الحضور"""
        if self.left_at:
            duration = self.left_at - self.joined_at
            self.duration_minutes = int(duration.total_seconds() / 60)
            self.save()


class LessonContent(models.Model):
    """نموذج محتوى الحصة"""

    CONTENT_TYPES = (
        ('memorization', _('حفظ')),
        ('review', _('مراجعة')),
        ('recitation', _('تلاوة')),
        ('correction', _('تصحيح')),
    )

    lesson = models.ForeignKey(
        Lesson,
        on_delete=models.CASCADE,
        related_name='content_records',
        verbose_name=_('الحصة')
    )

    content_type = models.CharField(
        max_length=15,
        choices=CONTENT_TYPES,
        verbose_name=_('نوع المحتوى')
    )

    surah_name = models.CharField(
        max_length=100,
        verbose_name=_('اسم السورة')
    )

    from_verse = models.PositiveIntegerField(
        verbose_name=_('من الآية')
    )

    to_verse = models.PositiveIntegerField(
        verbose_name=_('إلى الآية')
    )

    quality_score = models.PositiveIntegerField(
        choices=[(i, f"{i}/10") for i in range(1, 11)],
        blank=True,
        null=True,
        verbose_name=_('تقييم الجودة')
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات')
    )

    recorded_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='recorded_lesson_content',
        verbose_name=_('مسجل بواسطة')
    )

    recorded_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ التسجيل')
    )

    class Meta:
        verbose_name = _('محتوى الحصة')
        verbose_name_plural = _('محتوى الحصص')
        ordering = ['-recorded_at']

    def __str__(self):
        return f"{self.lesson.title} - {self.surah_name} ({self.from_verse}-{self.to_verse})"


class LessonRating(models.Model):
    """نموذج تقييم الحصة"""

    lesson = models.OneToOneField(
        Lesson,
        on_delete=models.CASCADE,
        related_name='rating',
        verbose_name=_('الحصة')
    )

    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='lesson_ratings',
        limit_choices_to={'user_type': 'student'},
        verbose_name=_('الطالب')
    )

    teacher_rating = models.PositiveIntegerField(
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('تقييم المعلم')
    )

    lesson_quality = models.PositiveIntegerField(
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('جودة الحصة')
    )

    technical_quality = models.PositiveIntegerField(
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('الجودة التقنية')
    )

    overall_satisfaction = models.PositiveIntegerField(
        choices=[(i, f"{i} نجوم") for i in range(1, 6)],
        verbose_name=_('الرضا العام')
    )

    comment = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('تعليق')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ التقييم')
    )

    class Meta:
        verbose_name = _('تقييم الحصة')
        verbose_name_plural = _('تقييمات الحصص')

    def __str__(self):
        return f"تقييم {self.lesson.title} - {self.student.get_full_name()}"

    def get_average_rating(self):
        """حساب متوسط التقييم"""
        total = self.teacher_rating + self.lesson_quality + self.technical_quality + self.overall_satisfaction
        return round(total / 4, 1)

    def save(self, *args, **kwargs):
        """حفظ التقييم مع إرسال إشعارات"""
        is_new = self.pk is None
        super().save(*args, **kwargs)

        if is_new:
            # إرسال إشعار للمعلم
            from notifications.utils import NotificationService
            NotificationService.notify_lesson_rating(self)

            # تحديث تقييم المعلم الإجمالي - تم إزالة التبعية على courses
            # TODO: إضافة نظام تقييم المعلمين في lessons app إذا لزم الأمر
            pass


class LiveLesson(models.Model):
    """نموذج الحصص المباشرة مع Jitsi Meet"""

    STATUS_CHOICES = (
        ('scheduled', _('مجدولة')),
        ('live', _('مباشرة')),
        ('ended', _('منتهية')),
        ('cancelled', _('ملغية')),
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان الحصة')
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('وصف الحصة')
    )

    teacher = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='live_lessons_as_teacher',
        limit_choices_to={'user_type': 'teacher'},
        verbose_name=_('المعلم')
    )

    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='live_lessons_as_student',
        limit_choices_to={'user_type': 'student'},
        verbose_name=_('الطالب')
    )

    jitsi_room_id = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_('معرف غرفة Jitsi')
    )

    jitsi_room_password = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('كلمة مرور الغرفة')
    )

    # Google Meet integration
    google_meet_url = models.URLField(
        blank=True,
        null=True,
        verbose_name=_('رابط Google Meet')
    )

    google_meet_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('معرف Google Meet')
    )

    meeting_platform = models.CharField(
        max_length=20,
        choices=[
            ('jitsi', 'Jitsi Meet'),
            ('google_meet', 'Google Meet'),
        ],
        default='jitsi',
        verbose_name=_('منصة الاجتماع')
    )

    scheduled_date = models.DateTimeField(
        default=timezone.now,
        verbose_name=_('موعد الحصة')
    )

    duration_minutes = models.PositiveIntegerField(
        choices=[(30, '30 دقيقة'), (45, '45 دقيقة'), (60, '60 دقيقة'), (90, '90 دقيقة')],
        default=45,
        verbose_name=_('مدة الحصة (بالدقائق)')
    )

    status = models.CharField(
        max_length=15,
        choices=STATUS_CHOICES,
        default='scheduled',
        verbose_name=_('حالة الحصة')
    )

    started_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('وقت البداية الفعلي')
    )

    ended_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('وقت النهاية الفعلي')
    )

    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='created_live_lessons',
        verbose_name=_('منشئ الحصة')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('حصة مباشرة')
        verbose_name_plural = _('الحصص المباشرة')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.teacher.get_full_name()} مع {self.student.get_full_name()}"

    def save(self, *args, **kwargs):
        """إنشاء معرف غرفة Jitsi فريد وكلمة مرور"""
        is_new = self.pk is None

        if not self.jitsi_room_id:
            # إنشاء معرف فريد للغرفة
            timestamp = timezone.now().strftime('%Y%m%d%H%M')
            random_part = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(6))
            self.jitsi_room_id = f"qurania_live_{timestamp}_{random_part}"

        if not self.jitsi_room_password:
            # إنشاء كلمة مرور عشوائية
            self.jitsi_room_password = ''.join(secrets.choice(string.digits) for _ in range(6))

        super().save(*args, **kwargs)

        # إرسال إشعارات عند إنشاء حصة جديدة
        if is_new:
            from notifications.utils import NotificationService
            NotificationService.notify_live_lesson_created(self)

    def get_jitsi_url(self):
        """إنشاء رابط غرفة Jitsi مع دعم JaaS"""
        from django.conf import settings

        # استخدام JaaS (Jitsi as a Service) للإنتاج
        if hasattr(settings, 'JITSI_JAAS_DOMAIN') and settings.JITSI_JAAS_DOMAIN:
            domain = settings.JITSI_JAAS_DOMAIN
        else:
            # fallback للخادم المحلي أو العام
            domain = getattr(settings, 'JITSI_DOMAIN', 'meet.jit.si')

        return f"https://{domain}/{self.jitsi_room_id}"

    def get_teacher_jitsi_url(self):
        """رابط Jitsi للمعلم مع صلاحيات المشرف"""
        from .jitsi_service import JitsiService

        # استخدام JaaS مع JWT token إذا كان متاحاً
        if JitsiService.is_jaas_enabled():
            return JitsiService.get_jitsi_url_with_token(
                room_id=self.jitsi_room_id,
                user=self.teacher,
                is_moderator=True
            )
        else:
            # fallback للطريقة القديمة
            base_url = self.get_jitsi_url()
            return f"{base_url}#config.startWithVideoMuted=false&config.startWithAudioMuted=false&userInfo.displayName={self.teacher.get_full_name()}&userInfo.role=moderator"

    def get_student_jitsi_url(self):
        """رابط Jitsi للطالب مع صلاحيات محدودة"""
        from .jitsi_service import JitsiService

        # استخدام JaaS مع JWT token إذا كان متاحاً
        if JitsiService.is_jaas_enabled():
            return JitsiService.get_jitsi_url_with_token(
                room_id=self.jitsi_room_id,
                user=self.student,
                is_moderator=False
            )
        else:
            # fallback للطريقة القديمة
            base_url = self.get_jitsi_url()
            return f"{base_url}#config.startWithVideoMuted=true&config.startWithAudioMuted=true&userInfo.displayName={self.student.get_full_name()}&userInfo.role=participant"

    def generate_google_meet_url(self):
        """إنشاء رابط Google Meet"""
        import uuid
        # إنشاء معرف فريد للاجتماع
        meeting_id = str(uuid.uuid4())[:12].replace('-', '')
        self.google_meet_id = meeting_id
        return f"https://meet.google.com/{meeting_id}"

    def get_meeting_url(self):
        """الحصول على رابط الاجتماع - Jitsi فقط"""
        return self.get_jitsi_url()

    def get_teacher_meeting_url(self):
        """رابط الاجتماع للمعلم - Jitsi فقط"""
        return self.get_teacher_jitsi_url()

    def get_student_meeting_url(self):
        """رابط الاجتماع للطالب - Jitsi فقط"""
        return self.get_student_jitsi_url()

    def start_lesson(self):
        """بدء الحصة"""
        self.status = 'live'
        self.started_at = timezone.now()
        self.save()

    def record_user_join(self, user):
        """تسجيل دخول مستخدم للحصة المباشرة"""
        try:
            # التحقق من عدم وجود سجل حضور نشط
            existing_attendance = LiveLessonAttendance.objects.filter(
                live_lesson=self,
                user=user,
                left_at__isnull=True
            ).first()

            if not existing_attendance:
                # إنشاء سجل حضور جديد
                LiveLessonAttendance.objects.create(
                    live_lesson=self,
                    user=user
                )
                print(f"تم تسجيل دخول {user.get_full_name()} للحصة المباشرة {self.id}")
            else:
                print(f"المستخدم {user.get_full_name()} موجود بالفعل في الحصة المباشرة {self.id}")

        except Exception as e:
            print(f"خطأ في تسجيل دخول المستخدم للحصة المباشرة: {str(e)}")

    def end_lesson(self):
        """إنهاء الحصة"""
        self.status = 'ended'
        self.ended_at = timezone.now()
        self.save()

        # تحديث الحصة المجدولة المرتبطة إذا وجدت
        self._complete_related_scheduled_lesson()

    def cancel_lesson(self):
        """إلغاء الحصة"""
        self.status = 'cancelled'
        self.save()

    def is_live(self):
        """التحقق من كون الحصة مباشرة"""
        return self.status == 'live'

    def is_scheduled(self):
        """التحقق من كون الحصة مجدولة"""
        return self.status == 'scheduled'

    def get_duration_display(self):
        """عرض مدة الحصة"""
        if self.started_at and self.ended_at:
            duration = self.ended_at - self.started_at
            minutes = int(duration.total_seconds() / 60)
            return f"{minutes} دقيقة"
        return f"{self.duration_minutes} دقيقة (مخطط)"

    def should_auto_end(self):
        """التحقق من ضرورة إنهاء الحصة تلقائياً"""
        if self.status != 'live' or not self.started_at:
            return False

        # حساب الوقت المنقضي منذ بداية الحصة
        now = timezone.now()
        elapsed_time = now - self.started_at
        elapsed_minutes = elapsed_time.total_seconds() / 60

        # إنهاء الحصة إذا تجاوزت المدة المحددة + 15 دقيقة إضافية
        return elapsed_minutes > (self.duration_minutes + 15)

    def auto_end_if_needed(self):
        """إنهاء الحصة تلقائياً إذا لزم الأمر"""
        if self.should_auto_end():
            self.end_lesson()
            return True
        return False

    def _complete_related_scheduled_lesson(self):
        """إكمال الحصة المجدولة المرتبطة بهذه الحصة المباشرة"""
        try:
            # تجنب الاستيراد الدائري
            from django.apps import apps
            ScheduledLesson = apps.get_model('subscriptions', 'ScheduledLesson')

            # البحث عن الحصة المجدولة المرتبطة
            scheduled_lesson = ScheduledLesson.objects.filter(
                live_lesson_id=self.id,
                status='converted_to_live'
            ).first()

            if scheduled_lesson:
                # تحديد الحصة المجدولة كمكتملة
                scheduled_lesson.mark_completed()
                print(f"تم إكمال الحصة المجدولة {scheduled_lesson.id} المرتبطة بالحصة المباشرة {self.id}")

        except Exception as e:
            print(f"خطأ في إكمال الحصة المجدولة المرتبطة: {str(e)}")
            # لا نريد أن يفشل إنهاء الحصة بسبب هذا الخطأ
            pass

    def get_related_scheduled_lesson(self):
        """الحصول على الحصة المجدولة المرتبطة"""
        try:
            # تجنب الاستيراد الدائري
            from django.apps import apps
            ScheduledLesson = apps.get_model('subscriptions', 'ScheduledLesson')
            return ScheduledLesson.objects.filter(
                live_lesson_id=self.id,
                status='converted_to_live'
            ).first()
        except Exception:
            return None


class LiveLessonRating(models.Model):
    """تقييم الحصة المباشرة"""

    live_lesson = models.OneToOneField(
        LiveLesson,
        on_delete=models.CASCADE,
        related_name='rating',
        verbose_name=_('الحصة المباشرة')
    )

    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='live_lesson_ratings_given',
        limit_choices_to={'user_type': 'student'},
        verbose_name=_('الطالب')
    )

    teacher = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='live_lesson_ratings_received',
        limit_choices_to={'user_type': 'teacher'},
        verbose_name=_('المعلم')
    )

    # التقييم العام
    overall_rating = models.IntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        verbose_name=_('التقييم العام'),
        help_text="التقييم العام للحصة من 1 إلى 5 نجوم"
    )

    # جودة الحصة
    lesson_quality = models.IntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        verbose_name=_('جودة الحصة'),
        help_text="تقييم جودة المحتوى والشرح"
    )

    # تفاعل المعلم
    teacher_interaction = models.IntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        verbose_name=_('تفاعل المعلم'),
        help_text="تقييم تفاعل المعلم وطريقة التدريس"
    )

    # الجودة التقنية
    technical_quality = models.IntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        verbose_name=_('الجودة التقنية'),
        help_text="تقييم جودة الصوت والصورة والاتصال"
    )

    # التعليق
    comment = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('التعليق'),
        help_text="تعليق اختياري حول الحصة"
    )

    # تاريخ التقييم
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ التقييم')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('تقييم الحصة المباشرة')
        verbose_name_plural = _('تقييمات الحصص المباشرة')
        unique_together = ['live_lesson', 'student']

    def __str__(self):
        return f"تقييم {self.live_lesson.title} - {self.student.get_full_name()}"

    @property
    def average_rating(self):
        """حساب متوسط التقييم"""
        total = self.overall_rating + self.lesson_quality + self.teacher_interaction + self.technical_quality
        return round(total / 4, 1)

    def save(self, *args, **kwargs):
        """حفظ التقييم مع إرسال إشعارات وتحديث تقييم المعلم"""
        is_new = self.pk is None
        super().save(*args, **kwargs)

        if is_new:
            # إرسال إشعار للمعلم
            from notifications.utils import NotificationService
            NotificationService.notify_live_lesson_rating(self)

            # تحديث تقييم المعلم الإجمالي - تم إزالة التبعية على courses
            # TODO: إضافة نظام تقييم المعلمين في lessons app إذا لزم الأمر
            pass


class UnifiedLessonRating(models.Model):
    """نظام تقييم موحد للحصص المجدولة والمباشرة"""

    # ربط مع نوع الحصة (واحد فقط يكون مملوء)
    scheduled_lesson = models.ForeignKey(
        'subscriptions.ScheduledLesson',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='unified_ratings',
        verbose_name=_('الحصة المجدولة')
    )

    live_lesson = models.ForeignKey(
        'lessons.LiveLesson',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='unified_ratings',
        verbose_name=_('الحصة المباشرة')
    )

    # معلومات التقييم
    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='unified_ratings_given',
        limit_choices_to={'user_type': 'student'},
        verbose_name=_('الطالب')
    )

    teacher = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='unified_ratings_received',
        limit_choices_to={'user_type': 'teacher'},
        verbose_name=_('المعلم')
    )

    # التقييمات الموحدة (4 معايير)
    overall_rating = models.IntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        verbose_name=_('التقييم العام'),
        help_text="التقييم العام للحصة من 1 إلى 5 نجوم"
    )

    lesson_quality = models.IntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        verbose_name=_('جودة الحصة'),
        help_text="تقييم جودة المحتوى والشرح"
    )

    teacher_interaction = models.IntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        verbose_name=_('تفاعل المعلم'),
        help_text="تقييم تفاعل المعلم وطريقة التدريس"
    )

    technical_quality = models.IntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        verbose_name=_('الجودة التقنية'),
        help_text="تقييم جودة الصوت والصورة والاتصال"
    )

    # التعليق
    comment = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('التعليق'),
        help_text="تعليق اختياري حول الحصة"
    )

    # نوع الحصة
    lesson_type = models.CharField(
        max_length=20,
        choices=[
            ('scheduled', 'حصة مجدولة'),
            ('live', 'حصة مباشرة')
        ],
        verbose_name=_('نوع الحصة')
    )

    # تواريخ
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ التقييم')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('تقييم الحصة الموحد')
        verbose_name_plural = _('تقييمات الحصص الموحدة')
        # ضمان عدم تكرار التقييم لنفس الحصة من نفس الطالب
        constraints = [
            models.CheckConstraint(
                check=models.Q(scheduled_lesson__isnull=False) | models.Q(live_lesson__isnull=False),
                name='either_scheduled_or_live_lesson'
            ),
            models.CheckConstraint(
                check=~(models.Q(scheduled_lesson__isnull=False) & models.Q(live_lesson__isnull=False)),
                name='not_both_lesson_types'
            )
        ]
        unique_together = [
            ['scheduled_lesson', 'student'],  # طالب واحد لكل حصة مجدولة
            ['live_lesson', 'student']        # طالب واحد لكل حصة مباشرة
        ]

    def __str__(self):
        if self.lesson_type == 'scheduled':
            lesson_name = f"حصة مجدولة رقم {self.scheduled_lesson.lesson_number}"
        else:
            lesson_name = f"حصة مباشرة: {self.live_lesson.title}"

        return f"تقييم {lesson_name} - {self.student.get_full_name()}"

    @property
    def average_rating(self):
        """حساب متوسط التقييم"""
        total = self.overall_rating + self.lesson_quality + self.teacher_interaction + self.technical_quality
        return round(total / 4, 1)

    @property
    def lesson_title(self):
        """الحصول على عنوان الحصة"""
        if self.lesson_type == 'scheduled':
            return f"حصة رقم {self.scheduled_lesson.lesson_number}"
        else:
            return self.live_lesson.title

    @property
    def lesson_date(self):
        """الحصول على تاريخ الحصة"""
        if self.lesson_type == 'scheduled':
            return self.scheduled_lesson.scheduled_date
        else:
            return self.live_lesson.scheduled_date

    def clean(self):
        """التحقق من صحة البيانات"""
        from django.core.exceptions import ValidationError

        # التأكد من وجود نوع حصة واحد فقط
        if not self.scheduled_lesson and not self.live_lesson:
            raise ValidationError('يجب تحديد نوع الحصة (مجدولة أو مباشرة)')

        if self.scheduled_lesson and self.live_lesson:
            raise ValidationError('لا يمكن ربط التقييم بنوعين من الحصص في نفس الوقت')

        # التأكد من تطابق نوع الحصة
        if self.scheduled_lesson and self.lesson_type != 'scheduled':
            raise ValidationError('نوع الحصة يجب أن يكون "مجدولة" للحصص المجدولة')

        if self.live_lesson and self.lesson_type != 'live':
            raise ValidationError('نوع الحصة يجب أن يكون "مباشرة" للحصص المباشرة')

    def save(self, *args, **kwargs):
        """حفظ التقييم مع إرسال إشعارات وتحديث الإحصائيات"""
        # تحديد نوع الحصة تلقائياً
        if self.scheduled_lesson:
            self.lesson_type = 'scheduled'
            self.teacher = self.scheduled_lesson.teacher
        elif self.live_lesson:
            self.lesson_type = 'live'
            self.teacher = self.live_lesson.teacher

        # التحقق من صحة البيانات
        self.clean()

        is_new = self.pk is None
        super().save(*args, **kwargs)

        if is_new:
            # إرسال إشعار للمعلم
            try:
                from notifications.utils import NotificationService
                NotificationService.notify_unified_lesson_rating(self)
            except ImportError:
                pass  # في حالة عدم وجود نظام الإشعارات

            # إكمال الحصة المجدولة عند كتابة التقييم
            self._complete_lesson_on_rating()

            # تحديث إحصائيات المعلم
            self._update_teacher_stats()

    def _complete_lesson_on_rating(self):
        """إكمال الحصة عند كتابة التقييم"""
        try:
            if self.scheduled_lesson and self.scheduled_lesson.status != 'completed':
                # إكمال الحصة المجدولة مباشرة
                self.scheduled_lesson.mark_completed()
                print(f"تم إكمال الحصة المجدولة {self.scheduled_lesson.id} عند كتابة التقييم")

            elif self.live_lesson:
                # إكمال الحصة المجدولة المرتبطة بالحصة المباشرة
                related_scheduled = self.live_lesson.get_related_scheduled_lesson()
                if related_scheduled and related_scheduled.status != 'completed':
                    related_scheduled.mark_completed()
                    print(f"تم إكمال الحصة المجدولة {related_scheduled.id} المرتبطة بالحصة المباشرة عند كتابة التقييم")

        except Exception as e:
            print(f"خطأ في إكمال الحصة عند كتابة التقييم: {str(e)}")

    def _update_teacher_stats(self):
        """تحديث إحصائيات المعلم بناءً على التقييمات الجديدة"""
        # يمكن إضافة منطق تحديث إحصائيات المعلم هنا
        # مثل حساب متوسط التقييمات، عدد التقييمات، إلخ
        pass


class TeacherLessonReport(models.Model):
    """تقرير المعلم عن الحصة والطالب بعد انتهاء الحصة"""

    lesson = models.OneToOneField(
        LiveLesson,
        on_delete=models.CASCADE,
        related_name='teacher_report',
        verbose_name=_('الحصة')
    )

    teacher = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='lesson_reports',
        verbose_name=_('المعلم')
    )

    student = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='lesson_reports_received',
        verbose_name=_('الطالب')
    )

    # تقييم أداء الطالب (من 1 إلى 5)
    student_performance = models.PositiveSmallIntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        verbose_name=_('أداء الطالب')
    )

    # مستوى التفاعل والمشاركة (من 1 إلى 5)
    student_participation = models.PositiveSmallIntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        verbose_name=_('مستوى التفاعل')
    )

    # مستوى الفهم والاستيعاب (من 1 إلى 5)
    student_understanding = models.PositiveSmallIntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        verbose_name=_('مستوى الفهم')
    )

    # نقاط القوة
    strengths = models.TextField(
        verbose_name=_('نقاط القوة'),
        help_text=_('اذكر نقاط القوة التي لاحظتها على الطالب')
    )

    # نقاط تحتاج تحسين
    areas_for_improvement = models.TextField(
        verbose_name=_('نقاط تحتاج تحسين'),
        help_text=_('اذكر النقاط التي يحتاج الطالب لتحسينها')
    )

    # ملخص الحصة
    lesson_summary = models.TextField(
        verbose_name=_('ملخص الحصة'),
        help_text=_('ملخص مختصر عما تم تدريسه في الحصة')
    )

    # الواجبات المطلوبة
    homework_assigned = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('الواجبات المطلوبة'),
        help_text=_('الواجبات أو المهام المطلوبة من الطالب')
    )

    # توصيات للحصص القادمة
    recommendations = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('توصيات للحصص القادمة'),
        help_text=_('توصيات لتحسين الحصص القادمة')
    )

    # تقييم عام للحصة
    overall_lesson_rating = models.PositiveSmallIntegerField(
        choices=[(i, f'{i} نجوم') for i in range(1, 6)],
        verbose_name=_('تقييم عام للحصة')
    )

    # ملاحظات إضافية
    additional_notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات إضافية')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ إنشاء التقرير')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ آخر تحديث')
    )

    class Meta:
        verbose_name = _('تقرير المعلم عن الحصة')
        verbose_name_plural = _('تقارير المعلمين عن الحصص')
        ordering = ['-created_at']

    def __str__(self):
        return f"تقرير {self.teacher.get_full_name()} عن حصة {self.lesson.title}"

    def get_average_student_rating(self):
        """حساب متوسط تقييم الطالب"""
        return (self.student_performance + self.student_participation + self.student_understanding) / 3


# ===== نظام المراقبة الموحد الجديد =====

class UniversalLessonMonitoring(models.Model):
    """نظام مراقبة موحد لجميع أنواع الحصص مع منع التلاعب"""

    # ربط مع أنواع الحصص المختلفة (واحد فقط يكون مملوء)
    live_lesson = models.ForeignKey(
        'lessons.LiveLesson',
        null=True, blank=True,
        on_delete=models.CASCADE,
        related_name='monitoring_records',
        verbose_name=_('الحصة المباشرة')
    )

    scheduled_lesson = models.ForeignKey(
        'subscriptions.ScheduledLesson',
        null=True, blank=True,
        on_delete=models.CASCADE,
        related_name='monitoring_records',
        verbose_name=_('الحصة المجدولة')
    )

    # معلومات المراقبة
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='lesson_monitoring_records',
        verbose_name=_('المستخدم')
    )

    user_role = models.CharField(
        max_length=10,
        choices=[('teacher', _('معلم')), ('student', _('طالب'))],
        verbose_name=_('دور المستخدم')
    )

    # نظام الحضور المحسن
    intended_join_time = models.DateTimeField(
        null=True, blank=True,
        verbose_name=_('وقت نية الدخول'),
        help_text=_('وقت الضغط على زر دخول الحصة')
    )

    confirmed_join_time = models.DateTimeField(
        null=True, blank=True,
        verbose_name=_('وقت تأكيد الحضور'),
        help_text=_('وقت تأكيد الحضور الفعلي في الحصة')
    )

    last_heartbeat = models.DateTimeField(
        null=True, blank=True,
        verbose_name=_('آخر إشارة حياة'),
        help_text=_('آخر وقت تم استلام heartbeat فيه')
    )

    left_at = models.DateTimeField(
        null=True, blank=True,
        verbose_name=_('وقت المغادرة'),
        help_text=_('وقت تسجيل الخروج من الحصة')
    )

    # مراقبة الجودة والتلاعب
    total_heartbeats_sent = models.PositiveIntegerField(
        default=0,
        verbose_name=_('إجمالي النبضات المرسلة'),
        help_text=_('العدد المتوقع للنبضات حسب مدة الحصة')
    )

    total_heartbeats_received = models.PositiveIntegerField(
        default=0,
        verbose_name=_('إجمالي النبضات المستلمة'),
        help_text=_('العدد الفعلي للنبضات المستلمة')
    )

    missed_heartbeats = models.PositiveIntegerField(
        default=0,
        verbose_name=_('النبضات المفقودة'),
        help_text=_('عدد النبضات التي لم تصل')
    )

    consecutive_missed = models.PositiveIntegerField(
        default=0,
        verbose_name=_('النبضات المفقودة المتتالية'),
        help_text=_('عدد النبضات المفقودة بشكل متتالي')
    )

    # نقاط الجودة الديناميكية
    quality_score = models.FloatField(
        default=100.0,
        verbose_name=_('نقاط الجودة'),
        help_text=_('نقاط الجودة من 0 إلى 100')
    )

    attendance_reliability = models.FloatField(
        default=100.0,
        verbose_name=_('موثوقية الحضور'),
        help_text=_('مؤشر موثوقية الحضور من 0 إلى 100')
    )

    # حالات المراقبة المتقدمة
    MONITORING_STATUS = [
        ('intended', _('نية الدخول')),
        ('confirmed', _('حضور مؤكد')),
        ('active', _('نشط')),
        ('warning', _('تحذير')),
        ('critical', _('حالة حرجة')),
        ('disconnected', _('منقطع')),
        ('suspicious', _('مشبوه')),
        ('completed', _('مكتمل')),
        ('fraud_detected', _('تم اكتشاف تلاعب')),
    ]

    status = models.CharField(
        max_length=20,
        choices=MONITORING_STATUS,
        default='intended',
        verbose_name=_('حالة المراقبة')
    )

    # سجل التحذيرات والأنشطة المشبوهة
    warnings_issued = models.PositiveIntegerField(
        default=0,
        verbose_name=_('عدد التحذيرات الصادرة')
    )

    warning_history = models.JSONField(
        default=list,
        verbose_name=_('سجل التحذيرات'),
        help_text=_('سجل تفصيلي بجميع التحذيرات الصادرة')
    )

    suspicious_activities = models.JSONField(
        default=list,
        verbose_name=_('الأنشطة المشبوهة'),
        help_text=_('سجل بالأنشطة المشبوهة المكتشفة')
    )

    # بيانات تقنية للمراقبة
    browser_info = models.JSONField(
        default=dict,
        verbose_name=_('معلومات المتصفح'),
        help_text=_('معلومات تقنية عن متصفح المستخدم')
    )

    page_visibility_changes = models.PositiveIntegerField(
        default=0,
        verbose_name=_('تغييرات رؤية الصفحة'),
        help_text=_('عدد مرات إخفاء/إظهار صفحة المراقبة')
    )

    # معلومات الجلسة الخارجية
    external_session_url = models.URLField(
        blank=True, null=True,
        verbose_name=_('رابط الجلسة الخارجية'),
        help_text=_('رابط Jitsi أو المنصة الخارجية')
    )

    external_session_token = models.CharField(
        max_length=500,
        blank=True, null=True,
        verbose_name=_('رمز الجلسة الخارجية'),
        help_text=_('JWT token أو رمز التحقق للجلسة الخارجية')
    )

    # تواريخ النظام
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ آخر تحديث')
    )

    class Meta:
        verbose_name = _('سجل المراقبة الموحد')
        verbose_name_plural = _('سجلات المراقبة الموحدة')
        ordering = ['-created_at']
        # ضمان عدم تكرار المراقبة لنفس المستخدم في نفس الحصة
        constraints = [
            models.CheckConstraint(
                check=models.Q(live_lesson__isnull=False) | models.Q(scheduled_lesson__isnull=False),
                name='monitoring_either_live_or_scheduled_lesson'
            ),
            models.CheckConstraint(
                check=~(models.Q(live_lesson__isnull=False) & models.Q(scheduled_lesson__isnull=False)),
                name='monitoring_not_both_lesson_types'
            )
        ]
        unique_together = [
            ['live_lesson', 'user'],      # مستخدم واحد لكل حصة مباشرة
            ['scheduled_lesson', 'user']  # مستخدم واحد لكل حصة مجدولة
        ]

    def __str__(self):
        lesson_info = self.get_lesson_info()
        return f"مراقبة {lesson_info['title']} - {self.user.get_full_name()} ({self.get_status_display()})"

    @property
    def lesson_type(self):
        """تحديد نوع الحصة"""
        if self.live_lesson:
            return 'live'
        elif self.scheduled_lesson:
            return 'scheduled'
        return None

    @property
    def lesson_object(self):
        """الحصول على كائن الحصة"""
        if self.live_lesson:
            return self.live_lesson
        elif self.scheduled_lesson:
            return self.scheduled_lesson
        return None

    def get_lesson_info(self):
        """الحصول على معلومات الحصة"""
        lesson = self.lesson_object
        if not lesson:
            return {'title': 'حصة غير محددة', 'teacher': None, 'student': None}

        if self.lesson_type == 'live':
            return {
                'title': lesson.title,
                'teacher': lesson.teacher,
                'student': lesson.student,
                'scheduled_date': lesson.scheduled_date,
                'duration_minutes': lesson.duration_minutes
            }
        elif self.lesson_type == 'scheduled':
            return {
                'title': f"حصة رقم {lesson.lesson_number}",
                'teacher': lesson.teacher,
                'student': lesson.student,
                'scheduled_date': lesson.scheduled_date,
                'duration_minutes': lesson.duration_minutes
            }

    def calculate_heartbeat_reliability(self):
        """حساب موثوقية النبضات"""
        if self.total_heartbeats_sent == 0:
            return 100.0

        reliability = (self.total_heartbeats_received / self.total_heartbeats_sent) * 100
        return round(min(reliability, 100.0), 2)

    def calculate_quality_score(self):
        """حساب نقاط الجودة الإجمالية"""
        score = 100.0

        # خصم نقاط للنبضات المفقودة
        heartbeat_penalty = (self.missed_heartbeats * 2)  # 2 نقطة لكل نبضة مفقودة
        score -= heartbeat_penalty

        # خصم نقاط لتغييرات رؤية الصفحة
        visibility_penalty = (self.page_visibility_changes * 5)  # 5 نقاط لكل تغيير
        score -= visibility_penalty

        # خصم نقاط للتحذيرات
        warning_penalty = (self.warnings_issued * 10)  # 10 نقاط لكل تحذير
        score -= warning_penalty

        # خصم نقاط للأنشطة المشبوهة
        suspicious_penalty = (len(self.suspicious_activities) * 20)  # 20 نقطة لكل نشاط مشبوه
        score -= suspicious_penalty

        return round(max(score, 0.0), 2)

    def add_suspicious_activity(self, activity_type, description, severity='medium'):
        """إضافة نشاط مشبوه"""
        from django.utils import timezone

        activity = {
            'timestamp': timezone.now().isoformat(),
            'type': activity_type,
            'description': description,
            'severity': severity,
            'consecutive_missed_at_time': self.consecutive_missed
        }

        self.suspicious_activities.append(activity)

        # تحديث نقاط الجودة
        self.quality_score = self.calculate_quality_score()
        self.attendance_reliability = self.calculate_heartbeat_reliability()

        # تحديث الحالة حسب الخطورة
        if severity == 'high' or len(self.suspicious_activities) >= 3:
            self.status = 'fraud_detected'
        elif severity == 'medium':
            self.status = 'suspicious'

        self.save()

    def add_warning(self, level, message, action_taken=None):
        """إضافة تحذير"""
        from django.utils import timezone

        warning = {
            'timestamp': timezone.now().isoformat(),
            'level': level,
            'message': message,
            'action_taken': action_taken,
            'consecutive_missed': self.consecutive_missed,
            'quality_score_at_time': self.quality_score
        }

        self.warning_history.append(warning)
        self.warnings_issued += 1

        # تحديث نقاط الجودة
        self.quality_score = self.calculate_quality_score()

        self.save()

    def is_fraud_suspected(self):
        """فحص ما إذا كان هناك شك في التلاعب"""
        # معايير اكتشاف التلاعب
        fraud_indicators = 0

        # نبضات مفقودة كثيرة
        if self.missed_heartbeats > 10:
            fraud_indicators += 1

        # تغييرات رؤية صفحة كثيرة
        if self.page_visibility_changes > 5:
            fraud_indicators += 1

        # نقاط جودة منخفضة
        if self.quality_score < 50:
            fraud_indicators += 1

        # أنشطة مشبوهة متعددة
        if len(self.suspicious_activities) >= 2:
            fraud_indicators += 1

        # تحذيرات كثيرة
        if self.warnings_issued >= 3:
            fraud_indicators += 1

        return fraud_indicators >= 2

    def get_fraud_risk_level(self):
        """تحديد مستوى خطر التلاعب"""
        if self.status == 'fraud_detected':
            return 'high'
        elif self.is_fraud_suspected():
            return 'medium'
        elif self.quality_score < 70 or self.warnings_issued > 0:
            return 'low'
        else:
            return 'none'


# ===== نماذج نظام مراقبة الجودة =====

class LessonQualitySettings(models.Model):
    """إعدادات معايير جودة الحصص"""

    minimum_duration_minutes = models.PositiveIntegerField(
        default=20,
        verbose_name=_('الحد الأدنى لمدة الحصة (بالدقائق)')
    )

    minimum_student_attendance_percentage = models.PositiveIntegerField(
        default=80,
        verbose_name=_('الحد الأدنى لنسبة حضور الطالب (%)')
    )

    minimum_teacher_attendance_percentage = models.PositiveIntegerField(
        default=95,
        verbose_name=_('الحد الأدنى لنسبة حضور المعلم (%)')
    )

    max_disconnection_minutes = models.PositiveIntegerField(
        default=5,
        verbose_name=_('الحد الأقصى لانقطاع الاتصال (بالدقائق)')
    )

    warning_threshold_incomplete_lessons = models.PositiveIntegerField(
        default=3,
        verbose_name=_('عدد الحصص غير المكتملة للتحذير الأول')
    )

    suspension_threshold_incomplete_lessons = models.PositiveIntegerField(
        default=7,
        verbose_name=_('عدد الحصص غير المكتملة للإيقاف المؤقت')
    )

    enable_real_time_monitoring = models.BooleanField(
        default=True,
        verbose_name=_('تفعيل المراقبة في الوقت الفعلي')
    )

    enable_automatic_warnings = models.BooleanField(
        default=True,
        verbose_name=_('تفعيل التحذيرات التلقائية')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('إعدادات جودة الحصص')
        verbose_name_plural = _('إعدادات جودة الحصص')

    def __str__(self):
        return f"إعدادات الجودة - الحد الأدنى: {self.minimum_duration_minutes} دقيقة"

    @classmethod
    def get_settings(cls):
        """الحصول على الإعدادات الحالية أو إنشاء إعدادات افتراضية"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings


class LessonActivityLog(models.Model):
    """سجل أنشطة الحصص المباشرة"""

    EVENT_TYPES = (
        ('lesson_started', _('بدء الحصة')),
        ('lesson_ended', _('انتهاء الحصة')),
        ('participant_joined', _('انضمام مشارك')),
        ('participant_left', _('مغادرة مشارك')),
        ('audio_toggled', _('تبديل الصوت')),
        ('video_toggled', _('تبديل الفيديو')),
        ('screen_share_started', _('بدء مشاركة الشاشة')),
        ('screen_share_stopped', _('إيقاف مشاركة الشاشة')),
        ('chat_message', _('رسالة دردشة')),
        ('connection_lost', _('فقدان الاتصال')),
        ('connection_restored', _('استعادة الاتصال')),
        ('quality_warning', _('تحذير جودة')),
        ('suspicious_activity', _('نشاط مشبوه')),
    )

    live_lesson = models.ForeignKey(
        LiveLesson,
        on_delete=models.CASCADE,
        related_name='activity_logs',
        verbose_name=_('الحصة المباشرة')
    )

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='lesson_activities',
        blank=True,
        null=True,
        verbose_name=_('المستخدم')
    )

    event_type = models.CharField(
        max_length=30,
        choices=EVENT_TYPES,
        verbose_name=_('نوع الحدث')
    )

    event_data = models.JSONField(
        default=dict,
        blank=True,
        verbose_name=_('بيانات الحدث')
    )

    timestamp = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('وقت الحدث')
    )

    ip_address = models.GenericIPAddressField(
        blank=True,
        null=True,
        verbose_name=_('عنوان IP')
    )

    user_agent = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('معلومات المتصفح')
    )

    class Meta:
        verbose_name = _('سجل نشاط الحصة')
        verbose_name_plural = _('سجلات أنشطة الحصص')
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['live_lesson', 'event_type']),
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['timestamp']),
        ]

    def __str__(self):
        user_name = self.user.get_full_name() if self.user else 'نظام'
        return f"{self.live_lesson.title} - {user_name} - {self.get_event_type_display()}"


class LessonQualityReport(models.Model):
    """تقرير جودة الحصة"""

    QUALITY_STATUS = (
        ('excellent', _('ممتاز')),
        ('good', _('جيد')),
        ('acceptable', _('مقبول')),
        ('poor', _('ضعيف')),
        ('suspicious', _('مشبوه')),
    )

    live_lesson = models.OneToOneField(
        LiveLesson,
        on_delete=models.CASCADE,
        related_name='quality_report',
        verbose_name=_('الحصة المباشرة')
    )

    actual_duration_minutes = models.PositiveIntegerField(
        default=0,
        verbose_name=_('المدة الفعلية (بالدقائق)')
    )

    teacher_attendance_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        verbose_name=_('نسبة حضور المعلم (%)')
    )

    student_attendance_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        verbose_name=_('نسبة حضور الطالب (%)')
    )

    interaction_score = models.PositiveIntegerField(
        default=0,
        verbose_name=_('نقاط التفاعل')
    )

    disconnection_count = models.PositiveIntegerField(
        default=0,
        verbose_name=_('عدد مرات الانقطاع')
    )

    total_disconnection_minutes = models.PositiveIntegerField(
        default=0,
        verbose_name=_('إجمالي دقائق الانقطاع')
    )

    quality_status = models.CharField(
        max_length=15,
        choices=QUALITY_STATUS,
        default='acceptable',
        verbose_name=_('حالة الجودة')
    )

    meets_minimum_duration = models.BooleanField(
        default=False,
        verbose_name=_('يحقق الحد الأدنى للمدة')
    )

    meets_attendance_requirements = models.BooleanField(
        default=False,
        verbose_name=_('يحقق متطلبات الحضور')
    )

    is_completed = models.BooleanField(
        default=False,
        verbose_name=_('مكتملة')
    )

    is_suspicious = models.BooleanField(
        default=False,
        verbose_name=_('مشبوهة')
    )

    suspicious_reasons = models.JSONField(
        default=list,
        blank=True,
        verbose_name=_('أسباب الشك')
    )

    admin_notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات الإدارة')
    )

    auto_generated = models.BooleanField(
        default=True,
        verbose_name=_('تم إنشاؤه تلقائياً')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('تاريخ التحديث')
    )

    class Meta:
        verbose_name = _('تقرير جودة الحصة')
        verbose_name_plural = _('تقارير جودة الحصص')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['quality_status']),
            models.Index(fields=['is_suspicious']),
            models.Index(fields=['is_completed']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"تقرير جودة: {self.live_lesson.title} - {self.get_quality_status_display()}"

    def calculate_quality_score(self):
        """حساب نقاط الجودة الإجمالية"""
        score = 0

        # نقاط المدة (40%)
        if self.meets_minimum_duration:
            score += 40
        else:
            # نقاط جزئية بناءً على النسبة
            settings = LessonQualitySettings.get_settings()
            if self.actual_duration_minutes > 0:
                duration_ratio = min(self.actual_duration_minutes / settings.minimum_duration_minutes, 1.0)
                score += int(40 * duration_ratio)

        # نقاط الحضور (40%)
        if self.meets_attendance_requirements:
            score += 40
        else:
            # نقاط جزئية بناءً على متوسط الحضور
            avg_attendance = (self.teacher_attendance_percentage + self.student_attendance_percentage) / 2
            score += int(40 * (avg_attendance / 100))

        # نقاط التفاعل (20%)
        interaction_ratio = min(self.interaction_score / 10, 1.0)  # افتراض أن 10 هو الحد الأقصى
        score += int(20 * interaction_ratio)

        # خصم نقاط للانقطاع
        if self.disconnection_count > 0:
            penalty = min(self.disconnection_count * 5, 20)  # خصم 5 نقاط لكل انقطاع، بحد أقصى 20
            score -= penalty

        return max(0, min(100, score))

    def update_quality_status(self):
        """تحديث حالة الجودة بناءً على النقاط"""
        score = self.calculate_quality_score()

        if score >= 90:
            self.quality_status = 'excellent'
        elif score >= 75:
            self.quality_status = 'good'
        elif score >= 60:
            self.quality_status = 'acceptable'
        elif score >= 40:
            self.quality_status = 'poor'
        else:
            self.quality_status = 'suspicious'
            self.is_suspicious = True

        self.save()

    def check_suspicious_patterns(self):
        """فحص الأنماط المشبوهة"""
        reasons = []
        settings = LessonQualitySettings.get_settings()

        # فحص المدة القصيرة
        if self.actual_duration_minutes < settings.minimum_duration_minutes:
            reasons.append(f"مدة قصيرة: {self.actual_duration_minutes} دقيقة")

        # فحص عدم حضور الطالب
        if self.student_attendance_percentage < settings.minimum_student_attendance_percentage:
            reasons.append(f"حضور طالب منخفض: {self.student_attendance_percentage}%")

        # فحص عدم حضور المعلم
        if self.teacher_attendance_percentage < settings.minimum_teacher_attendance_percentage:
            reasons.append(f"حضور معلم منخفض: {self.teacher_attendance_percentage}%")

        # فحص الانقطاع المفرط
        if self.total_disconnection_minutes > settings.max_disconnection_minutes:
            reasons.append(f"انقطاع مفرط: {self.total_disconnection_minutes} دقيقة")

        # فحص عدم التفاعل
        if self.interaction_score == 0:
            reasons.append("عدم وجود تفاعل مسجل")

        self.suspicious_reasons = reasons
        self.is_suspicious = len(reasons) > 0
        self.save()

        return reasons


class TeacherQualityScore(models.Model):
    """نقاط جودة المعلم"""

    teacher = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='quality_score',
        limit_choices_to={'user_type': 'teacher'},
        verbose_name=_('المعلم')
    )

    total_lessons = models.PositiveIntegerField(
        default=0,
        verbose_name=_('إجمالي الحصص')
    )

    completed_lessons = models.PositiveIntegerField(
        default=0,
        verbose_name=_('الحصص المكتملة')
    )

    suspicious_lessons = models.PositiveIntegerField(
        default=0,
        verbose_name=_('الحصص المشبوهة')
    )

    average_duration_minutes = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=0,
        verbose_name=_('متوسط مدة الحصص (بالدقائق)')
    )

    completion_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        verbose_name=_('معدل الإكمال (%)')
    )

    quality_score = models.PositiveIntegerField(
        default=100,
        verbose_name=_('نقاط الجودة')
    )

    warning_count = models.PositiveIntegerField(
        default=0,
        verbose_name=_('عدد التحذيرات')
    )

    last_warning_date = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('تاريخ آخر تحذير')
    )

    is_suspended = models.BooleanField(
        default=False,
        verbose_name=_('موقوف مؤقتاً')
    )

    suspension_reason = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('سبب الإيقاف')
    )

    suspension_date = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('تاريخ الإيقاف')
    )

    last_updated = models.DateTimeField(
        auto_now=True,
        verbose_name=_('آخر تحديث')
    )

    class Meta:
        verbose_name = _('نقاط جودة المعلم')
        verbose_name_plural = _('نقاط جودة المعلمين')
        ordering = ['-quality_score']

    def __str__(self):
        return f"{self.teacher.get_full_name()} - {self.quality_score} نقطة"

    def update_statistics(self):
        """تحديث الإحصائيات"""
        from django.db.models import Avg

        # حساب إحصائيات الحصص
        quality_reports = LessonQualityReport.objects.filter(
            live_lesson__teacher=self.teacher
        )

        self.total_lessons = quality_reports.count()
        self.completed_lessons = quality_reports.filter(is_completed=True).count()
        self.suspicious_lessons = quality_reports.filter(is_suspicious=True).count()

        # حساب متوسط المدة
        avg_duration = quality_reports.aggregate(
            avg=Avg('actual_duration_minutes')
        )['avg']
        self.average_duration_minutes = avg_duration or 0

        # حساب معدل الإكمال
        if self.total_lessons > 0:
            self.completion_rate = (self.completed_lessons / self.total_lessons) * 100
        else:
            self.completion_rate = 100

        # حساب نقاط الجودة
        self.calculate_quality_score()
        self.save()

    def calculate_quality_score(self):
        """حساب نقاط الجودة"""
        score = 100  # البداية بـ 100 نقطة

        # خصم نقاط للحصص المشبوهة
        if self.total_lessons > 0:
            suspicious_rate = (self.suspicious_lessons / self.total_lessons) * 100
            score -= int(suspicious_rate * 2)  # خصم نقطتين لكل 1% حصص مشبوهة

        # خصم نقاط لمعدل الإكمال المنخفض
        if self.completion_rate < 80:
            score -= int((80 - self.completion_rate) * 1.5)

        # خصم نقاط للتحذيرات
        score -= self.warning_count * 10

        # خصم إضافي للإيقاف
        if self.is_suspended:
            score -= 50

        self.quality_score = max(0, min(100, score))
        return self.quality_score

    def add_warning(self, reason):
        """إضافة تحذير"""
        self.warning_count += 1
        self.last_warning_date = timezone.now()

        # فحص الحاجة للإيقاف
        settings = LessonQualitySettings.get_settings()
        if self.warning_count >= settings.suspension_threshold_incomplete_lessons:
            self.is_suspended = True
            self.suspension_reason = f"تجاوز حد التحذيرات: {reason}"
            self.suspension_date = timezone.now()

        self.calculate_quality_score()
        self.save()

        # إرسال إشعار
        self.send_warning_notification(reason)

    def send_warning_notification(self, reason):
        """إرسال إشعار التحذير"""
        try:
            from notifications.utils import NotificationService
            NotificationService.notify_teacher_warning(self.teacher, reason, self.warning_count)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"فشل في إرسال إشعار التحذير: {e}")


class QualityMonitoringAlert(models.Model):
    """تنبيهات مراقبة الجودة"""

    ALERT_TYPES = (
        ('short_lesson', _('حصة قصيرة')),
        ('no_student', _('عدم حضور طالب')),
        ('excessive_disconnection', _('انقطاع مفرط')),
        ('suspicious_pattern', _('نمط مشبوه')),
        ('teacher_warning', _('تحذير معلم')),
        ('teacher_suspension', _('إيقاف معلم')),
        ('quality_degradation', _('تدهور الجودة')),
    )

    PRIORITY_LEVELS = (
        ('low', _('منخفض')),
        ('medium', _('متوسط')),
        ('high', _('عالي')),
        ('critical', _('حرج')),
    )

    alert_type = models.CharField(
        max_length=30,
        choices=ALERT_TYPES,
        verbose_name=_('نوع التنبيه')
    )

    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_LEVELS,
        default='medium',
        verbose_name=_('الأولوية')
    )

    title = models.CharField(
        max_length=200,
        verbose_name=_('عنوان التنبيه')
    )

    message = models.TextField(
        verbose_name=_('رسالة التنبيه')
    )

    live_lesson = models.ForeignKey(
        LiveLesson,
        on_delete=models.CASCADE,
        related_name='quality_alerts',
        blank=True,
        null=True,
        verbose_name=_('الحصة المباشرة')
    )

    teacher = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='quality_alerts',
        blank=True,
        null=True,
        limit_choices_to={'user_type': 'teacher'},
        verbose_name=_('المعلم')
    )

    quality_report = models.ForeignKey(
        LessonQualityReport,
        on_delete=models.CASCADE,
        related_name='alerts',
        blank=True,
        null=True,
        verbose_name=_('تقرير الجودة')
    )

    is_read = models.BooleanField(
        default=False,
        verbose_name=_('تم القراءة')
    )

    is_resolved = models.BooleanField(
        default=False,
        verbose_name=_('تم الحل')
    )

    resolved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        related_name='resolved_alerts',
        blank=True,
        null=True,
        verbose_name=_('تم الحل بواسطة')
    )

    resolved_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name=_('تاريخ الحل')
    )

    resolution_notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('ملاحظات الحل')
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('تاريخ الإنشاء')
    )

    class Meta:
        verbose_name = _('تنبيه مراقبة الجودة')
        verbose_name_plural = _('تنبيهات مراقبة الجودة')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['alert_type', 'priority']),
            models.Index(fields=['is_read', 'is_resolved']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.get_alert_type_display()} - {self.title}"

    def mark_as_resolved(self, user, notes=None):
        """تحديد التنبيه كمحلول"""
        self.is_resolved = True
        self.resolved_by = user
        self.resolved_at = timezone.now()
        if notes:
            self.resolution_notes = notes
        self.save()

    @classmethod
    def create_alert(cls, alert_type, title, message, priority='medium', **kwargs):
        """إنشاء تنبيه جديد"""
        alert = cls.objects.create(
            alert_type=alert_type,
            title=title,
            message=message,
            priority=priority,
            **kwargs
        )

        # إرسال إشعار للمديرين
        alert.notify_admins()
        return alert

    def notify_admins(self):
        """إرسال إشعار للمديرين"""
        try:
            from notifications.utils import NotificationService
            NotificationService.notify_quality_alert(self)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"فشل في إرسال إشعار التنبيه: {e}")
