from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.views import LoginView
from django.contrib.auth.decorators import login_required
from django.contrib.auth import get_user_model, login as auth_login
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.db.models import Count, Q, Avg, Sum
from django.utils import timezone
from datetime import timedelta
from django.urls import reverse
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import base64

from .models import AcademySettings, EmailNotification, EmailTracking, EmailApprovalRequest, EmailTemplate
from .forms import AcademySettingsForm
from .services import SMTPService, PasswordEncryption

User = get_user_model()


class CustomLoginView(LoginView):
    """صفحة تسجيل الدخول المخصصة"""
    template_name = 'auth/login.html'
    redirect_authenticated_user = True

    def form_invalid(self, form):
        """عرض رسالة خطأ عند فشل تسجيل الدخول"""
        # التحقق من وجود المستخدم حتى لو كان غير نشط
        username = form.data.get('username')
        password = form.data.get('password')

        if username and password:
            # التحقق من وجود الحساب في سجلات الحذف - تم تعطيله بعد حذف logs app
            deleted_user_log = None

            if deleted_user_log:
                # استخراج معلومات الحذف من السجل
                deleted_data = deleted_user_log.data

                # حفظ معلومات الحذف في الجلسة
                self.request.session['deleted_username'] = deleted_data.get('username', '')
                self.request.session['deleted_user_type'] = deleted_data.get('user_type', '')
                self.request.session['delete_reason'] = deleted_data.get('delete_reason', '')
                self.request.session['deleted_at'] = deleted_data.get('deleted_at', '')
                self.request.session['deleted_by'] = deleted_data.get('deleted_by', '')

                messages.warning(
                    self.request,
                    "هذا الحساب تم حذفه من قبل الإدارة."
                )

                # توجيه للصفحة العامة للحسابات المحذوفة
                return redirect('public_deleted_page')

            try:
                # البحث عن المستخدم بغض النظر عن حالة is_active
                # محاولة البحث باسم المستخدم أو البريد الإلكتروني
                user = User.objects.get(Q(username=username) | Q(email=username))

                # التحقق من كلمة المرور
                from django.contrib.auth.hashers import check_password
                if check_password(password, user.password):
                    # كلمة المرور صحيحة، التحقق من حالة الحساب

                    # حفظ معلومات المستخدم في الجلسة
                    self.request.session['username'] = user.username
                    self.request.session['user_type'] = user.user_type

                    # التحقق من الحظر أولاً
                    if user.is_currently_banned():
                        self.request.session['banned_username'] = user.username
                        self.request.session['ban_reason'] = user.ban_reason
                        self.request.session['ban_type'] = user.ban_type
                        if user.banned_until:
                            self.request.session['banned_until'] = user.banned_until.strftime('%Y-%m-%d %H:%M')

                        messages.warning(
                            self.request,
                            f"حسابك محظور حالياً. {user.ban_reason or ''}"
                        )

                        # التمييز بين المستخدم الجديد والمستخدم النشط سابقًا
                        if user.was_active:
                            # مستخدم كان نشطًا ثم تم حظره - توجيه للصفحة الداخلية
                            return redirect('user_banned')
                        else:
                            # مستخدم جديد تم حظره قبل التفعيل - توجيه للصفحة العامة
                            return redirect('public_banned_page')

                    # التحقق من حالة التحقق
                    if user.verification_status == 'pending':
                        self.request.session['pending_username'] = user.username
                        self.request.session['pending_user_type'] = user.user_type

                        messages.info(
                            self.request,
                            "حسابك قيد المراجعة. يرجى الانتظار حتى تتم الموافقة عليه."
                        )

                        # التمييز بين المستخدم الجديد والمستخدم النشط سابقًا
                        if user.was_active:
                            # مستخدم كان نشطًا ثم تم تعليقه للمراجعة - توجيه للصفحة الداخلية
                            return redirect('verification_pending')
                        else:
                            # مستخدم جديد قيد المراجعة - توجيه للصفحة العامة
                            return redirect('public_pending_page')

                    elif user.verification_status == 'rejected':
                        self.request.session['rejected_username'] = user.username
                        self.request.session['rejected_user_type'] = user.user_type
                        self.request.session['rejection_reason'] = user.rejection_reason

                        messages.warning(
                            self.request,
                            f"تم رفض حسابك. {user.rejection_reason or ''}"
                        )

                        # التمييز بين المستخدم الجديد والمستخدم النشط سابقًا
                        if user.was_active:
                            # مستخدم كان نشطًا ثم تم رفضه - توجيه للصفحة الداخلية
                            return redirect('verification_rejected')
                        else:
                            # مستخدم جديد تم رفضه - توجيه للصفحة العامة
                            return redirect('public_rejected_page')

                    # إذا وصلنا إلى هنا، فهناك مشكلة أخرى في الحساب
                    messages.error(
                        self.request,
                        "لا يمكن تسجيل الدخول. يرجى التواصل مع الإدارة."
                    )
                    return super().form_invalid(form)

            except User.DoesNotExist:
                # المستخدم غير موجود، استمر في السلوك الافتراضي
                pass

        # إضافة رسالة خطأ واضحة
        messages.error(
            self.request,
            "اسم المستخدم أو كلمة المرور غير صحيحة. يرجى المحاولة مرة أخرى."
        )
        return super().form_invalid(form)

    def form_valid(self, form):
        """التحقق من حالة المستخدم قبل تسجيل الدخول"""
        # الحصول على المستخدم
        username = form.cleaned_data.get('username')
        try:
            # محاولة البحث باسم المستخدم أو البريد الإلكتروني
            user = User.objects.get(Q(username=username) | Q(email=username))

            # التحقق من الحظر أولاً
            if not user.is_admin() and user.is_currently_banned():
                # حفظ معلومات المستخدم في الجلسة
                self.request.session['banned_username'] = user.username
                self.request.session['ban_reason'] = user.ban_reason
                self.request.session['ban_type'] = user.ban_type
                if user.banned_until:
                    self.request.session['banned_until'] = user.banned_until.strftime('%Y-%m-%d %H:%M')

                # تسجيل الدخول للمستخدم مع تحديد backend
                from django.contrib.auth import get_user_model
                from users.backends import EmailOrUsernameModelBackend
                auth_login(self.request, user, backend='users.backends.EmailOrUsernameModelBackend')
                # إضافة رسالة توضيحية
                messages.warning(
                    self.request,
                    f"حسابك محظور حالياً. {user.ban_reason or ''}"
                )

                # التمييز بين المستخدم الجديد والمستخدم النشط سابقًا
                if user.was_active:
                    # مستخدم كان نشطًا ثم تم حظره - توجيه للصفحة الداخلية
                    return redirect('user_banned')
                else:
                    # مستخدم جديد تم حظره قبل التفعيل - توجيه للصفحة العامة
                    return redirect('public_banned_page')

            # التحقق من حالة التحقق
            if not user.is_admin() and user.verification_status == 'pending':
                # حفظ معلومات المستخدم في الجلسة
                self.request.session['pending_username'] = user.username
                self.request.session['pending_user_type'] = user.user_type

                # تسجيل الدخول للمستخدم مع تحديد backend
                auth_login(self.request, user, backend='users.backends.EmailOrUsernameModelBackend')
                # إضافة رسالة توضيحية
                messages.info(
                    self.request,
                    "حسابك قيد المراجعة. يرجى الانتظار حتى تتم الموافقة عليه."
                )

                # التمييز بين المستخدم الجديد والمستخدم النشط سابقًا
                if user.was_active:
                    # مستخدم كان نشطًا ثم تم تعليقه للمراجعة - توجيه للصفحة الداخلية
                    return redirect('verification_pending')
                else:
                    # مستخدم جديد قيد المراجعة - توجيه للصفحة العامة
                    return redirect('public_pending_page')

            elif not user.is_admin() and user.verification_status == 'rejected':
                # حفظ معلومات المستخدم في الجلسة
                self.request.session['rejected_username'] = user.username
                self.request.session['rejected_user_type'] = user.user_type
                self.request.session['rejection_reason'] = user.rejection_reason

                # تسجيل الدخول للمستخدم مع تحديد backend
                auth_login(self.request, user, backend='users.backends.EmailOrUsernameModelBackend')
                # إضافة رسالة توضيحية
                messages.warning(
                    self.request,
                    f"تم رفض حسابك. {user.rejection_reason or ''}"
                )

                # التمييز بين المستخدم الجديد والمستخدم النشط سابقًا
                if user.was_active:
                    # مستخدم كان نشطًا ثم تم رفضه - توجيه للصفحة الداخلية
                    return redirect('verification_rejected')
                else:
                    # مستخدم جديد تم رفضه - توجيه للصفحة العامة
                    return redirect('public_rejected_page')

            # تسجيل الدخول الناجح مع تحديد backend
            # استخدام form.get_user() بدلاً من super().form_valid(form)
            auth_login(self.request, user, backend='users.backends.EmailOrUsernameModelBackend')
            # إضافة رسالة ترحيب
            messages.success(
                self.request,
                f"مرحباً بك {user.get_full_name() or user.username}! تم تسجيل الدخول بنجاح."
            )
            # الانتقال إلى صفحة النجاح
            return redirect(self.get_success_url())

        except User.DoesNotExist:
            # استمرار في السلوك الافتراضي (سيتم التعامل معه في form_invalid)
            pass

        return super().form_valid(form)

    def get_success_url(self):
        """توجيه المستخدم حسب نوعه بعد تسجيل الدخول"""
        user = self.request.user

        # التحقق من الحظر أولاً
        if user.is_currently_banned():
            return reverse('user_banned')

        # التحقق من حالة التحقق
        if not user.can_access_dashboard():
            if user.is_pending_verification():
                return reverse('verification_pending')
            elif user.is_rejected():
                return reverse('verification_rejected')

        # توجيه حسب نوع المستخدم
        if user.is_admin():
            return '/dashboard/admin/'
        elif user.is_teacher():
            return '/dashboard/teacher/'
        elif user.is_student():
            return '/dashboard/student/'
        return '/dashboard/'


def dashboard_redirect(request):
    """توجيه المستخدم إلى لوحة التحكم المناسبة"""
    if request.user.is_authenticated:
        # التحقق من الحظر أولاً
        if request.user.is_currently_banned():
            return redirect('user_banned')

        # التحقق من حالة التحقق
        if not request.user.is_admin():
            if request.user.verification_status == 'pending':
                return redirect('verification_pending')
            elif request.user.verification_status == 'rejected':
                return redirect('verification_rejected')

        # التوجيه حسب نوع المستخدم
        if request.user.is_admin():
            return redirect('/dashboard/admin/')
        elif request.user.is_teacher():
            return redirect('/dashboard/teacher/')
        elif request.user.is_student():
            return redirect('/dashboard/student/')
        return redirect('/dashboard/')
    return redirect('login')


@login_required
def dashboard(request):
    """لوحة التحكم العامة - توجيه المستخدم حسب نوعه"""
    # التحقق من الحظر أولاً
    if request.user.is_currently_banned():
        return redirect('user_banned')

    # التحقق من حالة التحقق
    if not request.user.is_admin():
        if request.user.verification_status == 'pending':
            return redirect('verification_pending')
        elif request.user.verification_status == 'rejected':
            return redirect('verification_rejected')

    # التوجيه حسب نوع المستخدم
    if request.user.is_admin():
        return redirect('/dashboard/admin/')
    elif request.user.is_teacher():
        return redirect('/dashboard/teacher/')
    elif request.user.is_student():
        return redirect('/dashboard/student/')
    else:
        messages.error(request, "نوع المستخدم غير محدد.")
        return redirect('login')


@login_required
def admin_dashboard(request):
    """لوحة تحكم المدير"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.utils import timezone
    from datetime import timedelta
    # تم إزالة التبعية على courses.models
    from lessons.models import Lesson
    from support.models import SupportTicket

    # إحصائيات المستخدمين
    total_users = User.objects.count()
    total_students = User.objects.filter(user_type='student').count()
    total_teachers = User.objects.filter(user_type='teacher').count()

    # إحصائيات التسجيلات - تم تعطيلها بعد حذف courses app
    active_enrollments = 0  # تم إزالة التبعية على Enrollment

    # إحصائيات الحصص
    today = timezone.now().date()
    today_lessons = Lesson.objects.filter(
        scheduled_date__date=today,
        status__in=['scheduled', 'in_progress']
    ).count()

    # إحصائيات الدعم الفني
    open_tickets = SupportTicket.objects.filter(
        status__in=['open', 'in_progress', 'waiting_response']
    ).count()

    # المستخدمون الجدد هذا الأسبوع
    week_ago = timezone.now() - timedelta(days=7)
    new_users_this_week = User.objects.filter(
        created_at__gte=week_ago
    ).count()

    # إحصائيات مالية - تم تعطيلها بعد حذف courses app
    total_revenue = 0  # تم إزالة التبعية على Payment

    # الحصص المكتملة هذا الشهر
    month_start = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    completed_lessons_this_month = Lesson.objects.filter(
        status='completed',
        actual_end_time__gte=month_start
    ).count()

    # إحصائيات إضافية للإجراءات السريعة
    # طلبات التحقق المعلقة (جميع الحالات المعلقة)
    pending_verifications_count = User.objects.filter(
        verification_status='pending'
    ).exclude(user_type='admin').count()

    # ملاحظة: تم حذف نظام طلبات السحب
    pending_payouts_count = 0

    # الرسائل غير المقروءة للمدير
    from messaging.models import ChatMessage, Conversation
    from django.db.models import Q

    # البحث عن المحادثات التي يشارك فيها المدير
    admin_conversations = Conversation.objects.filter(
        Q(participant1=request.user) | Q(participant2=request.user) |
        Q(student=request.user) | Q(teacher=request.user)
    )

    # حساب الرسائل غير المقروءة في هذه المحادثات
    unread_messages_count = ChatMessage.objects.filter(
        conversation__in=admin_conversations,
        is_read=False
    ).exclude(sender=request.user).count()

    # إحصائيات إضافية للبطاقات الجديدة
    # المعلمين النشطين
    active_teachers_count = User.objects.filter(
        user_type='teacher',
        is_active=True,
        verification_status='approved'
    ).count()

    # الطلاب النشطين - محدث للنظام الحالي
    active_students_count = User.objects.filter(
        user_type='student',
        is_active=True,
        verification_status='approved'
    ).count()

    # إجمالي الإيرادات من الاشتراكات
    from subscriptions.models import SubscriptionPayment
    from django.db.models import Sum
    total_revenue_amount = SubscriptionPayment.objects.filter(
        status='completed'
    ).aggregate(
        total=Sum('amount')
    )['total'] or 0

    # الحصص المجدولة للأسبوع القادم
    from datetime import timedelta
    next_week = timezone.now() + timedelta(days=7)
    upcoming_lessons_week = Lesson.objects.filter(
        scheduled_date__gte=timezone.now(),
        scheduled_date__lte=next_week,
        status='scheduled'
    ).count()

    # إحصائيات إضافية للوحة التحكم
    # إجمالي الحصص المباشرة النشطة
    from lessons.models import LiveLesson
    active_live_lessons_count = LiveLesson.objects.filter(
        status='live'
    ).count()

    # إجمالي الاشتراكات النشطة
    from subscriptions.models import StudentSubscription
    active_subscriptions_count = StudentSubscription.objects.filter(
        status='active'
    ).count()

    # متوسط التقييم العام للمعلمين - محدث للنظام الحالي
    from lessons.models import UnifiedLessonRating
    from django.db.models import Avg
    average_teacher_rating = UnifiedLessonRating.objects.filter(
        teacher__isnull=False
    ).aggregate(
        avg_rating=Avg('overall_rating')
    )['avg_rating'] or 0

    # عدد الفواتير المولدة هذا الشهر
    try:
        from subscriptions.invoice_models import Invoice
        invoices_this_month = Invoice.objects.filter(
            created_at__gte=month_start
        ).count()
    except ImportError:
        invoices_this_month = 0

    # عدد المستخدمين الجدد هذا الأسبوع
    week_start = timezone.now() - timedelta(days=7)
    new_users_this_week = User.objects.filter(
        date_joined__gte=week_start
    ).count()

    # عدد الحصص المكتملة هذا الشهر
    from lessons.models import Lesson, LiveLesson
    completed_lessons_this_month = (
        Lesson.objects.filter(
            status='completed',
            created_at__gte=month_start
        ).count() +
        LiveLesson.objects.filter(
            status='completed',
            created_at__gte=month_start
        ).count()
    )

    # عدد الحصص المباشرة النشطة الآن
    active_live_lessons_count = LiveLesson.objects.filter(
        status='live'
    ).count()

    # إحصائيات حالة السيرفر والنظام
    try:
        import psutil
        import platform
        import os
        from django.conf import settings
        from django.db import connection
        from django.core.cache import cache
        import time

        # معلومات السيرفر
        server_info = {
            'platform': platform.system(),
            'platform_version': platform.release(),
            'python_version': platform.python_version(),
            'django_version': settings.DEBUG,  # سنستخدم هذا للتحقق من وضع التطوير
        }

        # معلومات الذاكرة
        try:
            memory = psutil.virtual_memory()
            memory_info = {
                'total': round(memory.total / (1024**3), 2),  # GB
                'available': round(memory.available / (1024**3), 2),  # GB
                'used': round(memory.used / (1024**3), 2),  # GB
                'percentage': memory.percent
            }
        except:
            memory_info = {
                'total': 0,
                'available': 0,
                'used': 0,
                'percentage': 0
            }

        # معلومات المعالج
        try:
            cpu_info = {
                'count': psutil.cpu_count(),
                'usage': psutil.cpu_percent(interval=0.1),  # تقليل وقت الانتظار
                'load_avg': os.getloadavg() if hasattr(os, 'getloadavg') else [0, 0, 0]
            }
        except:
            cpu_info = {
                'count': 0,
                'usage': 0,
                'load_avg': [0, 0, 0]
            }

        # معلومات القرص الصلب
        try:
            disk = psutil.disk_usage('/' if os.name != 'nt' else 'C:')
            disk_info = {
                'total': round(disk.total / (1024**3), 2),  # GB
                'used': round(disk.used / (1024**3), 2),  # GB
                'free': round(disk.free / (1024**3), 2),  # GB
                'percentage': round((disk.used / disk.total) * 100, 1)
            }
        except:
            disk_info = {
                'total': 0,
                'used': 0,
                'free': 0,
                'percentage': 0
            }

        # وقت تشغيل السيرفر (تقدير)
        try:
            uptime_seconds = time.time() - psutil.boot_time()
            uptime_hours = int(uptime_seconds // 3600)
            uptime_days = uptime_hours // 24
            uptime_hours = uptime_hours % 24
            server_uptime = f"{uptime_days} يوم، {uptime_hours} ساعة"
        except:
            server_uptime = "غير متاح"

    except ImportError:
        # في حالة عدم توفر psutil، استخدم قيم افتراضية
        server_info = {
            'platform': 'غير معروف',
            'platform_version': 'غير معروف',
            'python_version': 'غير معروف',
            'django_version': False,
        }
        memory_info = {
            'total': 0,
            'available': 0,
            'used': 0,
            'percentage': 0
        }
        cpu_info = {
            'count': 0,
            'usage': 0,
            'load_avg': [0, 0, 0]
        }
        disk_info = {
            'total': 0,
            'used': 0,
            'free': 0,
            'percentage': 0
        }
        server_uptime = "غير متاح"

    # حالة قاعدة البيانات
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            db_status = 'متصل'
            db_response_time = 'سريع'
    except Exception:
        db_status = 'غير متصل'
        db_response_time = 'بطيء'

    # حالة الكاش
    try:
        cache.set('health_check', 'ok', 30)
        cache_test = cache.get('health_check')
        cache_status = 'يعمل' if cache_test == 'ok' else 'لا يعمل'
    except Exception:
        cache_status = 'لا يعمل'

    # الحصص المباشرة النشطة
    try:
        from lessons.models import LiveLesson
        live_lessons_now = LiveLesson.objects.filter(status='live').count()
    except:
        live_lessons_now = 0

    # المستخدمين المتصلين حالياً (تقدير بناءً على النشاط الأخير)
    try:
        from django.utils import timezone
        last_hour = timezone.now() - timedelta(hours=1)
        active_users_now = User.objects.filter(
            last_login__gte=last_hour
        ).count()
    except:
        active_users_now = 0

    # حالة النظام العامة
    system_health = 'ممتاز'
    if memory_info['percentage'] > 90 or cpu_info['usage'] > 90 or disk_info['percentage'] > 90:
        system_health = 'تحذير'
    elif memory_info['percentage'] > 80 or cpu_info['usage'] > 80 or disk_info['percentage'] > 80:
        system_health = 'جيد'

    # جلب النشاطات الأخيرة من مختلف أقسام النظام
    recent_activities = []

    # 1. المستخدمين الجدد (آخر 24 ساعة)
    last_24_hours = timezone.now() - timedelta(hours=24)
    new_users = User.objects.filter(
        created_at__gte=last_24_hours
    ).exclude(user_type='admin').order_by('-created_at')[:3]

    for user in new_users:
        time_diff = timezone.now() - user.created_at
        if time_diff.total_seconds() < 3600:  # أقل من ساعة
            time_text = f"منذ {int(time_diff.total_seconds() // 60)} دقيقة"
        else:
            time_text = f"منذ {int(time_diff.total_seconds() // 3600)} ساعة"

        user_type_text = "طالب" if user.user_type == 'student' else "معلم"
        recent_activities.append({
            'type': 'user_registration',
            'title': f'تم إنشاء حساب {user_type_text} جديد',
            'description': f'{user.get_full_name()}',
            'time': time_text,
            'icon': 'fas fa-user-plus',
            'color': 'green',
            'created_at': user.created_at
        })

    # 2. الحصص المجدولة حديثاً (آخر 24 ساعة)
    new_lessons = Lesson.objects.filter(
        created_at__gte=last_24_hours,
        status='scheduled'
    ).select_related('student', 'teacher').order_by('-created_at')[:3]

    for lesson in new_lessons:
        time_diff = timezone.now() - lesson.created_at
        if time_diff.total_seconds() < 3600:
            time_text = f"منذ {int(time_diff.total_seconds() // 60)} دقيقة"
        else:
            time_text = f"منذ {int(time_diff.total_seconds() // 3600)} ساعة"

        recent_activities.append({
            'type': 'lesson_scheduled',
            'title': 'تم جدولة حصة جديدة',
            'description': f'بين {lesson.student.get_full_name()} و {lesson.teacher.get_full_name()}',
            'time': time_text,
            'icon': 'fas fa-calendar-plus',
            'color': 'blue',
            'created_at': lesson.created_at
        })

    # 3. الحصص المكتملة (آخر 24 ساعة)
    completed_lessons = Lesson.objects.filter(
        actual_end_time__gte=last_24_hours,
        status='completed'
    ).select_related('student', 'teacher').order_by('-actual_end_time')[:3]

    for lesson in completed_lessons:
        time_diff = timezone.now() - lesson.actual_end_time
        if time_diff.total_seconds() < 3600:
            time_text = f"منذ {int(time_diff.total_seconds() // 60)} دقيقة"
        else:
            time_text = f"منذ {int(time_diff.total_seconds() // 3600)} ساعة"

        recent_activities.append({
            'type': 'lesson_completed',
            'title': 'تم إكمال حصة',
            'description': f'بين {lesson.student.get_full_name()} و {lesson.teacher.get_full_name()}',
            'time': time_text,
            'icon': 'fas fa-check-circle',
            'color': 'purple',
            'created_at': lesson.actual_end_time
        })

    # 4. تذاكر الدعم الجديدة (آخر 24 ساعة)
    from support.models import SupportTicket
    new_tickets = SupportTicket.objects.filter(
        created_at__gte=last_24_hours
    ).select_related('created_by').order_by('-created_at')[:3]

    for ticket in new_tickets:
        time_diff = timezone.now() - ticket.created_at
        if time_diff.total_seconds() < 3600:
            time_text = f"منذ {int(time_diff.total_seconds() // 60)} دقيقة"
        else:
            time_text = f"منذ {int(time_diff.total_seconds() // 3600)} ساعة"

        recent_activities.append({
            'type': 'support_ticket',
            'title': 'تذكرة دعم جديدة',
            'description': f'من {ticket.created_by.get_full_name()} - {ticket.title[:50]}...',
            'time': time_text,
            'icon': 'fas fa-ticket-alt',
            'color': 'yellow',
            'created_at': ticket.created_at
        })

    # 5. المدفوعات الجديدة (آخر 24 ساعة) - تم تعطيلها بعد حذف courses app
    new_payments = []  # تم إزالة التبعية على Payment

    for payment in new_payments:
        time_diff = timezone.now() - payment.payment_date
        if time_diff.total_seconds() < 3600:
            time_text = f"منذ {int(time_diff.total_seconds() // 60)} دقيقة"
        else:
            time_text = f"منذ {int(time_diff.total_seconds() // 3600)} ساعة"

        recent_activities.append({
            'type': 'payment',
            'title': 'دفعة جديدة',
            'description': f'من طالب - ${payment.amount}',  # تم إزالة التبعية على enrollment
            'time': time_text,
            'icon': 'fas fa-credit-card',
            'color': 'indigo',
            'created_at': payment.payment_date
        })

    # ملاحظة: تم حذف نظام طلبات السحب

    # ترتيب النشاطات حسب التاريخ (الأحدث أولاً) وأخذ أحدث 6 نشاطات
    recent_activities.sort(key=lambda x: x['created_at'], reverse=True)
    recent_activities = recent_activities[:6]

    # إضافة نشاطات وهمية إذا لم توجد نشاطات حقيقية (للعرض التوضيحي)
    if not recent_activities:
        from datetime import timedelta
        now = timezone.now()

        recent_activities = [
            {
                'type': 'user_registration',
                'title': 'تم إنشاء حساب طالب جديد',
                'description': 'أحمد محمد علي',
                'time': 'منذ 5 دقائق',
                'icon': 'fas fa-user-plus',
                'color': 'green',
                'created_at': now - timedelta(minutes=5)
            },
            {
                'type': 'lesson_scheduled',
                'title': 'تم جدولة حصة جديدة',
                'description': 'بين فاطمة أحمد و الأستاذ محمد',
                'time': 'منذ 15 دقيقة',
                'icon': 'fas fa-calendar-plus',
                'color': 'blue',
                'created_at': now - timedelta(minutes=15)
            },
            {
                'type': 'support_ticket',
                'title': 'تذكرة دعم جديدة',
                'description': 'من سارة محمد - مشكلة في تسجيل الدخول',
                'time': 'منذ 30 دقيقة',
                'icon': 'fas fa-ticket-alt',
                'color': 'yellow',
                'created_at': now - timedelta(minutes=30)
            },
            {
                'type': 'lesson_completed',
                'title': 'تم إكمال حصة',
                'description': 'بين عبدالله أحمد و الأستاذة عائشة',
                'time': 'منذ ساعة',
                'icon': 'fas fa-check-circle',
                'color': 'purple',
                'created_at': now - timedelta(hours=1)
            },
            {
                'type': 'payment_request',
                'title': 'طلب دفع جديد',
                'description': 'من خالد محمد - $50',
                'time': 'منذ ساعتين',
                'icon': 'fas fa-credit-card',
                'color': 'indigo',
                'created_at': now - timedelta(hours=2)
            },
            {
                'type': 'payout_request',
                'title': 'طلب سحب جديد',
                'description': 'من الأستاذ يوسف - $120',
                'time': 'منذ 3 ساعات',
                'icon': 'fas fa-money-bill-wave',
                'color': 'emerald',
                'created_at': now - timedelta(hours=3)
            }
        ]

    context = {
        'total_users': total_users,
        'total_students': total_students,
        'total_teachers': total_teachers,
        'active_enrollments': active_enrollments,
        'today_lessons': today_lessons,
        'open_tickets': open_tickets,
        'new_users_this_week': new_users_this_week,
        'total_revenue': total_revenue,
        'completed_lessons_this_month': completed_lessons_this_month,
        'pending_verifications_count': pending_verifications_count,
        'pending_payouts_count': pending_payouts_count,
        'unread_messages_count': unread_messages_count,
        'active_teachers_count': active_teachers_count,
        'active_students_count': active_students_count,
        'total_revenue_amount': total_revenue_amount,
        'upcoming_lessons_week': upcoming_lessons_week,
        'recent_activities': recent_activities,
        'active_live_lessons_count': active_live_lessons_count,
        'active_subscriptions_count': active_subscriptions_count,
        'average_teacher_rating': round(average_teacher_rating, 1) if average_teacher_rating else 0,
        'invoices_this_month': invoices_this_month,
        # بيانات حالة السيرفر والنظام
        'server_info': server_info,
        'memory_info': memory_info,
        'cpu_info': cpu_info,
        'disk_info': disk_info,
        'db_status': db_status,
        'db_response_time': db_response_time,
        'cache_status': cache_status,
        'live_lessons_now': live_lessons_now,
        'active_users_now': active_users_now,
        'system_health': system_health,
        'server_uptime': server_uptime,
    }

    return render(request, 'dashboard/admin.html', context)





@login_required
def admin_reports(request):
    """صفحة التقارير والإحصائيات"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.utils import timezone
    from datetime import timedelta
    # تم إزالة التبعية على courses.models
    from lessons.models import Lesson, LessonRating
    from django.db.models import Sum, Avg, Count

    # الفترة الزمنية للتقرير
    today = timezone.now().date()
    month_start = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    last_month_start = (month_start - timedelta(days=1)).replace(day=1)

    # الإحصائيات الرئيسية
    total_users = User.objects.count()
    completed_lessons = Lesson.objects.filter(status='completed').count()
    total_revenue = 0  # تم إزالة التبعية على Payment

    # متوسط التقييم العام
    average_rating = LessonRating.objects.aggregate(
        avg=Avg('overall_satisfaction')
    )['avg'] or 0

    # نمو المستخدمين
    last_month_users = User.objects.filter(
        created_at__gte=last_month_start,
        created_at__lt=month_start
    ).count()
    this_month_users = User.objects.filter(
        created_at__gte=month_start
    ).count()

    user_growth = 0
    if last_month_users > 0:
        user_growth = round(((this_month_users - last_month_users) / last_month_users) * 100, 1)

    # إحصائيات المستخدمين
    students_count = User.objects.filter(user_type='student').count()
    teachers_count = User.objects.filter(user_type='teacher').count()
    admins_count = User.objects.filter(user_type='admin').count()
    active_users = User.objects.filter(is_active=True).count()

    # إحصائيات الدورات - تم تعطيلها بعد حذف courses app
    total_courses = 0
    active_courses = 0
    active_enrollments = 0

    # معدل الإكمال - تم تعطيله بعد حذف courses app
    total_enrollments = 0
    completed_enrollments = 0
    completion_rate = 0

    # أفضل المعلمين - تم تعطيله بعد حذف courses app
    top_teachers = []

    # أشهر الدورات - تم تعطيله بعد حذف courses app
    popular_courses = []

    context = {
        'total_users': total_users,
        'completed_lessons': completed_lessons,
        'total_revenue': total_revenue,
        'avg_rating': round(average_rating, 1),
        'user_growth': user_growth,
        'lesson_growth': 8,  # يمكن حسابه لاحقاً
        'revenue_growth': 15,  # يمكن حسابه لاحقاً
        'rating_improvement': 0.2,  # يمكن حسابه لاحقاً
        'students_count': students_count,
        'teachers_count': teachers_count,
        'admins_count': admins_count,
        'active_users': active_users,
        'this_month_users': this_month_users,
        'total_courses': total_courses,
        'active_courses': active_courses,
        'active_enrollments': active_enrollments,
        'completion_rate': completion_rate,
        'top_teachers': top_teachers,
        'popular_courses': popular_courses,
    }

    return render(request, 'admin/reports.html', context)


@login_required
def teacher_dashboard(request):
    """لوحة تحكم المعلم"""
    if not request.user.is_teacher():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.utils import timezone
    from datetime import timedelta
    # تم إزالة التبعية على courses.models
    from lessons.models import Lesson
    from django.db.models import Sum, Count, Avg

    # إحصائيات الحصص
    today = timezone.now().date()
    today_lessons_count = Lesson.objects.filter(
        teacher=request.user,
        scheduled_date__date=today,
        status__in=['scheduled', 'in_progress']
    ).count()

    upcoming_lessons_count = Lesson.objects.filter(
        teacher=request.user,
        scheduled_date__gt=timezone.now(),
        status='scheduled'
    ).count()

    # إحصائيات الطلاب - تم تعطيلها بعد حذف courses app
    total_students = 0  # تم إزالة التبعية على Enrollment

    # إحصائيات التقييم - استخدام تقييمات الحصص المباشرة والعادية
    from lessons.models import LessonRating, LiveLessonRating

    # حساب تقييمات الحصص العادية
    lesson_ratings = LessonRating.objects.filter(lesson__teacher=request.user)
    lesson_ratings_count = lesson_ratings.count()
    lesson_avg = lesson_ratings.aggregate(
        avg=Avg('overall_satisfaction')
    )['avg'] or 0

    # حساب تقييمات الحصص المباشرة
    live_ratings = LiveLessonRating.objects.filter(teacher=request.user)
    live_ratings_count = live_ratings.count()
    live_avg = live_ratings.aggregate(
        avg=Avg('overall_rating')
    )['avg'] or 0

    # حساب المتوسط العام
    total_ratings = lesson_ratings_count + live_ratings_count
    if total_ratings > 0:
        avg_rating = round(
            ((lesson_avg * lesson_ratings_count) + (live_avg * live_ratings_count)) / total_ratings, 1
        )
    else:
        avg_rating = 0.0

    # الحصص المكتملة
    completed_lessons_count = Lesson.objects.filter(
        teacher=request.user,
        status='completed'
    ).count()

    # الحصص المباشرة
    from lessons.models import LiveLesson
    live_lessons = LiveLesson.objects.filter(
        teacher=request.user,
        status='live'
    ).select_related('student').order_by('-started_at')

    # الحصص المجدولة (تشمل التي حان موعدها ولم تبدأ بعد)
    now = timezone.now()
    scheduled_live_lessons = LiveLesson.objects.filter(
        teacher=request.user,
        status='scheduled',
        scheduled_date__gte=now - timezone.timedelta(hours=2)  # تظهر الحصص حتى لو تأخرت ساعتين
    ).select_related('student').order_by('scheduled_date')[:3]

    # الحصص المجدولة من نظام الاشتراكات
    from subscriptions.models import ScheduledLesson
    subscription_scheduled_lessons = ScheduledLesson.objects.filter(
        teacher=request.user,  # الحصص التي يدرسها هذا المعلم
        status='scheduled',
        scheduled_date__gte=now - timezone.timedelta(hours=2)
    ).select_related('subscription__student', 'subscription__plan').order_by('scheduled_date')[:3]

    context = {
        'today_lessons_count': today_lessons_count,
        'upcoming_lessons_count': upcoming_lessons_count,
        'total_students': total_students,
        'avg_rating': avg_rating,
        'total_ratings': total_ratings,
        'completed_lessons_count': completed_lessons_count,
        'live_lessons': live_lessons,
        'scheduled_live_lessons': scheduled_live_lessons,
        'subscription_scheduled_lessons': subscription_scheduled_lessons,
        'today': timezone.now(),
        'tomorrow': timezone.now() + timezone.timedelta(days=1),
    }

    return render(request, 'dashboard/teacher.html', context)


@login_required
def teacher_students(request):
    """صفحة إدارة طلاب المعلم"""
    if not request.user.is_teacher():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    # تم إزالة التبعية على courses.models
    from lessons.models import Lesson
    from django.db.models import Count, Q
    from django.utils import timezone

    # تم تعطيل نظام التسجيلات بعد حذف courses app
    enrollments = []
    total_students = 0
    active_students = 0
    new_students = 0
    completed_students = 0

    # يمكن استبدال هذا بنظام جديد يعتمد على الحصص مباشرة
    # جلب الطلاب الذين لديهم حصص مع هذا المعلم
    students_with_lessons = User.objects.filter(
        user_type='student',
        lessons_as_student__teacher=request.user
    ).distinct()

    total_students = students_with_lessons.count()

    context = {
        'enrollments': enrollments,
        'total_students': total_students,
        'active_students': active_students,
        'new_students': new_students,
        'completed_students': completed_students,
        'students_with_lessons': students_with_lessons,
    }

    return render(request, 'teacher/students.html', context)














@login_required
def admin_ratings_view(request):
    """صفحة عرض التقييمات للمدير - النظام الموحد"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from lessons.models import LessonRating, LiveLessonRating, UnifiedLessonRating
    from django.db.models import Avg, Count, Q
    from django.contrib.auth import get_user_model

    User = get_user_model()

    # جلب إحصائيات النظام الموحد
    unified_ratings = UnifiedLessonRating.objects.all()
    unified_stats = {}

    if unified_ratings.exists():
        unified_stats = {
            'total_ratings': unified_ratings.count(),
            'avg_overall': unified_ratings.aggregate(avg=Avg('overall_rating'))['avg'] or 0,
            'avg_quality': unified_ratings.aggregate(avg=Avg('lesson_quality'))['avg'] or 0,
            'avg_interaction': unified_ratings.aggregate(avg=Avg('teacher_interaction'))['avg'] or 0,
            'avg_technical': unified_ratings.aggregate(avg=Avg('technical_quality'))['avg'] or 0,
            'scheduled_count': unified_ratings.filter(lesson_type='scheduled').count(),
            'live_count': unified_ratings.filter(lesson_type='live').count(),
        }
        unified_stats['overall_average'] = round(
            (unified_stats['avg_overall'] + unified_stats['avg_quality'] +
             unified_stats['avg_interaction'] + unified_stats['avg_technical']) / 4, 1
        )

    # جلب تقييمات المعلمين الموحدة
    teachers = User.objects.filter(user_type='teacher')
    unified_teacher_ratings = []

    for teacher in teachers:
        teacher_unified_ratings = unified_ratings.filter(teacher=teacher)
        if teacher_unified_ratings.exists():
            teacher_stats = {
                'teacher': teacher,
                'total_ratings': teacher_unified_ratings.count(),
                'avg_overall': teacher_unified_ratings.aggregate(avg=Avg('overall_rating'))['avg'] or 0,
                'avg_quality': teacher_unified_ratings.aggregate(avg=Avg('lesson_quality'))['avg'] or 0,
                'avg_interaction': teacher_unified_ratings.aggregate(avg=Avg('teacher_interaction'))['avg'] or 0,
                'avg_technical': teacher_unified_ratings.aggregate(avg=Avg('technical_quality'))['avg'] or 0,
                'scheduled_count': teacher_unified_ratings.filter(lesson_type='scheduled').count(),
                'live_count': teacher_unified_ratings.filter(lesson_type='live').count(),
            }
            teacher_stats['overall_average'] = round(
                (teacher_stats['avg_overall'] + teacher_stats['avg_quality'] +
                 teacher_stats['avg_interaction'] + teacher_stats['avg_technical']) / 4, 1
            )
            unified_teacher_ratings.append(teacher_stats)

    # ترتيب المعلمين حسب المتوسط العام
    unified_teacher_ratings.sort(key=lambda x: x['overall_average'], reverse=True)

    # جلب آخر التقييمات الموحدة
    recent_unified_ratings = unified_ratings.select_related(
        'teacher', 'student', 'scheduled_lesson', 'live_lesson'
    ).order_by('-created_at')[:10]

    # جلب آخر التقييمات للحصص العادية (النظام القديم)
    recent_lesson_ratings = LessonRating.objects.select_related(
        'lesson__teacher',
        'student'
    ).order_by('-created_at')[:10]

    # جلب آخر التقييمات للحصص المباشرة
    recent_live_ratings = LiveLessonRating.objects.select_related(
        'teacher',
        'student',
        'live_lesson'
    ).order_by('-created_at')[:10]

    # إحصائيات عامة للحصص العادية
    total_lesson_ratings = LessonRating.objects.count()
    average_lesson_rating = LessonRating.objects.aggregate(
        avg=Avg('overall_satisfaction')
    )['avg'] or 0

    # إحصائيات عامة للحصص المباشرة
    total_live_ratings = LiveLessonRating.objects.count()
    if total_live_ratings > 0:
        live_ratings_avg = LiveLessonRating.objects.aggregate(
            avg_overall=Avg('overall_rating'),
            avg_quality=Avg('lesson_quality'),
            avg_interaction=Avg('teacher_interaction'),
            avg_technical=Avg('technical_quality')
        )
        average_live_rating = (
            live_ratings_avg['avg_overall'] + live_ratings_avg['avg_quality'] +
            live_ratings_avg['avg_interaction'] + live_ratings_avg['avg_technical']
        ) / 4 if all(live_ratings_avg.values()) else 0
    else:
        average_live_rating = 0

    # إحصائيات شاملة
    total_ratings = total_lesson_ratings + total_live_ratings
    if total_ratings > 0:
        overall_average = (
            (average_lesson_rating * total_lesson_ratings) +
            (average_live_rating * total_live_ratings)
        ) / total_ratings
    else:
        overall_average = 0

    # أفضل المعلمين حسب تقييمات الحصص المباشرة
    top_live_teachers = User.objects.filter(
        user_type='teacher',
        live_lesson_ratings_received__isnull=False
    ).annotate(
        avg_live_rating=Avg('live_lesson_ratings_received__overall_rating'),
        live_ratings_count=Count('live_lesson_ratings_received')
    ).filter(
        live_ratings_count__gte=3
    ).order_by('-avg_live_rating')[:5]

    # حساب الإحصائيات للصفحة الرئيسية
    total_teacher_ratings = unified_ratings.count() if unified_ratings.exists() else 0

    # حساب تقييمات الطلاب (من تقارير المعلمين)
    from lessons.models import TeacherLessonReport
    total_student_ratings = TeacherLessonReport.objects.count()

    active_teachers_count = User.objects.filter(user_type='teacher', is_active=True).count()
    active_students_count = User.objects.filter(user_type='student', is_active=True).count()
    total_lesson_reports = TeacherLessonReport.objects.count()
    avg_teacher_rating = unified_stats.get('overall_average', 0) if unified_stats else 0
    total_unified_ratings = unified_ratings.count() if unified_ratings.exists() else 0

    context = {
        # إحصائيات الصفحة الرئيسية
        'total_teacher_ratings': total_teacher_ratings,
        'total_student_ratings': total_student_ratings,
        'active_teachers_count': active_teachers_count,
        'active_students_count': active_students_count,
        'total_lesson_reports': total_lesson_reports,
        'avg_teacher_rating': avg_teacher_rating,
        'total_unified_ratings': total_unified_ratings,
    }

    return render(request, 'admin/ratings_view.html', context)


@login_required
def admin_teacher_ratings(request):
    """صفحة تقييمات المعلمين المخصصة مع فلاتر زمنية وتقارير متقدمة"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from lessons.models import LessonRating, LiveLessonRating, UnifiedLessonRating
    from django.db.models import Avg, Count, Q, Case, When, IntegerField
    from django.contrib.auth import get_user_model
    from django.utils import timezone
    from datetime import timedelta
    import json

    User = get_user_model()

    # معالجة الفلاتر الزمنية
    time_filter = request.GET.get('time_filter', 'all')
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    teacher_filter = request.GET.get('teacher_filter', 'all')

    # تحديد الفترة الزمنية
    now = timezone.now()
    date_filter = Q()

    if time_filter == 'last_month':
        date_filter = Q(created_at__gte=now - timedelta(days=30))
    elif time_filter == 'last_3_months':
        date_filter = Q(created_at__gte=now - timedelta(days=90))
    elif time_filter == 'last_6_months':
        date_filter = Q(created_at__gte=now - timedelta(days=180))
    elif time_filter == 'current_year':
        date_filter = Q(created_at__year=now.year)
    elif time_filter == 'custom' and start_date and end_date:
        date_filter = Q(created_at__date__gte=start_date, created_at__date__lte=end_date)

    # جلب إحصائيات النظام الموحد مع الفلاتر
    unified_ratings = UnifiedLessonRating.objects.filter(date_filter)

    # فلتر المعلم المحدد
    if teacher_filter != 'all':
        unified_ratings = unified_ratings.filter(teacher_id=teacher_filter)

    unified_stats = {}
    if unified_ratings.exists():
        unified_stats = {
            'total_ratings': unified_ratings.count(),
            'avg_overall': unified_ratings.aggregate(avg=Avg('overall_rating'))['avg'] or 0,
            'avg_quality': unified_ratings.aggregate(avg=Avg('lesson_quality'))['avg'] or 0,
            'avg_interaction': unified_ratings.aggregate(avg=Avg('teacher_interaction'))['avg'] or 0,
            'avg_technical': unified_ratings.aggregate(avg=Avg('technical_quality'))['avg'] or 0,
            'scheduled_count': unified_ratings.filter(lesson_type='scheduled').count(),
            'live_count': unified_ratings.filter(lesson_type='live').count(),
        }
        unified_stats['overall_average'] = round(
            (unified_stats['avg_overall'] + unified_stats['avg_quality'] +
             unified_stats['avg_interaction'] + unified_stats['avg_technical']) / 4, 1
        )

    # جلب تقييمات المعلمين الموحدة مع تفاصيل إضافية
    teachers = User.objects.filter(user_type='teacher', is_active=True)
    unified_teacher_ratings = []

    for teacher in teachers:
        teacher_unified_ratings = unified_ratings.filter(teacher=teacher)
        if teacher_unified_ratings.exists():
            # حساب توزيع النجوم
            star_distribution = {}
            for i in range(1, 6):
                star_distribution[i] = teacher_unified_ratings.filter(overall_rating=i).count()

            # حساب اتجاه التقييمات (آخر 10 تقييمات)
            recent_ratings = teacher_unified_ratings.order_by('-created_at')[:10]
            trend_direction = 'stable'
            if recent_ratings.count() >= 5:
                first_half = recent_ratings[5:].aggregate(avg=Avg('overall_rating'))['avg'] or 0
                second_half = recent_ratings[:5].aggregate(avg=Avg('overall_rating'))['avg'] or 0
                if second_half > first_half + 0.3:
                    trend_direction = 'improving'
                elif second_half < first_half - 0.3:
                    trend_direction = 'declining'

            # حساب عدد التعليقات
            comments_count = teacher_unified_ratings.exclude(Q(comment='') | Q(comment__isnull=True)).count()

            # آخر تقييم
            last_rating = teacher_unified_ratings.order_by('-created_at').first()

            teacher_stats = {
                'teacher': teacher,
                'total_ratings': teacher_unified_ratings.count(),
                'avg_overall': teacher_unified_ratings.aggregate(avg=Avg('overall_rating'))['avg'] or 0,
                'avg_quality': teacher_unified_ratings.aggregate(avg=Avg('lesson_quality'))['avg'] or 0,
                'avg_interaction': teacher_unified_ratings.aggregate(avg=Avg('teacher_interaction'))['avg'] or 0,
                'avg_technical': teacher_unified_ratings.aggregate(avg=Avg('technical_quality'))['avg'] or 0,
                'scheduled_count': teacher_unified_ratings.filter(lesson_type='scheduled').count(),
                'live_count': teacher_unified_ratings.filter(lesson_type='live').count(),
                'star_distribution': star_distribution,
                'trend_direction': trend_direction,
                'comments_count': comments_count,
                'last_rating': last_rating,
                'performance_level': 'excellent' if 0 else 'good',  # سيتم حسابه لاحقاً
            }

            # حساب المتوسط العام
            teacher_stats['overall_average'] = round(
                (teacher_stats['avg_overall'] + teacher_stats['avg_quality'] +
                 teacher_stats['avg_interaction'] + teacher_stats['avg_technical']) / 4, 1
            )

            # تحديد مستوى الأداء
            avg = teacher_stats['overall_average']
            if avg >= 4.5:
                teacher_stats['performance_level'] = 'excellent'
            elif avg >= 4.0:
                teacher_stats['performance_level'] = 'very_good'
            elif avg >= 3.5:
                teacher_stats['performance_level'] = 'good'
            elif avg >= 3.0:
                teacher_stats['performance_level'] = 'fair'
            else:
                teacher_stats['performance_level'] = 'poor'

            unified_teacher_ratings.append(teacher_stats)

    # ترتيب المعلمين حسب المتوسط العام
    unified_teacher_ratings.sort(key=lambda x: x['overall_average'], reverse=True)

    # إحصائيات مقارنة
    comparison_stats = {
        'top_performer': unified_teacher_ratings[0] if unified_teacher_ratings else None,
        'average_rating': sum([t['overall_average'] for t in unified_teacher_ratings]) / len(unified_teacher_ratings) if unified_teacher_ratings else 0,
        'total_teachers': len(unified_teacher_ratings),
        'excellent_teachers': len([t for t in unified_teacher_ratings if t['performance_level'] == 'excellent']),
        'improving_teachers': len([t for t in unified_teacher_ratings if t['trend_direction'] == 'improving']),
    }

    # بيانات للرسوم البيانية
    chart_data = {
        'teacher_names': [t['teacher'].get_full_name() for t in unified_teacher_ratings[:10]],
        'teacher_averages': [t['overall_average'] for t in unified_teacher_ratings[:10]],
        'criteria_averages': {
            'overall': unified_stats.get('avg_overall', 0),
            'quality': unified_stats.get('avg_quality', 0),
            'interaction': unified_stats.get('avg_interaction', 0),
            'technical': unified_stats.get('avg_technical', 0),
        }
    }

    # قائمة المعلمين للفلتر
    all_teachers = User.objects.filter(user_type='teacher', is_active=True).order_by('first_name', 'last_name')

    context = {
        'unified_stats': unified_stats,
        'unified_teacher_ratings': unified_teacher_ratings,
        'comparison_stats': comparison_stats,
        'chart_data': json.dumps(chart_data),
        'time_filter': time_filter,
        'teacher_filter': teacher_filter,
        'start_date': start_date,
        'end_date': end_date,
        'all_teachers': all_teachers,
        'filter_applied': time_filter != 'all' or teacher_filter != 'all',
    }

    # معالجة طلبات الإجراءات
    action = request.GET.get('action')

    if action == 'teacher_details':
        # طلبات AJAX فقط
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            teacher_id = request.GET.get('teacher_id')
            return get_teacher_details_ajax(request, teacher_id, unified_ratings)



    elif action == 'generate_report':
        # إنشاء التقارير - طلب مباشر
        teacher_id = request.GET.get('teacher_id')
        if teacher_id:
            return generate_teacher_pdf_report(request, teacher_id, unified_ratings)
        else:
            return generate_all_teachers_pdf_report(request, unified_teacher_ratings)

    return render(request, 'admin/teacher_ratings.html', context)


def get_teacher_details_ajax(request, teacher_id, unified_ratings):
    """جلب تفاصيل المعلم عبر AJAX"""
    from django.contrib.auth import get_user_model
    from django.template.loader import render_to_string
    from django.db.models import Avg

    User = get_user_model()

    try:
        teacher = User.objects.get(id=teacher_id, user_type='teacher')
        teacher_ratings = unified_ratings.filter(teacher=teacher).order_by('-created_at')

        # إحصائيات تفصيلية
        stats = {
            'total_ratings': teacher_ratings.count(),
            'avg_overall': teacher_ratings.aggregate(avg=Avg('overall_rating'))['avg'] or 0,
            'avg_quality': teacher_ratings.aggregate(avg=Avg('lesson_quality'))['avg'] or 0,
            'avg_interaction': teacher_ratings.aggregate(avg=Avg('teacher_interaction'))['avg'] or 0,
            'avg_technical': teacher_ratings.aggregate(avg=Avg('technical_quality'))['avg'] or 0,
            'scheduled_count': teacher_ratings.filter(lesson_type='scheduled').count(),
            'live_count': teacher_ratings.filter(lesson_type='live').count(),
        }

        # آخر 10 تقييمات
        recent_ratings = teacher_ratings[:10]

        # توزيع النجوم
        star_distribution = {}
        for i in range(1, 6):
            star_distribution[i] = teacher_ratings.filter(overall_rating=i).count()

        context = {
            'teacher': teacher,
            'stats': stats,
            'recent_ratings': recent_ratings,
            'star_distribution': star_distribution,
        }

        html = render_to_string('admin/teacher_details_modal.html', context)

        return JsonResponse({
            'success': True,
            'html': html
        })

    except User.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'المعلم غير موجود'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        })


def export_teachers_data(request, teacher_ratings, time_filter):
    """تصدير بيانات المعلمين كـ Excel"""
    import io
    import xlsxwriter
    from django.http import HttpResponse

    try:
        # إنشاء ملف Excel في الذاكرة
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output)
        worksheet = workbook.add_worksheet('تقييمات المعلمين')

        # تنسيق الخلايا
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#2D5016',
            'font_color': 'white',
            'align': 'center',
            'valign': 'vcenter'
        })

        data_format = workbook.add_format({
            'align': 'center',
            'valign': 'vcenter'
        })

        # العناوين
        headers = [
            'اسم المعلم',
            'إجمالي التقييمات',
            'التقييم العام',
            'جودة الحصة',
            'تفاعل المعلم',
            'الجودة التقنية',
            'المتوسط العام',
            'الحصص المجدولة',
            'الحصص المباشرة',
            'مستوى الأداء',
            'اتجاه التقييمات'
        ]

        # كتابة العناوين
        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)

        # كتابة البيانات
        for row, teacher_rating in enumerate(teacher_ratings, 1):
            performance_level_ar = {
                'excellent': 'ممتاز',
                'very_good': 'جيد جداً',
                'good': 'جيد',
                'fair': 'مقبول',
                'poor': 'ضعيف'
            }.get(teacher_rating.get('performance_level', ''), 'غير محدد')

            trend_direction_ar = {
                'improving': 'متحسن',
                'declining': 'متراجع',
                'stable': 'مستقر'
            }.get(teacher_rating.get('trend_direction', ''), 'غير محدد')

            data = [
                teacher_rating['teacher'].get_full_name(),
                teacher_rating.get('total_ratings', 0),
                round(teacher_rating.get('avg_overall', 0), 2),
                round(teacher_rating.get('avg_quality', 0), 2),
                round(teacher_rating.get('avg_interaction', 0), 2),
                round(teacher_rating.get('avg_technical', 0), 2),
                round(teacher_rating.get('overall_average', 0), 2),
                teacher_rating.get('scheduled_count', 0),
                teacher_rating.get('live_count', 0),
                performance_level_ar,
                trend_direction_ar
            ]

            for col, value in enumerate(data):
                worksheet.write(row, col, value, data_format)

        # ضبط عرض الأعمدة
        worksheet.set_column(0, 0, 20)  # اسم المعلم
        worksheet.set_column(1, 10, 15)  # باقي الأعمدة

        workbook.close()
        output.seek(0)

        # إنشاء الاستجابة
        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

        filename = f'teacher_ratings_{time_filter}.xlsx'
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في تصدير البيانات: {str(e)}'
        })


def generate_teacher_pdf_report(request, teacher_id, unified_ratings):
    """إنشاء تقرير PDF لمعلم واحد"""
    from django.contrib.auth import get_user_model
    from django.template.loader import render_to_string
    from django.http import HttpResponse
    from django.db.models import Avg

    User = get_user_model()

    try:
        teacher = User.objects.get(id=teacher_id, user_type='teacher')
        teacher_ratings = unified_ratings.filter(teacher=teacher)

        # إحصائيات المعلم
        stats = {
            'total_ratings': teacher_ratings.count(),
            'avg_overall': teacher_ratings.aggregate(avg=Avg('overall_rating'))['avg'] or 0,
            'avg_quality': teacher_ratings.aggregate(avg=Avg('lesson_quality'))['avg'] or 0,
            'avg_interaction': teacher_ratings.aggregate(avg=Avg('teacher_interaction'))['avg'] or 0,
            'avg_technical': teacher_ratings.aggregate(avg=Avg('technical_quality'))['avg'] or 0,
            'scheduled_count': teacher_ratings.filter(lesson_type='scheduled').count(),
            'live_count': teacher_ratings.filter(lesson_type='live').count(),
        }

        stats['overall_average'] = round(
            (stats['avg_overall'] + stats['avg_quality'] +
             stats['avg_interaction'] + stats['avg_technical']) / 4, 1
        )

        # توزيع النجوم
        star_distribution = {}
        for i in range(1, 6):
            star_distribution[i] = teacher_ratings.filter(overall_rating=i).count()

        # آخر التقييمات
        recent_ratings = teacher_ratings.order_by('-created_at')[:20]

        context = {
            'teacher': teacher,
            'stats': stats,
            'star_distribution': star_distribution,
            'recent_ratings': recent_ratings,
            'report_date': timezone.now(),
        }

        # إنشاء PDF باستخدام reportlab مع دعم العربية
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch, cm
            from reportlab.lib import colors
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
            import io

            # إنشاء buffer للـ PDF
            buffer = io.BytesIO()

            # إنشاء المستند مع هوامش محسنة
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm,
                title=f"تقرير تقييمات المعلم - {teacher.get_full_name()}"
            )

            # الحصول على الأنماط
            styles = getSampleStyleSheet()

            # إنشاء أنماط مخصصة محسنة
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=20,
                spaceAfter=20,
                spaceBefore=10,
                alignment=TA_CENTER,
                textColor=colors.HexColor('#2D5016'),
                fontName='Helvetica-Bold'
            )

            subtitle_style = ParagraphStyle(
                'CustomSubtitle',
                parent=styles['Normal'],
                fontSize=12,
                spaceAfter=30,
                alignment=TA_CENTER,
                textColor=colors.HexColor('#666666'),
                fontName='Helvetica'
            )

            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=16,
                spaceAfter=15,
                spaceBefore=20,
                textColor=colors.HexColor('#2D5016'),
                fontName='Helvetica-Bold',
                borderWidth=1,
                borderColor=colors.HexColor('#2D5016'),
                borderPadding=5,
                backColor=colors.HexColor('#f8f9fa')
            )

            normal_style = ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontSize=11,
                spaceAfter=8,
                fontName='Helvetica',
                textColor=colors.black
            )

            # محتوى المستند
            story = []

            # رأس التقرير
            story.append(Paragraph("أكاديمية القرآنية", title_style))
            story.append(Paragraph("تقرير تقييمات المعلم", subtitle_style))
            story.append(Paragraph(f"تاريخ التقرير: {timezone.now().strftime('%Y-%m-%d %H:%M')}", subtitle_style))
            story.append(Spacer(1, 20))

            # معلومات المعلم في صندوق مميز
            story.append(Paragraph("معلومات المعلم", heading_style))
            teacher_info = [
                ['الاسم الكامل', teacher.get_full_name()],
                ['البريد الإلكتروني', teacher.email],
                ['تاريخ الانضمام', teacher.date_joined.strftime('%d/%m/%Y')],
                ['حالة الحساب', 'نشط' if teacher.is_active else 'غير نشط'],
                ['إجمالي التقييمات', str(stats['total_ratings'])],
                ['المتوسط العام', f"{stats['overall_average']:.1f}/5"]
            ]

            teacher_table = Table(teacher_info, colWidths=[4*cm, 8*cm])
            teacher_table.setStyle(TableStyle([
                # تنسيق الرأس
                ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#2D5016')),
                ('TEXTCOLOR', (0, 0), (0, -1), colors.white),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (0, -1), 12),

                # تنسيق البيانات
                ('BACKGROUND', (1, 0), (1, -1), colors.HexColor('#f8f9fa')),
                ('TEXTCOLOR', (1, 0), (1, -1), colors.black),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (1, 0), (1, -1), 11),

                # تنسيق عام
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#ddd')),
                ('ROWBACKGROUNDS', (0, 0), (-1, -1), [colors.white, colors.HexColor('#f8f9fa')]),
                ('TOPPADDING', (0, 0), (-1, -1), 8),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                ('LEFTPADDING', (0, 0), (-1, -1), 10),
                ('RIGHTPADDING', (0, 0), (-1, -1), 10),
            ]))

            story.append(teacher_table)
            story.append(Spacer(1, 25))

            # تفصيل المعايير
            story.append(Paragraph("تفصيل المعايير", heading_style))
            criteria_data = [
                ['المعيار', 'التقييم', 'النسبة المئوية'],
                ['التقييم العام', f"{stats['avg_overall']:.1f}/5", f"{(stats['avg_overall']/5)*100:.0f}%"],
                ['جودة الحصة', f"{stats['avg_quality']:.1f}/5", f"{(stats['avg_quality']/5)*100:.0f}%"],
                ['تفاعل المعلم', f"{stats['avg_interaction']:.1f}/5", f"{(stats['avg_interaction']/5)*100:.0f}%"],
                ['الجودة التقنية', f"{stats['avg_technical']:.1f}/5", f"{(stats['avg_technical']/5)*100:.0f}%"]
            ]

            criteria_table = Table(criteria_data, colWidths=[5*cm, 3*cm, 3*cm])
            criteria_table.setStyle(TableStyle([
                # تنسيق الرأس
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2D5016')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('ALIGN', (0, 0), (-1, 0), 'CENTER'),

                # تنسيق البيانات
                ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#f8f9fa')),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 11),
                ('ALIGN', (0, 1), (0, -1), 'RIGHT'),  # المعيار
                ('ALIGN', (1, 1), (-1, -1), 'CENTER'),  # التقييم والنسبة

                # تنسيق عام
                ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#ddd')),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('TOPPADDING', (0, 0), (-1, -1), 8),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                ('LEFTPADDING', (0, 0), (-1, -1), 10),
                ('RIGHTPADDING', (0, 0), (-1, -1), 10),
            ]))

            story.append(criteria_table)
            story.append(Spacer(1, 25))

            # توزيع النجوم
            story.append(Paragraph("توزيع التقييمات", heading_style))
            star_data = [['عدد النجوم', 'عدد التقييمات', 'النسبة المئوية']]
            total_ratings = sum(star_distribution.values()) if star_distribution else 1

            for star in [5, 4, 3, 2, 1]:  # ترتيب تنازلي
                count = star_distribution.get(star, 0)
                percentage = (count / total_ratings) * 100 if total_ratings > 0 else 0
                star_data.append([
                    f"⭐ {star} نجوم",
                    str(count),
                    f"{percentage:.1f}%"
                ])

            star_table = Table(star_data, colWidths=[4*cm, 3*cm, 3*cm])
            star_table.setStyle(TableStyle([
                # تنسيق الرأس
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2D5016')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('ALIGN', (0, 0), (-1, 0), 'CENTER'),

                # تنسيق البيانات
                ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#f8f9fa')),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 11),
                ('ALIGN', (0, 1), (0, -1), 'RIGHT'),  # النجوم
                ('ALIGN', (1, 1), (-1, -1), 'CENTER'),  # العدد والنسبة

                # تنسيق عام
                ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#ddd')),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('TOPPADDING', (0, 0), (-1, -1), 8),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                ('LEFTPADDING', (0, 0), (-1, -1), 10),
                ('RIGHTPADDING', (0, 0), (-1, -1), 10),
            ]))

            story.append(star_table)
            story.append(Spacer(1, 25))

            # آخر التقييمات
            if recent_ratings:
                story.append(Paragraph("آخر التقييمات (أحدث 10 تقييمات)", heading_style))
                ratings_data = [['الطالب', 'التقييم العام', 'جودة الحصة', 'التاريخ', 'نوع الحصة', 'التعليق']]

                for rating in recent_ratings[:10]:  # أول 10 فقط
                    comment = rating.comment[:30] + '...' if rating.comment and len(rating.comment) > 30 else (rating.comment or 'لا يوجد')
                    ratings_data.append([
                        rating.student.get_full_name(),
                        f"⭐ {rating.overall_rating}/5",
                        f"⭐ {rating.lesson_quality}/5",
                        rating.created_at.strftime('%d/%m/%Y'),
                        'مجدولة' if rating.lesson_type == 'scheduled' else 'مباشرة',
                        comment
                    ])

                ratings_table = Table(ratings_data, colWidths=[3*cm, 2*cm, 2*cm, 2*cm, 2*cm, 4*cm])
                ratings_table.setStyle(TableStyle([
                    # تنسيق الرأس
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2D5016')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('ALIGN', (0, 0), (-1, 0), 'CENTER'),

                    # تنسيق البيانات
                    ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#f8f9fa')),
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 1), (-1, -1), 9),
                    ('ALIGN', (0, 1), (0, -1), 'RIGHT'),  # اسم الطالب
                    ('ALIGN', (1, 1), (4, -1), 'CENTER'),  # التقييمات والتاريخ والنوع
                    ('ALIGN', (5, 1), (5, -1), 'RIGHT'),  # التعليق

                    # تنسيق عام
                    ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#ddd')),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('TOPPADDING', (0, 0), (-1, -1), 6),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                    ('LEFTPADDING', (0, 0), (-1, -1), 8),
                    ('RIGHTPADDING', (0, 0), (-1, -1), 8),

                    # تلوين متدرج للصفوف
                    ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f8f9fa')]),
                ]))

                story.append(ratings_table)
                story.append(Spacer(1, 20))
            else:
                story.append(Paragraph("لا توجد تقييمات حديثة", heading_style))
                story.append(Spacer(1, 20))

            # إضافة تذييل التقرير
            story.append(Spacer(1, 30))
            footer_style = ParagraphStyle(
                'Footer',
                parent=styles['Normal'],
                fontSize=10,
                alignment=TA_CENTER,
                textColor=colors.HexColor('#666666'),
                borderWidth=1,
                borderColor=colors.HexColor('#ddd'),
                borderPadding=10,
                backColor=colors.HexColor('#f8f9fa')
            )

            footer_text = f"""
            <b>أكاديمية القرآنية</b><br/>
            تقرير تم إنشاؤه تلقائياً في {timezone.now().strftime('%d/%m/%Y الساعة %H:%M')}<br/>
            هذا التقرير يحتوي على معلومات سرية ومخصص للاستخدام الداخلي فقط
            """

            story.append(Paragraph(footer_text, footer_style))

            # بناء المستند
            doc.build(story)

            # إرجاع PDF
            pdf = buffer.getvalue()
            buffer.close()

            response = HttpResponse(pdf, content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="teacher_report_{teacher.get_full_name()}_{timezone.now().strftime("%Y%m%d")}.pdf"'
            return response

        except Exception as pdf_error:
            # في حالة فشل PDF، إرجاع HTML
            html_content = render_to_string('admin/teacher_pdf_report.html', context)
            response = HttpResponse(html_content, content_type='text/html')
            response['Content-Disposition'] = f'attachment; filename="teacher_report_{teacher.get_full_name()}_{timezone.now().strftime("%Y%m%d")}.html"'
            return response

    except User.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'المعلم غير موجود'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في إنشاء التقرير: {str(e)}'
        })


def generate_all_teachers_pdf_report(request, teacher_ratings):
    """إنشاء تقرير PDF شامل لجميع المعلمين"""
    from django.template.loader import render_to_string
    from django.http import HttpResponse

    try:
        total_teachers = len(teacher_ratings)

        context = {
            'teacher_ratings': teacher_ratings,
            'report_date': timezone.now(),
            'total_teachers': total_teachers,
        }

        # إنشاء PDF باستخدام reportlab مع تحسينات
        try:
            from reportlab.lib.pagesizes import A4, landscape
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch, cm
            from reportlab.lib import colors
            from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
            import io

            # إنشاء buffer للـ PDF
            buffer = io.BytesIO()

            # إنشاء المستند بتوجه أفقي للجدول الكبير مع هوامش محسنة
            doc = SimpleDocTemplate(
                buffer,
                pagesize=landscape(A4),
                rightMargin=1.5*cm,
                leftMargin=1.5*cm,
                topMargin=2*cm,
                bottomMargin=2*cm,
                title="تقرير شامل - تقييمات جميع المعلمين"
            )

            # الحصول على الأنماط
            styles = getSampleStyleSheet()

            # إنشاء أنماط مخصصة محسنة
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=20,
                spaceBefore=10,
                alignment=TA_CENTER,
                textColor=colors.HexColor('#2D5016'),
                fontName='Helvetica-Bold'
            )

            subtitle_style = ParagraphStyle(
                'CustomSubtitle',
                parent=styles['Normal'],
                fontSize=12,
                spaceAfter=25,
                alignment=TA_CENTER,
                textColor=colors.HexColor('#666666'),
                fontName='Helvetica'
            )

            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12,
                spaceBefore=15,
                textColor=colors.HexColor('#2D5016'),
                fontName='Helvetica-Bold',
                borderWidth=1,
                borderColor=colors.HexColor('#2D5016'),
                borderPadding=5,
                backColor=colors.HexColor('#f8f9fa')
            )

            # محتوى المستند
            story = []

            # رأس التقرير
            story.append(Paragraph("أكاديمية القرآنية", title_style))
            story.append(Paragraph("تقرير شامل - تقييمات جميع المعلمين", subtitle_style))
            story.append(Paragraph(f"تاريخ التقرير: {timezone.now().strftime('%Y-%m-%d %H:%M')}", subtitle_style))
            story.append(Spacer(1, 20))

            # ملخص الإحصائيات
            story.append(Paragraph("ملخص عام", heading_style))

            # حساب إحصائيات إضافية
            total_ratings = sum(tr.get('total_ratings', 0) for tr in teacher_ratings)
            avg_overall = sum(tr.get('overall_average', 0) for tr in teacher_ratings) / len(teacher_ratings) if teacher_ratings else 0

            summary_info = [
                ['إجمالي المعلمين', str(total_teachers)],
                ['معلمين لديهم تقييمات', str(len(teacher_ratings))],
                ['إجمالي التقييمات', str(total_ratings)],
                ['متوسط التقييمات العام', f"{avg_overall:.1f}/5"],
                ['تاريخ التقرير', timezone.now().strftime('%d/%m/%Y %H:%M')]
            ]

            summary_table = Table(summary_info, colWidths=[5*cm, 4*cm])
            summary_table.setStyle(TableStyle([
                # تنسيق الرأس
                ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#2D5016')),
                ('TEXTCOLOR', (0, 0), (0, -1), colors.white),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (0, -1), 12),

                # تنسيق البيانات
                ('BACKGROUND', (1, 0), (1, -1), colors.HexColor('#f8f9fa')),
                ('TEXTCOLOR', (1, 0), (1, -1), colors.black),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (1, 0), (1, -1), 11),

                # تنسيق عام
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#ddd')),
                ('TOPPADDING', (0, 0), (-1, -1), 8),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                ('LEFTPADDING', (0, 0), (-1, -1), 10),
                ('RIGHTPADDING', (0, 0), (-1, -1), 10),
            ]))

            story.append(summary_table)
            story.append(Spacer(1, 25))

            # جدول المعلمين
            if teacher_ratings:
                story.append(Paragraph("تفاصيل المعلمين", heading_style))

                # إعداد بيانات الجدول
                table_data = [
                    ['اسم المعلم', 'التقييمات', 'التقييم العام', 'جودة الحصة', 'تفاعل المعلم', 'الجودة التقنية', 'المتوسط العام', 'حصص مجدولة', 'حصص مباشرة', 'مستوى الأداء']
                ]

                for teacher_rating in teacher_ratings:
                    performance_level_ar = {
                        'excellent': 'ممتاز',
                        'very_good': 'جيد جداً',
                        'good': 'جيد',
                        'fair': 'مقبول',
                        'poor': 'ضعيف'
                    }.get(teacher_rating.get('performance_level', ''), 'غير محدد')

                    table_data.append([
                        teacher_rating['teacher'].get_full_name(),
                        str(teacher_rating.get('total_ratings', 0)),
                        f"{teacher_rating.get('avg_overall', 0):.1f}",
                        f"{teacher_rating.get('avg_quality', 0):.1f}",
                        f"{teacher_rating.get('avg_interaction', 0):.1f}",
                        f"{teacher_rating.get('avg_technical', 0):.1f}",
                        f"{teacher_rating.get('overall_average', 0):.1f}",
                        str(teacher_rating.get('scheduled_count', 0)),
                        str(teacher_rating.get('live_count', 0)),
                        performance_level_ar
                    ])

                # إنشاء الجدول مع أعمدة محسنة
                teachers_table = Table(table_data, colWidths=[
                    3*cm,    # اسم المعلم
                    1.5*cm,  # التقييمات
                    1.5*cm,  # التقييم العام
                    1.5*cm,  # جودة الحصة
                    1.5*cm,  # تفاعل المعلم
                    1.5*cm,  # الجودة التقنية
                    1.5*cm,  # المتوسط العام
                    1.5*cm,  # حصص مجدولة
                    1.5*cm,  # حصص مباشرة
                    2*cm     # مستوى الأداء
                ])

                teachers_table.setStyle(TableStyle([
                    # تنسيق الرأس
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2D5016')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 9),
                    ('ALIGN', (0, 0), (-1, 0), 'CENTER'),

                    # تنسيق البيانات
                    ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#f8f9fa')),
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 1), (-1, -1), 8),
                    ('ALIGN', (0, 1), (0, -1), 'RIGHT'),  # اسم المعلم
                    ('ALIGN', (1, 1), (-1, -1), 'CENTER'),  # باقي الأعمدة

                    # تنسيق عام
                    ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#ddd')),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('TOPPADDING', (0, 0), (-1, -1), 6),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                    ('LEFTPADDING', (0, 0), (-1, -1), 5),
                    ('RIGHTPADDING', (0, 0), (-1, -1), 5),

                    # تلوين متدرج للصفوف
                    ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f8f9fa')]),
                ]))

                story.append(teachers_table)
            else:
                story.append(Paragraph("لا توجد تقييمات للمعلمين حتى الآن", heading_style))

            # إضافة تذييل التقرير
            story.append(Spacer(1, 30))
            footer_style = ParagraphStyle(
                'Footer',
                parent=styles['Normal'],
                fontSize=10,
                alignment=TA_CENTER,
                textColor=colors.HexColor('#666666'),
                borderWidth=1,
                borderColor=colors.HexColor('#ddd'),
                borderPadding=10,
                backColor=colors.HexColor('#f8f9fa')
            )

            footer_text = f"""
            <b>أكاديمية القرآنية</b><br/>
            تقرير شامل تم إنشاؤه تلقائياً في {timezone.now().strftime('%d/%m/%Y الساعة %H:%M')}<br/>
            هذا التقرير يحتوي على معلومات سرية ومخصص للاستخدام الداخلي فقط<br/>
            عدد المعلمين المشمولين: {len(teacher_ratings)} معلم
            """

            story.append(Paragraph(footer_text, footer_style))

            # بناء المستند
            doc.build(story)

            # إرجاع PDF
            pdf = buffer.getvalue()
            buffer.close()

            response = HttpResponse(pdf, content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="all_teachers_report_{timezone.now().strftime("%Y%m%d")}.pdf"'
            return response

        except Exception as pdf_error:
            # في حالة فشل PDF، إرجاع HTML
            html_content = render_to_string('admin/all_teachers_pdf_report.html', context)
            response = HttpResponse(html_content, content_type='text/html')
            response['Content-Disposition'] = f'attachment; filename="all_teachers_report_{timezone.now().strftime("%Y%m%d")}.html"'
            return response

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في إنشاء التقرير: {str(e)}'
        })


@login_required
def admin_student_ratings(request):
    """صفحة تقييمات الطلاب المخصصة مع فلاتر زمنية وتقارير متقدمة"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from lessons.models import LessonRating, LiveLessonRating, UnifiedLessonRating
    from django.db.models import Avg, Count, Q, Case, When, IntegerField, Value, CharField
    from django.contrib.auth import get_user_model
    from django.utils import timezone
    from datetime import timedelta
    import json

    User = get_user_model()

    # معالجة الفلاتر الزمنية
    time_filter = request.GET.get('time_filter', 'all')
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    student_filter = request.GET.get('student_filter', 'all')

    # تحديد الفترة الزمنية
    now = timezone.now()
    date_filter = Q()

    if time_filter == 'last_month':
        date_filter = Q(created_at__gte=now - timedelta(days=30))
    elif time_filter == 'last_3_months':
        date_filter = Q(created_at__gte=now - timedelta(days=90))
    elif time_filter == 'last_6_months':
        date_filter = Q(created_at__gte=now - timedelta(days=180))
    elif time_filter == 'current_year':
        date_filter = Q(created_at__year=now.year)
    elif time_filter == 'custom' and start_date and end_date:
        date_filter = Q(created_at__date__gte=start_date, created_at__date__lte=end_date)

    # جلب إحصائيات النظام الموحد مع الفلاتر
    unified_ratings = UnifiedLessonRating.objects.filter(date_filter)

    # فلتر الطالب المحدد
    if student_filter != 'all':
        unified_ratings = unified_ratings.filter(student_id=student_filter)

    unified_stats = {}
    if unified_ratings.exists():
        unified_stats = {
            'total_ratings': unified_ratings.count(),
            'avg_overall': unified_ratings.aggregate(avg=Avg('overall_rating'))['avg'] or 0,
            'avg_quality': unified_ratings.aggregate(avg=Avg('lesson_quality'))['avg'] or 0,
            'avg_interaction': unified_ratings.aggregate(avg=Avg('teacher_interaction'))['avg'] or 0,
            'avg_technical': unified_ratings.aggregate(avg=Avg('technical_quality'))['avg'] or 0,
            'scheduled_count': unified_ratings.filter(lesson_type='scheduled').count(),
            'live_count': unified_ratings.filter(lesson_type='live').count(),
        }
        unified_stats['overall_average'] = round(
            (unified_stats['avg_overall'] + unified_stats['avg_quality'] +
             unified_stats['avg_interaction'] + unified_stats['avg_technical']) / 4, 1
        )

    # حساب تقييمات الطلاب مجمعة
    student_ratings_data = unified_ratings.values(
        'student__id',
        'student__first_name',
        'student__last_name',
        'student__email'
    ).annotate(
        total_ratings=Count('id'),
        overall_average=Avg('overall_rating'),
        lesson_quality_avg=Avg('lesson_quality'),
        teacher_interaction_avg=Avg('teacher_interaction'),
        technical_quality_avg=Avg('technical_quality'),
        # تصنيف الأداء
        performance_level=Case(
            When(overall_rating__gte=4.5, then=Value('excellent')),
            When(overall_rating__gte=4.0, then=Value('very-good')),
            When(overall_rating__gte=3.5, then=Value('good')),
            When(overall_rating__gte=3.0, then=Value('average')),
            default=Value('needs-improvement'),
            output_field=CharField()
        )
    ).order_by('-overall_average')

    # تحويل إلى قائمة وإضافة معلومات إضافية
    unified_student_ratings = []
    for student_data in student_ratings_data:
        student = User.objects.get(id=student_data['student__id'])
        student_unified_ratings = unified_ratings.filter(student=student)

        # حساب توزيع النجوم
        star_distribution = {}
        for i in range(1, 6):
            star_distribution[i] = student_unified_ratings.filter(overall_rating=i).count()

        # حساب اتجاه التقييمات (آخر 10 تقييمات)
        recent_ratings = student_unified_ratings.order_by('-created_at')[:10]
        trend_direction = 'stable'
        if recent_ratings.count() >= 5:
            first_half = recent_ratings[5:].aggregate(avg=Avg('overall_rating'))['avg'] or 0
            second_half = recent_ratings[:5].aggregate(avg=Avg('overall_rating'))['avg'] or 0
            if second_half > first_half + 0.3:
                trend_direction = 'improving'
            elif second_half < first_half - 0.3:
                trend_direction = 'declining'

        # حساب عدد التعليقات
        comments_count = student_unified_ratings.exclude(Q(comment='') | Q(comment__isnull=True)).count()

        # آخر تقييم
        last_rating = student_unified_ratings.order_by('-created_at').first()

        student_rating_info = {
            'student': student,
            'total_ratings': student_data['total_ratings'],
            'avg_overall': round(student_data['overall_average'] or 0, 2),
            'avg_quality': round(student_data['lesson_quality_avg'] or 0, 2),
            'avg_interaction': round(student_data['teacher_interaction_avg'] or 0, 2),
            'avg_technical': round(student_data['technical_quality_avg'] or 0, 2),
            'overall_average': round(student_data['overall_average'] or 0, 2),
            'scheduled_count': student_unified_ratings.filter(lesson_type='scheduled').count(),
            'live_count': student_unified_ratings.filter(lesson_type='live').count(),
            'star_distribution': star_distribution,
            'trend_direction': trend_direction,
            'comments_count': comments_count,
            'last_rating': last_rating,
            'performance_level': student_data['performance_level'],
            'performance_text': {
                'excellent': 'ممتاز',
                'very-good': 'جيد جداً',
                'good': 'جيد',
                'average': 'متوسط',
                'needs-improvement': 'يحتاج تحسين'
            }.get(student_data['performance_level'], 'غير محدد')
        }
        unified_student_ratings.append(student_rating_info)

    # إضافة المتغيرات المفقودة
    performance_filter = request.GET.get('performance_filter', 'all')
    search_query = request.GET.get('search', '').strip()

    # فلترة حسب مستوى الأداء
    if performance_filter != 'all':
        unified_student_ratings = [
            rating for rating in unified_student_ratings
            if rating['performance_level'] == performance_filter
        ]

    # ترتيب حسب المتوسط العام
    unified_student_ratings.sort(key=lambda x: x['overall_average'], reverse=True)

    # جلب آخر التقييمات الموحدة
    recent_unified_ratings = unified_ratings.select_related(
        'student', 'teacher', 'scheduled_lesson', 'live_lesson'
    ).order_by('-created_at')[:10]

    # إحصائيات شاملة
    total_student_ratings = unified_stats.get('total_ratings', 0)
    active_students_count = User.objects.filter(user_type='student', is_active=True).count()
    total_ratings = total_student_ratings
    overall_average = unified_stats.get('avg_overall', 0)
    filter_title = "جميع الفترات"

    # أفضل الطلاب حسب التقييمات
    top_students = unified_student_ratings[:10]

    # إحصائيات المقارنة
    comparison_stats = {}
    if unified_student_ratings:
        comparison_stats = {
            'total_students': len(unified_student_ratings),
            'excellent_students': len([s for s in unified_student_ratings if s['performance_level'] == 'excellent']),
            'improving_students': len([s for s in unified_student_ratings if s['trend_direction'] == 'improving']),
            'average_rating': sum(s['overall_average'] for s in unified_student_ratings) / len(unified_student_ratings),
            'top_performer': unified_student_ratings[0] if unified_student_ratings else None
        }

    # بيانات للرسوم البيانية
    chart_data = {
        'student_names': [s['student'].get_full_name()[:15] for s in unified_student_ratings[:10]],
        'student_averages': [s['overall_average'] for s in unified_student_ratings[:10]],
        'criteria_labels': ['التقييم العام', 'جودة الحصة', 'تفاعل المعلم', 'الجودة التقنية'],
        'criteria_values': [
            unified_stats.get('avg_overall', 0),
            unified_stats.get('avg_quality', 0),
            unified_stats.get('avg_interaction', 0),
            unified_stats.get('avg_technical', 0)
        ]
    }

    # معالجة طلبات الإجراءات
    action = request.GET.get('action')

    if action == 'student_details':
        # طلبات AJAX فقط
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            student_id = request.GET.get('student_id')
            return get_student_details_ajax(request, student_id, unified_ratings)



    elif action == 'generate_report':
        # إنشاء التقارير - طلب مباشر
        student_id = request.GET.get('student_id')
        if student_id:
            return generate_student_pdf_report(request, student_id, unified_ratings)
        else:
            return generate_all_students_pdf_report(request, unified_student_ratings)

    context = {
        # إحصائيات أساسية
        'unified_stats': unified_stats,
        'comparison_stats': comparison_stats,
        'total_student_ratings': total_student_ratings,
        'active_students_count': active_students_count,

        # بيانات الطلاب
        'unified_student_ratings': unified_student_ratings,
        'recent_unified_ratings': recent_unified_ratings,
        'top_students': top_students,
        'all_students': User.objects.filter(user_type='student', is_active=True).order_by('first_name', 'last_name'),

        # فلاتر
        'time_filter': time_filter,
        'student_filter': student_filter,
        'start_date': start_date,
        'end_date': end_date,
        'filter_applied': (time_filter != 'all' or student_filter != 'all' or
                          (time_filter == 'custom' and start_date and end_date)),

        # رسوم بيانية
        'chart_data': json.dumps(chart_data),
    }

    return render(request, 'admin/student_ratings.html', context)


def get_student_details_ajax(request, student_id, unified_ratings):
    """جلب تفاصيل الطالب عبر AJAX"""
    from django.contrib.auth import get_user_model
    from django.template.loader import render_to_string
    from django.db.models import Avg

    User = get_user_model()

    try:
        student = User.objects.get(id=student_id, user_type='student')
        student_ratings = unified_ratings.filter(student=student)

        # حساب الإحصائيات
        stats = {
            'total_ratings': student_ratings.count(),
            'avg_overall': student_ratings.aggregate(avg=Avg('overall_rating'))['avg'] or 0,
            'avg_quality': student_ratings.aggregate(avg=Avg('lesson_quality'))['avg'] or 0,
            'avg_interaction': student_ratings.aggregate(avg=Avg('teacher_interaction'))['avg'] or 0,
            'avg_technical': student_ratings.aggregate(avg=Avg('technical_quality'))['avg'] or 0,
            'scheduled_count': student_ratings.filter(lesson_type='scheduled').count(),
            'live_count': student_ratings.filter(lesson_type='live').count(),
        }
        stats['overall_average'] = round(
            (stats['avg_overall'] + stats['avg_quality'] +
             stats['avg_interaction'] + stats['avg_technical']) / 4, 1
        )

        # آخر 10 تقييمات
        recent_ratings = student_ratings[:10]

        # توزيع النجوم
        star_distribution = {}
        for i in range(1, 6):
            star_distribution[i] = student_ratings.filter(overall_rating=i).count()

        context = {
            'student': student,
            'stats': stats,
            'recent_ratings': recent_ratings,
            'star_distribution': star_distribution,
        }

        html = render_to_string('admin/student_details_modal.html', context)

        return JsonResponse({
            'success': True,
            'html': html
        })

    except User.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'الطالب غير موجود'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        })


def export_students_data(request, student_ratings, time_filter):
    """تصدير بيانات الطلاب كـ Excel"""
    import io
    import xlsxwriter
    from django.http import HttpResponse
    from django.utils import timezone

    try:
        # إنشاء ملف Excel في الذاكرة
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        worksheet = workbook.add_worksheet('تقييمات الطلاب')

        # تنسيقات الخلايا
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#2D5016',
            'font_color': 'white',
            'align': 'center',
            'valign': 'vcenter',
            'border': 1
        })

        cell_format = workbook.add_format({
            'align': 'center',
            'valign': 'vcenter',
            'border': 1
        })

        # عناوين الأعمدة
        headers = [
            'اسم الطالب',
            'البريد الإلكتروني',
            'إجمالي التقييمات',
            'المتوسط العام',
            'جودة الحصة',
            'تفاعل الطالب',
            'الجودة التقنية',
            'الحصص المجدولة',
            'الحصص المباشرة',
            'مستوى الأداء',
            'آخر تقييم'
        ]

        # كتابة العناوين
        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)

        # كتابة البيانات
        for row, student_rating in enumerate(student_ratings, 1):
            worksheet.write(row, 0, student_rating['student'].get_full_name(), cell_format)
            worksheet.write(row, 1, student_rating['student'].email, cell_format)
            worksheet.write(row, 2, student_rating['total_ratings'], cell_format)
            worksheet.write(row, 3, round(student_rating['overall_average'], 2), cell_format)
            worksheet.write(row, 4, round(student_rating['avg_quality'], 2), cell_format)
            worksheet.write(row, 5, round(student_rating['avg_interaction'], 2), cell_format)
            worksheet.write(row, 6, round(student_rating['avg_technical'], 2), cell_format)
            worksheet.write(row, 7, student_rating['scheduled_count'], cell_format)
            worksheet.write(row, 8, student_rating['live_count'], cell_format)
            worksheet.write(row, 9, student_rating['performance_text'], cell_format)
            worksheet.write(row, 10,
                          student_rating['last_rating'].created_at.strftime('%Y-%m-%d') if student_rating['last_rating'] else 'لا يوجد',
                          cell_format)

        # ضبط عرض الأعمدة
        worksheet.set_column('A:K', 15)

        workbook.close()
        output.seek(0)

        # إنشاء الاستجابة
        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="students_ratings_{timezone.now().strftime("%Y%m%d")}.xlsx"'
        return response

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في تصدير البيانات: {str(e)}'
        })


def generate_student_pdf_report(request, student_id, unified_ratings):
    """إنشاء تقرير PDF لطالب واحد"""
    from django.contrib.auth import get_user_model
    from django.template.loader import render_to_string
    from django.http import HttpResponse
    from django.utils import timezone

    User = get_user_model()

    try:
        student = User.objects.get(id=student_id, user_type='student')
        student_ratings = unified_ratings.filter(student=student)

        # حساب الإحصائيات
        stats = {
            'total_ratings': student_ratings.count(),
            'avg_overall': student_ratings.aggregate(avg=Avg('overall_rating'))['avg'] or 0,
            'avg_quality': student_ratings.aggregate(avg=Avg('lesson_quality'))['avg'] or 0,
            'avg_interaction': student_ratings.aggregate(avg=Avg('teacher_interaction'))['avg'] or 0,
            'avg_technical': student_ratings.aggregate(avg=Avg('technical_quality'))['avg'] or 0,
            'scheduled_count': student_ratings.filter(lesson_type='scheduled').count(),
            'live_count': student_ratings.filter(lesson_type='live').count(),
        }
        stats['overall_average'] = round(
            (stats['avg_overall'] + stats['avg_quality'] +
             stats['avg_interaction'] + stats['avg_technical']) / 4, 1
        )

        # توزيع النجوم
        star_distribution = {}
        for i in range(1, 6):
            star_distribution[i] = student_ratings.filter(overall_rating=i).count()

        # آخر التقييمات
        recent_ratings = student_ratings.order_by('-created_at')[:20]

        context = {
            'student': student,
            'stats': stats,
            'star_distribution': star_distribution,
            'recent_ratings': recent_ratings,
            'generated_at': timezone.now(),
        }

        # إنشاء PDF باستخدام reportlab مع دعم العربية
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import cm
            from reportlab.lib import colors
            from reportlab.lib.enums import TA_CENTER
            import io

            # إنشاء buffer للـ PDF
            buffer = io.BytesIO()

            # إنشاء المستند مع هوامش محسنة
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm,
                title=f"تقرير تقييمات الطالب - {student.get_full_name()}"
            )

            # إعداد الأنماط
            styles = getSampleStyleSheet()

            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Title'],
                fontSize=18,
                spaceAfter=30,
                alignment=TA_CENTER,
                textColor=colors.HexColor('#2D5016')
            )

            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading1'],
                fontSize=14,
                spaceAfter=12,
                textColor=colors.HexColor('#2D5016'),
                backColor=colors.HexColor('#f8f9fa')
            )

            # بناء المحتوى
            story = []

            # العنوان الرئيسي
            story.append(Paragraph("أكاديمية القرآنية", title_style))
            story.append(Paragraph(f"تقرير تقييمات الطالب: {student.get_full_name()}", title_style))
            story.append(Spacer(1, 20))

            # معلومات الطالب
            story.append(Paragraph("معلومات الطالب", heading_style))
            student_data = [
                ['الاسم الكامل', student.get_full_name()],
                ['البريد الإلكتروني', student.email],
                ['تاريخ التسجيل', student.date_joined.strftime('%Y-%m-%d')],
                ['إجمالي التقييمات', str(stats['total_ratings'])],
            ]

            student_table = Table(student_data, colWidths=[4*cm, 8*cm])
            student_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2D5016')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(student_table)
            story.append(Spacer(1, 20))

            # إحصائيات التقييمات
            story.append(Paragraph("إحصائيات التقييمات", heading_style))
            stats_data = [
                ['المعيار', 'المتوسط', 'من 5'],
                ['التقييم العام', f"{stats['avg_overall']:.1f}", '5.0'],
                ['جودة الحصة', f"{stats['avg_quality']:.1f}", '5.0'],
                ['تفاعل الطالب', f"{stats['avg_interaction']:.1f}", '5.0'],
                ['الجودة التقنية', f"{stats['avg_technical']:.1f}", '5.0'],
                ['المتوسط الإجمالي', f"{stats['overall_average']:.1f}", '5.0'],
            ]

            stats_table = Table(stats_data, colWidths=[6*cm, 3*cm, 3*cm])
            stats_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2D5016')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(stats_table)
            story.append(Spacer(1, 20))

            # توزيع النجوم
            story.append(Paragraph("توزيع التقييمات", heading_style))
            stars_data = [['عدد النجوم', 'عدد التقييمات', 'النسبة المئوية']]

            for stars in range(5, 0, -1):
                count = star_distribution.get(stars, 0)
                percentage = (count / stats['total_ratings'] * 100) if stats['total_ratings'] > 0 else 0
                stars_data.append([f"{stars} نجوم", str(count), f"{percentage:.1f}%"])

            stars_table = Table(stars_data, colWidths=[4*cm, 4*cm, 4*cm])
            stars_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2D5016')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(stars_table)
            story.append(Spacer(1, 20))

            # آخر التقييمات
            if recent_ratings:
                story.append(Paragraph("آخر التقييمات", heading_style))
                ratings_data = [['التاريخ', 'المعلم', 'التقييم العام', 'نوع الحصة', 'التعليق']]

                for rating in recent_ratings[:10]:  # أول 10 تقييمات
                    comment = rating.comment[:50] + '...' if rating.comment and len(rating.comment) > 50 else (rating.comment or 'لا يوجد')
                    ratings_data.append([
                        rating.created_at.strftime('%Y-%m-%d'),
                        rating.teacher.get_full_name() if rating.teacher else 'غير محدد',
                        f"{rating.overall_rating}/5",
                        'مجدولة' if rating.lesson_type == 'scheduled' else 'مباشرة',
                        comment
                    ])

                ratings_table = Table(ratings_data, colWidths=[2.5*cm, 3*cm, 2*cm, 2*cm, 4.5*cm])
                ratings_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2D5016')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('FONTSIZE', (0, 1), (-1, -1), 8),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                story.append(ratings_table)

            # تذييل التقرير
            footer_style = ParagraphStyle(
                'Footer',
                parent=styles['Normal'],
                fontSize=10,
                alignment=TA_CENTER,
                textColor=colors.grey
            )

            story.append(Spacer(1, 30))
            footer_text = f"""
            <b>أكاديمية القرآنية</b><br/>
            تقرير تم إنشاؤه تلقائياً في {timezone.now().strftime('%d/%m/%Y الساعة %H:%M')}<br/>
            هذا التقرير يحتوي على معلومات سرية ومخصص للاستخدام الداخلي فقط
            """

            story.append(Paragraph(footer_text, footer_style))

            # بناء المستند
            doc.build(story)

            # إرجاع PDF
            pdf = buffer.getvalue()
            buffer.close()

            response = HttpResponse(pdf, content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="student_report_{student.get_full_name()}_{timezone.now().strftime("%Y%m%d")}.pdf"'
            return response

        except Exception as pdf_error:
            # في حالة فشل PDF، إرجاع HTML
            html_content = render_to_string('admin/student_pdf_report.html', context)
            response = HttpResponse(html_content, content_type='text/html')
            response['Content-Disposition'] = f'attachment; filename="student_report_{student.get_full_name()}_{timezone.now().strftime("%Y%m%d")}.html"'
            return response

    except User.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'الطالب غير موجود'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        })


def generate_all_students_pdf_report(request, student_ratings):
    """إنشاء تقرير PDF شامل لجميع الطلاب"""
    from django.template.loader import render_to_string
    from django.http import HttpResponse
    from django.utils import timezone

    context = {
        'student_ratings': student_ratings,
        'total_students': len(student_ratings),
        'generated_at': timezone.now(),
    }

    try:
        # إنشاء PDF باستخدام reportlab مع تحسينات
        try:
            from reportlab.lib.pagesizes import A4, landscape
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import cm
            from reportlab.lib import colors
            from reportlab.lib.enums import TA_CENTER
            import io

            # إنشاء buffer للـ PDF
            buffer = io.BytesIO()

            # إنشاء المستند بتوجه أفقي للجدول الكبير مع هوامش محسنة
            doc = SimpleDocTemplate(
                buffer,
                pagesize=landscape(A4),
                rightMargin=1.5*cm,
                leftMargin=1.5*cm,
                topMargin=2*cm,
                bottomMargin=2*cm,
                title="تقرير شامل - تقييمات جميع الطلاب"
            )

            # إعداد الأنماط
            styles = getSampleStyleSheet()

            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Title'],
                fontSize=20,
                spaceAfter=30,
                alignment=TA_CENTER,
                textColor=colors.HexColor('#2D5016')
            )

            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading1'],
                fontSize=14,
                spaceAfter=12,
                textColor=colors.HexColor('#2D5016')
            )

            # بناء المحتوى
            story = []

            # العنوان الرئيسي
            story.append(Paragraph("أكاديمية القرآنية", title_style))
            story.append(Paragraph("تقرير شامل - تقييمات جميع الطلاب", title_style))
            story.append(Spacer(1, 20))

            # معلومات التقرير
            story.append(Paragraph("معلومات التقرير", heading_style))
            report_info = [
                ['تاريخ الإنشاء', timezone.now().strftime('%Y-%m-%d %H:%M')],
                ['إجمالي الطلاب', str(len(student_ratings))],
                ['متوسط التقييمات', f"{sum(s['overall_average'] for s in student_ratings) / len(student_ratings):.1f}" if student_ratings else "0"],
            ]

            info_table = Table(report_info, colWidths=[4*cm, 6*cm])
            info_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2D5016')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(info_table)
            story.append(Spacer(1, 30))

            # جدول الطلاب الرئيسي
            story.append(Paragraph("تفاصيل تقييمات الطلاب", heading_style))

            # إعداد بيانات الجدول
            table_data = [
                ['اسم الطالب', 'إجمالي التقييمات', 'المتوسط العام', 'جودة الحصة', 'تفاعل الطالب', 'الجودة التقنية', 'مستوى الأداء']
            ]

            for student_rating in student_ratings:
                table_data.append([
                    student_rating['student'].get_full_name(),
                    str(student_rating['total_ratings']),
                    f"{student_rating['overall_average']:.1f}",
                    f"{student_rating['avg_quality']:.1f}",
                    f"{student_rating['avg_interaction']:.1f}",
                    f"{student_rating['avg_technical']:.1f}",
                    student_rating['performance_text']
                ])

            # إنشاء الجدول مع عرض أعمدة محسن للتوجه الأفقي
            main_table = Table(table_data, colWidths=[4*cm, 2.5*cm, 2.5*cm, 2.5*cm, 2.5*cm, 2.5*cm, 3*cm])
            main_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2D5016')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))

            story.append(main_table)

            # تذييل التقرير
            footer_style = ParagraphStyle(
                'Footer',
                parent=styles['Normal'],
                fontSize=10,
                alignment=TA_CENTER,
                textColor=colors.grey
            )

            story.append(Spacer(1, 30))
            footer_text = f"""
            <b>أكاديمية القرآنية</b><br/>
            تقرير شامل تم إنشاؤه تلقائياً في {timezone.now().strftime('%d/%m/%Y الساعة %H:%M')}<br/>
            هذا التقرير يحتوي على معلومات سرية ومخصص للاستخدام الداخلي فقط<br/>
            إجمالي الطلاب المدرجين: {len(student_ratings)}
            """

            story.append(Paragraph(footer_text, footer_style))

            # بناء المستند
            doc.build(story)

            # إرجاع PDF
            pdf = buffer.getvalue()
            buffer.close()

            response = HttpResponse(pdf, content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="all_students_report_{timezone.now().strftime("%Y%m%d")}.pdf"'
            return response

        except Exception as pdf_error:
            # في حالة فشل PDF، إرجاع HTML
            html_content = render_to_string('admin/all_students_pdf_report.html', context)
            response = HttpResponse(html_content, content_type='text/html')
            response['Content-Disposition'] = f'attachment; filename="all_students_report_{timezone.now().strftime("%Y%m%d")}.html"'
            return response

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ في إنشاء التقرير: {str(e)}'
        })


@login_required
def admin_academy_settings(request):
    """صفحة إعدادات الأكاديمية"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    # الحصول على إعدادات الأكاديمية الحالية أو إنشاء إعدادات افتراضية
    academy_settings = AcademySettings.get_settings()

    if request.method == 'POST':
        form = AcademySettingsForm(request.POST, request.FILES, instance=academy_settings)
        if form.is_valid():
            form.save()
            messages.success(request, "تم حفظ إعدادات الأكاديمية بنجاح.")
            return redirect('admin_academy_settings')
    else:
        form = AcademySettingsForm(instance=academy_settings)

    context = {
        'form': form,
        'academy_settings': academy_settings,
    }

    return render(request, 'admin/academy_settings.html', context)


@login_required
def admin_database_backup(request):
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    import os
    import datetime
    from django.conf import settings
    from django.http import JsonResponse, HttpResponse
    import zipfile
    import tempfile
    from django.core.management import call_command
    from io import StringIO

    if request.method == 'POST':
        try:
            # إنشاء اسم ملف النسخة الاحتياطية
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"qurania_backup_{timestamp}"

            # إنشاء مجلد مؤقت للنسخة الاحتياطية
            temp_dir = tempfile.mkdtemp()
            backup_path = os.path.join(temp_dir, f"{backup_filename}.json")

            # تصدير البيانات باستخدام Django's dumpdata
            with open(backup_path, 'w', encoding='utf-8') as f:
                call_command(
                    'dumpdata',
                    '--natural-foreign',
                    '--natural-primary',
                    '--exclude=contenttypes',
                    '--exclude=auth.permission',
                    '--exclude=sessions',
                    '--exclude=admin.logentry',
                    stdout=f
                )

            # التحقق من وجود الملف وحجمه
            if os.path.exists(backup_path) and os.path.getsize(backup_path) > 0:
                # إنشاء ملف ZIP يحتوي على النسخة الاحتياطية
                zip_path = os.path.join(temp_dir, f"{backup_filename}.zip")

                with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zipf:
                    # إضافة ملف البيانات
                    zipf.write(backup_path, f"{backup_filename}.json")

                    # إضافة ملف معلومات النسخة الاحتياطية
                    info_content = f"""نسخة احتياطية من قاعدة بيانات أكاديمية القرآنية
تاريخ الإنشاء: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
المستخدم: {request.user.get_full_name() or request.user.username}
حجم البيانات: {os.path.getsize(backup_path)} بايت

تعليمات الاستعادة:
1. تأكد من أن النظام يعمل بنفس إصدار Django
2. استخدم الأمر: python manage.py loaddata {backup_filename}.json
3. تأكد من إنشاء قاعدة بيانات فارغة قبل الاستعادة

ملاحظة: هذه النسخة الاحتياطية تحتوي على جميع البيانات عدا:
- جلسات المستخدمين
- سجلات الإدارة
- أذونات المستخدمين الافتراضية

للاستعادة في حالة الطوارئ:
1. فك ضغط هذا الملف
2. انسخ ملف {backup_filename}.json إلى مجلد المشروع
3. نفذ الأمر: python manage.py loaddata {backup_filename}.json
"""
                    zipf.writestr("README.txt", info_content.encode('utf-8'))

                # قراءة الملف المضغوط وإرساله
                with open(zip_path, 'rb') as f:
                    file_data = f.read()

                # إنشاء الاستجابة
                response = HttpResponse(file_data, content_type='application/zip')
                response['Content-Disposition'] = f'attachment; filename="{backup_filename}.zip"'
                response['Content-Length'] = len(file_data)

                # تنظيف الملفات المؤقتة
                try:
                    os.remove(backup_path)
                    os.remove(zip_path)
                    os.rmdir(temp_dir)
                except:
                    pass  # تجاهل أخطاء التنظيف

                return response
            else:
                return JsonResponse({'success': False, 'error': 'فشل في إنشاء ملف النسخة الاحتياطية'})

        except Exception as e:
            # تنظيف الملفات في حالة الخطأ
            try:
                if 'temp_dir' in locals():
                    import shutil
                    shutil.rmtree(temp_dir, ignore_errors=True)
            except:
                pass

            return JsonResponse({'success': False, 'error': f'حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}'})

    return JsonResponse({'success': False, 'error': 'طريقة غير مدعومة'})


@login_required
def admin_database_restore(request):
    """استعادة قاعدة البيانات من نسخة احتياطية"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    import os
    import tempfile
    import zipfile
    from django.conf import settings
    from django.http import JsonResponse
    from django.core.management import call_command
    from io import StringIO

    if request.method == 'POST' and request.FILES.get('backup_file'):
        try:
            backup_file = request.FILES['backup_file']

            # التحقق من نوع الملف
            if not backup_file.name.endswith('.zip'):
                return JsonResponse({'success': False, 'error': 'يجب أن يكون الملف من نوع ZIP'})

            # التحقق من حجم الملف (حد أقصى 100 ميجابايت)
            if backup_file.size > 100 * 1024 * 1024:
                return JsonResponse({'success': False, 'error': 'حجم الملف كبير جداً (الحد الأقصى 100 ميجابايت)'})

            # إنشاء مجلد مؤقت
            temp_dir = tempfile.mkdtemp()
            zip_path = os.path.join(temp_dir, backup_file.name)

            # حفظ الملف المرفوع
            with open(zip_path, 'wb') as destination:
                for chunk in backup_file.chunks():
                    destination.write(chunk)

            # التحقق من سلامة الملف المضغوط
            try:
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    # التحقق من سلامة الملف المضغوط
                    bad_file = zip_ref.testzip()
                    if bad_file:
                        return JsonResponse({'success': False, 'error': f'الملف المضغوط تالف: {bad_file}'})

                    # استخراج الملفات
                    zip_ref.extractall(temp_dir)
            except zipfile.BadZipFile:
                return JsonResponse({'success': False, 'error': 'الملف المضغوط تالف أو غير صالح'})

            # البحث عن ملف JSON
            json_files = [f for f in os.listdir(temp_dir) if f.endswith('.json')]
            if not json_files:
                return JsonResponse({'success': False, 'error': 'لم يتم العثور على ملف البيانات في النسخة الاحتياطية'})

            json_file_path = os.path.join(temp_dir, json_files[0])

            # التحقق من صحة ملف JSON
            try:
                import json
                with open(json_file_path, 'r', encoding='utf-8') as f:
                    json.load(f)  # محاولة قراءة الملف للتأكد من صحته
            except json.JSONDecodeError:
                return JsonResponse({'success': False, 'error': 'ملف البيانات تالف أو غير صالح'})
            except Exception as e:
                return JsonResponse({'success': False, 'error': f'خطأ في قراءة ملف البيانات: {str(e)}'})

            # تنفيذ أمر الاستعادة باستخدام Django management command
            try:
                # إعادة توجيه المخرجات لالتقاط الأخطاء
                output = StringIO()
                call_command('loaddata', json_file_path, stdout=output, stderr=output)

                # تنظيف الملفات المؤقتة
                try:
                    import shutil
                    shutil.rmtree(temp_dir, ignore_errors=True)
                except:
                    pass

                return JsonResponse({'success': True, 'message': 'تم استعادة قاعدة البيانات بنجاح'})

            except Exception as e:
                # تنظيف الملفات المؤقتة في حالة الخطأ
                try:
                    import shutil
                    shutil.rmtree(temp_dir, ignore_errors=True)
                except:
                    pass

                return JsonResponse({'success': False, 'error': f'فشل في استعادة البيانات: {str(e)}'})

        except Exception as e:
            # تنظيف الملفات المؤقتة في حالة الخطأ
            try:
                if 'temp_dir' in locals():
                    import shutil
                    shutil.rmtree(temp_dir, ignore_errors=True)
            except:
                pass

            return JsonResponse({'success': False, 'error': f'حدث خطأ أثناء استعادة البيانات: {str(e)}'})

    return JsonResponse({'success': False, 'error': 'لم يتم رفع ملف صحيح'})


@login_required
def teacher_ratings(request):
    """صفحة تقييمات المعلم"""
    if not request.user.is_teacher():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from lessons.models import LessonRating, LiveLessonRating, UnifiedLessonRating
    from django.db.models import Avg, Count

    # جلب التقييمات الموحدة للمعلم
    unified_ratings = UnifiedLessonRating.objects.filter(
        teacher=request.user
    ).select_related('student', 'scheduled_lesson', 'live_lesson').order_by('-created_at')

    # جلب تقييمات الحصص العادية (النظام القديم - للتوافق)
    lesson_ratings = LessonRating.objects.filter(
        lesson__teacher=request.user
    ).select_related('lesson', 'student').order_by('-created_at')

    # جلب تقييمات الحصص المباشرة (النظام القديم - للتوافق)
    live_lesson_ratings = LiveLessonRating.objects.filter(
        teacher=request.user
    ).select_related('live_lesson', 'student').order_by('-created_at')

    # حساب الإحصائيات للنظام الموحد
    total_unified_ratings = unified_ratings.count()
    unified_stats = {}
    if total_unified_ratings > 0:
        unified_stats = {
            'total_ratings': total_unified_ratings,
            'avg_overall': unified_ratings.aggregate(avg=Avg('overall_rating'))['avg'] or 0,
            'avg_quality': unified_ratings.aggregate(avg=Avg('lesson_quality'))['avg'] or 0,
            'avg_interaction': unified_ratings.aggregate(avg=Avg('teacher_interaction'))['avg'] or 0,
            'avg_technical': unified_ratings.aggregate(avg=Avg('technical_quality'))['avg'] or 0,
            'scheduled_count': unified_ratings.filter(lesson_type='scheduled').count(),
            'live_count': unified_ratings.filter(lesson_type='live').count(),
        }
        unified_stats['overall_average'] = round(
            (unified_stats['avg_overall'] + unified_stats['avg_quality'] +
             unified_stats['avg_interaction'] + unified_stats['avg_technical']) / 4, 1
        )

    # حساب الإحصائيات للحصص العادية (النظام القديم)
    total_lesson_ratings = lesson_ratings.count()
    if total_lesson_ratings > 0:
        lesson_stats = lesson_ratings.aggregate(
            avg_teacher=Avg('teacher_rating'),
            avg_quality=Avg('lesson_quality'),
            avg_technical=Avg('technical_quality'),
            avg_satisfaction=Avg('overall_satisfaction')
        )
        average_lesson_rating = round(
            (lesson_stats['avg_teacher'] + lesson_stats['avg_quality'] +
             lesson_stats['avg_technical'] + lesson_stats['avg_satisfaction']) / 4, 1
        ) if all(lesson_stats.values()) else 0
    else:
        average_lesson_rating = 0
        lesson_stats = {}

    # حساب الإحصائيات للحصص المباشرة
    total_live_ratings = live_lesson_ratings.count()
    if total_live_ratings > 0:
        live_stats = live_lesson_ratings.aggregate(
            avg_overall=Avg('overall_rating'),
            avg_quality=Avg('lesson_quality'),
            avg_interaction=Avg('teacher_interaction'),
            avg_technical=Avg('technical_quality')
        )
        average_live_rating = round(
            (live_stats['avg_overall'] + live_stats['avg_quality'] +
             live_stats['avg_interaction'] + live_stats['avg_technical']) / 4, 1
        ) if all(live_stats.values()) else 0
    else:
        average_live_rating = 0
        live_stats = {}

    # حساب المتوسط العام
    total_ratings = total_lesson_ratings + total_live_ratings
    if total_ratings > 0:
        overall_average = round(
            (average_lesson_rating * total_lesson_ratings + average_live_rating * total_live_ratings) / total_ratings, 1
        )
    else:
        overall_average = 0

    # توزيع التقييمات للحصص العادية
    lesson_rating_distribution = {}
    for i in range(1, 6):
        lesson_rating_distribution[i] = lesson_ratings.filter(teacher_rating=i).count()

    # توزيع التقييمات للحصص المباشرة
    live_rating_distribution = {}
    for i in range(1, 6):
        live_rating_distribution[i] = live_lesson_ratings.filter(overall_rating=i).count()

    context = {
        # النظام الموحد الجديد
        'unified_ratings': unified_ratings[:15],
        'unified_stats': unified_stats,
        'total_unified_ratings': total_unified_ratings,

        # الأنظمة القديمة (للتوافق)
        'lesson_ratings': lesson_ratings,
        'live_lesson_ratings': live_lesson_ratings,
        'total_lesson_ratings': total_lesson_ratings,
        'total_live_ratings': total_live_ratings,
        'total_ratings': total_ratings + total_unified_ratings,
        'average_lesson_rating': average_lesson_rating,
        'average_live_rating': average_live_rating,
        'overall_average': overall_average,
        'lesson_rating_distribution': lesson_rating_distribution,
        'live_rating_distribution': live_rating_distribution,
        'lesson_stats': lesson_stats,
        'live_stats': live_stats,
    }

    return render(request, 'teacher/ratings.html', context)


@login_required
def student_dashboard(request):
    """لوحة تحكم الطالب"""
    if not request.user.is_student():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.utils import timezone
    # تم إزالة التبعية على courses.models
    from lessons.models import Lesson
    from django.db.models import Count, Avg

    # إحصائيات الحصص
    today = timezone.now().date()
    today_lessons_count = Lesson.objects.filter(
        student=request.user,
        scheduled_date__date=today,
        status__in=['scheduled', 'in_progress']
    ).count()

    upcoming_lessons_count = Lesson.objects.filter(
        student=request.user,
        scheduled_date__gt=timezone.now(),
        status='scheduled'
    ).count()

    # إحصائيات الدورات - تم تعطيلها بعد حذف courses app
    total_courses = 0  # تم إزالة التبعية على Enrollment

    # إحصائيات الحصص المكتملة والمتبقية
    total_lessons_completed = Lesson.objects.filter(
        student=request.user,
        status='completed'
    ).count()

    total_lessons_scheduled = Lesson.objects.filter(
        student=request.user,
        status__in=['scheduled', 'in_progress', 'completed']
    ).count()

    total_lessons_remaining = Lesson.objects.filter(
        student=request.user,
        status='scheduled'
    ).count()

    # حساب نسبة التقدم
    if total_lessons_scheduled > 0:
        progress_percentage = round((total_lessons_completed / total_lessons_scheduled) * 100)
    else:
        progress_percentage = 0

    # المستوى الحالي (يمكن تحسينه لاحقاً)
    current_level = request.user.student_level or 'مبتدئ'

    # الحصص المباشرة
    from lessons.models import LiveLesson
    live_lessons = LiveLesson.objects.filter(
        student=request.user,
        status='live'
    ).select_related('teacher').order_by('-started_at')

    # الحصص المجدولة (تشمل التي حان موعدها ولم تبدأ بعد)
    now = timezone.now()
    scheduled_live_lessons = LiveLesson.objects.filter(
        student=request.user,
        status='scheduled',
        scheduled_date__gte=now - timezone.timedelta(hours=2)  # تظهر الحصص حتى لو تأخرت ساعتين
    ).select_related('teacher').order_by('scheduled_date')[:3]

    # الحصص المجدولة من نظام الاشتراكات
    from subscriptions.models import ScheduledLesson
    subscription_scheduled_lessons = ScheduledLesson.objects.filter(
        subscription__student=request.user,  # الحصص الخاصة بهذا الطالب
        status='scheduled',
        scheduled_date__gte=now - timezone.timedelta(hours=2)
    ).select_related('subscription__student', 'subscription__plan', 'teacher').order_by('scheduled_date')[:3]

    context = {
        'today_lessons_count': today_lessons_count,
        'upcoming_lessons_count': upcoming_lessons_count,
        'total_courses': total_courses,
        'total_lessons_completed': total_lessons_completed,
        'total_lessons_remaining': total_lessons_remaining,
        'progress_percentage': progress_percentage,
        'current_level': current_level,
        'live_lessons': live_lessons,
        'scheduled_live_lessons': scheduled_live_lessons,
        'subscription_scheduled_lessons': subscription_scheduled_lessons,
    }

    return render(request, 'dashboard/student.html', context)


@login_required
def student_progress(request):
    """صفحة تقدم الطالب"""
    if not request.user.is_student():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import StudentSubscription
    from lessons.models import Lesson, LessonRating
    from django.utils import timezone
    from django.db.models import Avg, Count

    # الحصول على الاشتراك النشط للطالب
    active_subscription = StudentSubscription.objects.filter(
        student=request.user,
        status='active'
    ).first()

    # إحصائيات الحصص
    total_lessons_completed = Lesson.objects.filter(
        student=request.user,
        status='completed'
    ).count()

    total_lessons_scheduled = Lesson.objects.filter(
        student=request.user,
        status__in=['scheduled', 'in_progress', 'completed']
    ).count()

    total_lessons_remaining = Lesson.objects.filter(
        student=request.user,
        status='scheduled'
    ).count()

    # آخر حصة حضرها الطالب
    last_lesson = Lesson.objects.filter(
        student=request.user,
        status='completed'
    ).order_by('-actual_end_time').first()

    # حساب متوسط التقييمات التي حصل عليها الطالب
    avg_rating = LessonRating.objects.filter(
        lesson__student=request.user
    ).aggregate(avg_rating=Avg('overall_satisfaction'))['avg_rating'] or 0

    # حساب معدل الحضور
    if total_lessons_scheduled > 0:
        attendance_rate = round((total_lessons_completed / total_lessons_scheduled) * 100)
    else:
        attendance_rate = 0

    # حساب ساعات التعلم الإجمالية
    total_learning_hours = 0
    if active_subscription:
        completed_lessons_in_subscription = active_subscription.get_completed_lessons_count()
        total_learning_hours = round((completed_lessons_in_subscription * active_subscription.plan.lesson_duration) / 60, 1)

    # حساب نسبة التقدم في الاشتراك
    subscription_progress_percentage = 0
    if active_subscription and active_subscription.plan.lessons_count > 0:
        subscription_progress_percentage = round(
            (active_subscription.get_completed_lessons_count() / active_subscription.plan.lessons_count) * 100
        )

    # حساب الأيام المتبقية في الاشتراك
    days_remaining = 0
    if active_subscription:
        days_remaining = active_subscription.days_remaining()

    context = {
        'active_subscription': active_subscription,
        'completed_lessons': total_lessons_completed,
        'total_lessons': active_subscription.plan.lessons_count if active_subscription else total_lessons_scheduled,
        'remaining_lessons': active_subscription.remaining_lessons if active_subscription else total_lessons_remaining,
        'subscription_progress_percentage': subscription_progress_percentage,
        'days_remaining': days_remaining,
        'last_lesson': last_lesson,
        'avg_rating': round(avg_rating, 1) if avg_rating else 0,
        'attendance_rate': attendance_rate,
        'total_learning_hours': total_learning_hours,
        'lessons_this_month': Lesson.objects.filter(
            student=request.user,
            status='completed',
            actual_end_time__month=timezone.now().month,
            actual_end_time__year=timezone.now().year
        ).count(),
    }

    return render(request, 'student/progress.html', context)







@login_required
def admin_join_lesson(request, lesson_id):
    """انضمام المدير لمراقبة الحصة"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.shortcuts import get_object_or_404
    from lessons.models import Lesson

    lesson = get_object_or_404(Lesson, id=lesson_id)

    # إعادة توجيه لرابط Jitsi
    return redirect(lesson.get_jitsi_url())


@login_required
def admin_lesson_details(request, lesson_id):
    """عرض تفاصيل الحصة للمدير"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.shortcuts import get_object_or_404
    from lessons.models import Lesson, LessonAttendance, LessonContent, LessonRating

    lesson = get_object_or_404(Lesson, id=lesson_id)

    # جلب بيانات الحضور
    attendance_records = LessonAttendance.objects.filter(lesson=lesson)

    # جلب محتوى الحصة
    content_records = LessonContent.objects.filter(lesson=lesson)

    # جلب التقييم إن وجد
    rating = LessonRating.objects.filter(lesson=lesson).first()

    context = {
        'lesson': lesson,
        'attendance_records': attendance_records,
        'content_records': content_records,
        'rating': rating,
    }

    return render(request, 'admin/lesson_details.html', context)


@login_required
def admin_live_lessons(request):
    """صفحة الحصص المباشرة مع النظام الجديد"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from lessons.models import LiveLesson, UniversalLessonMonitoring
    from subscriptions.models import ScheduledLesson
    from django.utils import timezone

    now = timezone.now()

    # الحصص المباشرة الجارية الآن (التي بدأها المعلم)
    live_lessons = LiveLesson.objects.filter(
        status='live'
    ).select_related('teacher', 'student').order_by('-started_at')

    # إضافة بيانات المراقبة لكل حصة
    for lesson in live_lessons:
        lesson.monitoring_data = UniversalLessonMonitoring.objects.filter(
            live_lesson=lesson
        ).select_related('user')

    # الحصص المجدولة من نظام الاشتراكات (التي جدولها المدير يدوياً أو تلقائياً)
    scheduled_subscription_lessons = ScheduledLesson.objects.filter(
        status='scheduled',
        teacher__isnull=False  # فقط الحصص التي لها معلم مُعيَّن
    ).select_related('subscription__student', 'teacher', 'subscription__plan').order_by('scheduled_date')

    # الحصص المباشرة المجدولة (من النظام القديم)
    scheduled_live_lessons = LiveLesson.objects.filter(
        status='scheduled'
    ).select_related('teacher', 'student').order_by('scheduled_date')

    # دمج الحصص المجدولة من كلا النظامين
    all_scheduled_lessons = []

    # إضافة الحصص المجدولة من الاشتراكات
    for lesson in scheduled_subscription_lessons:
        all_scheduled_lessons.append({
            'id': lesson.id,
            'title': f"حصة رقم {lesson.lesson_number}",
            'teacher': lesson.teacher,
            'student': lesson.subscription.student,
            'scheduled_date': lesson.scheduled_date,
            'duration_minutes': lesson.duration_minutes,
            'type': 'subscription',
            'lesson_obj': lesson
        })

    # إضافة الحصص المباشرة المجدولة
    for lesson in scheduled_live_lessons:
        all_scheduled_lessons.append({
            'id': lesson.id,
            'title': lesson.title,
            'teacher': lesson.teacher,
            'student': lesson.student,
            'scheduled_date': lesson.scheduled_date,
            'duration_minutes': lesson.duration_minutes,
            'type': 'live',
            'lesson_obj': lesson
        })

    # ترتيب جميع الحصص المجدولة حسب التاريخ
    all_scheduled_lessons.sort(key=lambda x: x['scheduled_date'])

    context = {
        'live_lessons': live_lessons,
        'scheduled_live_lessons': scheduled_live_lessons,
        'scheduled_subscription_lessons': scheduled_subscription_lessons,
        'all_scheduled_lessons': all_scheduled_lessons,
    }

    return render(request, 'admin/live_lessons.html', context)


@login_required
def admin_monitoring_dashboard(request):
    """لوحة مراقبة المدير الجديدة مع النظام المتقدم"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from lessons.models import LiveLesson, UniversalLessonMonitoring
    from subscriptions.models import ScheduledLesson
    from django.utils import timezone
    from django.db.models import Count, Q

    now = timezone.now()

    # الحصص النشطة حالياً مع المراقبة
    active_lessons = LiveLesson.objects.filter(
        status='live'
    ).select_related('teacher', 'student').order_by('-started_at')

    # إضافة بيانات المراقبة لكل حصة
    for lesson in active_lessons:
        lesson.monitoring_data = UniversalLessonMonitoring.objects.filter(
            live_lesson=lesson
        ).select_related('user').order_by('user_role')

    # الحصص المجدولة القادمة
    upcoming_lessons = ScheduledLesson.objects.filter(
        status='scheduled',
        scheduled_date__gte=now,
        scheduled_date__lte=now + timezone.timedelta(hours=2)
    ).select_related('teacher', 'subscription__student').order_by('scheduled_date')

    # إحصائيات المراقبة
    monitoring_stats = {
        'total_active_monitoring': UniversalLessonMonitoring.objects.filter(
            status__in=['confirmed', 'active', 'warning', 'critical']
        ).count(),
        'warning_count': UniversalLessonMonitoring.objects.filter(
            status__in=['warning', 'critical']
        ).count(),
        'fraud_suspected': UniversalLessonMonitoring.objects.filter(
            status='suspicious'
        ).count(),
        'fraud_detected': UniversalLessonMonitoring.objects.filter(
            status='fraud_detected'
        ).count(),
    }

    # أنشطة مشبوهة حديثة
    suspicious_activities = UniversalLessonMonitoring.objects.filter(
        suspicious_activities__isnull=False
    ).exclude(
        suspicious_activities=[]
    ).select_related('user', 'live_lesson', 'scheduled_lesson').order_by('-updated_at')[:10]

    context = {
        'active_lessons': active_lessons,
        'upcoming_lessons': upcoming_lessons,
        'monitoring_stats': monitoring_stats,
        'suspicious_activities': suspicious_activities,
    }

    return render(request, 'admin/monitoring_dashboard.html', context)


@login_required
@require_http_methods(["POST"])
def admin_start_subscription_lesson(request, lesson_id):
    """API endpoint لبدء حصة مجدولة من الاشتراكات"""
    if not request.user.is_admin():
        return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية لهذا الإجراء'})

    try:
        from subscriptions.models import ScheduledLesson
        from lessons.models import LiveLesson
        from django.utils import timezone

        # الحصول على الحصة المجدولة
        scheduled_lesson = ScheduledLesson.objects.get(
            id=lesson_id,
            status='scheduled'
        )

        # إنشاء حصة مباشرة جديدة من الحصة المجدولة
        live_lesson = LiveLesson.objects.create(
            title=f"حصة رقم {scheduled_lesson.lesson_number} - {scheduled_lesson.subscription.plan.name}",
            description=f"حصة من باقة {scheduled_lesson.subscription.plan.name} للطالب {scheduled_lesson.subscription.student.get_full_name()}",
            teacher=scheduled_lesson.teacher,
            student=scheduled_lesson.subscription.student,
            scheduled_date=scheduled_lesson.scheduled_date,
            duration_minutes=scheduled_lesson.duration_minutes,
            status='live',
            started_at=timezone.now(),
            created_by=request.user
        )

        # تحديث الحصة المجدولة
        scheduled_lesson.status = 'converted_to_live'
        scheduled_lesson.live_lesson_id = live_lesson.id
        scheduled_lesson.save()

        # إرسال إشعارات
        try:
            from notifications.models import Notification

            # إشعار للطالب
            Notification.objects.create(
                user=scheduled_lesson.subscription.student,
                title="بدء حصة مباشرة",
                message=f"بدأ المدير الحصة رقم {scheduled_lesson.lesson_number} مع المعلم {scheduled_lesson.teacher.get_full_name()}",
                notification_type='lesson_started'
            )

            # إشعار للمعلم
            Notification.objects.create(
                user=scheduled_lesson.teacher,
                title="بدء حصة مباشرة",
                message=f"بدأ المدير الحصة رقم {scheduled_lesson.lesson_number} مع الطالب {scheduled_lesson.subscription.student.get_full_name()}",
                notification_type='lesson_started'
            )
        except Exception as e:
            print(f"Error sending notifications: {e}")

        return JsonResponse({
            'success': True,
            'message': 'تم تحويل الحصة وبدئها بنجاح',
            'live_lesson_id': live_lesson.id,
            'live_lesson_url': f'/dashboard/teacher/live-lesson/{live_lesson.id}/'
        })

    except ScheduledLesson.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'الحصة المجدولة غير موجودة'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})


@login_required
@require_http_methods(["POST"])
def admin_start_live_lesson(request, lesson_id):
    """API endpoint لبدء حصة مباشرة مجدولة"""
    if not request.user.is_admin():
        return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية لهذا الإجراء'})

    try:
        from lessons.models import LiveLesson
        from django.utils import timezone

        # الحصول على الحصة المباشرة المجدولة
        live_lesson = LiveLesson.objects.get(
            id=lesson_id,
            status='scheduled'
        )

        # تحديث حالة الحصة إلى مباشرة
        live_lesson.status = 'live'
        live_lesson.started_at = timezone.now()
        live_lesson.save()

        # إرسال إشعارات
        try:
            from notifications.models import Notification

            # إشعار للطالب
            Notification.objects.create(
                user=live_lesson.student,
                title="بدء حصة مباشرة",
                message=f"بدأ المدير الحصة '{live_lesson.title}' مع المعلم {live_lesson.teacher.get_full_name()}",
                notification_type='lesson_started'
            )

            # إشعار للمعلم
            Notification.objects.create(
                user=live_lesson.teacher,
                title="بدء حصة مباشرة",
                message=f"بدأ المدير الحصة '{live_lesson.title}' مع الطالب {live_lesson.student.get_full_name()}",
                notification_type='lesson_started'
            )
        except Exception as e:
            print(f"Error sending notifications: {e}")

        return JsonResponse({
            'success': True,
            'message': 'تم بدء الحصة بنجاح',
            'live_lesson_id': live_lesson.id,
            'live_lesson_url': f'/live-lesson-room/{live_lesson.id}/'
        })

    except LiveLesson.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'الحصة المباشرة غير موجودة'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})


@login_required
def admin_lessons(request):
    """مركز مراقبة الحصص - مراقبة شاملة للحصص المباشرة والعادية في الوقت الفعلي"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.db.models import Q, Count, Avg
    from lessons.models import Lesson, LessonRating, LiveLesson
    # تم إزالة التبعية على courses.models بعد حذف courses app
    from django.utils import timezone
    from django.http import JsonResponse

    now = timezone.now()

    # الحصص المباشرة الجارية
    live_lessons = LiveLesson.objects.filter(
        status='live'
    ).select_related('teacher', 'student').order_by('-started_at')

    # الحصص المباشرة المجدولة
    scheduled_live_lessons = LiveLesson.objects.filter(
        status='scheduled'
    ).select_related('teacher', 'student').order_by('scheduled_date')

    # الحصص العادية الجارية - تم تعطيلها بعد حذف courses app
    regular_lessons = Lesson.objects.filter(
        status='in_progress'
    ).select_related('teacher', 'student').order_by('scheduled_date')

    # حساب المعلمين النشطين
    active_teachers_live = live_lessons.values_list('teacher_id', flat=True).distinct()
    active_teachers_regular = regular_lessons.values_list('teacher_id', flat=True).distinct()
    active_teachers_count = len(set(list(active_teachers_live) + list(active_teachers_regular)))

    # حساب الطلاب النشطين
    active_students_live = live_lessons.values_list('student_id', flat=True).distinct()
    active_students_regular = regular_lessons.values_list('student_id', flat=True).distinct()
    active_students_count = len(set(list(active_students_live) + list(active_students_regular)))

    # إذا كان الطلب AJAX، إرجاع البيانات كـ JSON للتحديث في الوقت الفعلي
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'live_lessons_count': live_lessons.count(),
            'scheduled_lessons_count': scheduled_live_lessons.count(),
            'regular_lessons_count': regular_lessons.count(),
            'active_teachers_count': active_teachers_count,
            'active_students_count': active_students_count,
        })

    # جلب جميع الحصص المباشرة للجدول
    all_live_lessons = LiveLesson.objects.select_related(
        'teacher',
        'student'
    ).order_by('-created_at')

    # فلترة حسب الحالة
    status_filter = request.GET.get('status')
    if status_filter:
        all_live_lessons = all_live_lessons.filter(status=status_filter)

    # فلترة حسب المعلم
    teacher_filter = request.GET.get('teacher')
    if teacher_filter:
        all_live_lessons = all_live_lessons.filter(teacher_id=teacher_filter)

    # فلترة حسب التاريخ
    date_filter = request.GET.get('date_filter')
    today = timezone.now().date()
    if date_filter == 'today':
        all_live_lessons = all_live_lessons.filter(
            Q(scheduled_date__date=today) |
            Q(started_at__date=today)
        )
    elif date_filter == 'week':
        week_start = today - timezone.timedelta(days=today.weekday())
        week_end = week_start + timezone.timedelta(days=6)
        all_live_lessons = all_live_lessons.filter(
            Q(scheduled_date__date__range=[week_start, week_end]) |
            Q(started_at__date__range=[week_start, week_end])
        )
    elif date_filter == 'month':
        all_live_lessons = all_live_lessons.filter(
            Q(scheduled_date__year=today.year, scheduled_date__month=today.month) |
            Q(started_at__year=today.year, started_at__month=today.month)
        )

    # البحث
    search_query = request.GET.get('search')
    if search_query:
        all_live_lessons = all_live_lessons.filter(
            Q(title__icontains=search_query) |
            Q(student__first_name__icontains=search_query) |
            Q(student__last_name__icontains=search_query) |
            Q(teacher__first_name__icontains=search_query) |
            Q(teacher__last_name__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # إحصائيات الحصص المباشرة
    total_live_lessons = all_live_lessons.count()
    today_live_lessons = all_live_lessons.filter(
        Q(scheduled_date__date=today) |
        Q(started_at__date=today)
    ).count()
    completed_live_lessons = all_live_lessons.filter(status='completed').count()
    cancelled_live_lessons = all_live_lessons.filter(status='cancelled').count()
    live_now_lessons = all_live_lessons.filter(status='live').count()

    # إحصائيات الحصص المباشرة حسب الحالة
    live_status_stats = {}
    live_lesson_statuses = [
        ('live', 'جارية الآن'),
        ('scheduled', 'مجدولة'),
        ('completed', 'مكتملة'),
        ('cancelled', 'ملغية'),
    ]

    for status_code, status_name in live_lesson_statuses:
        count = all_live_lessons.filter(status=status_code).count()
        live_status_stats[status_code] = {
            'name': status_name,
            'count': count
        }

    context = {
        # بيانات المراقبة المباشرة
        'live_lessons': live_lessons,
        'scheduled_live_lessons': scheduled_live_lessons,
        'regular_lessons': regular_lessons,
        'active_teachers_count': active_teachers_count,
        'active_students_count': active_students_count,

        # بيانات جدول الحصص المباشرة
        'all_live_lessons': all_live_lessons,

        # بيانات الفلترة والبحث
        'status_filter': status_filter,
        'teacher_filter': teacher_filter,
        'date_filter': date_filter,
        'search_query': search_query,

        # المعلمون المتاحون للفلترة
        'available_teachers': User.objects.filter(
            user_type='teacher',
            is_active=True,
            is_active_teacher=True
        ).order_by('first_name'),

        # خيارات الحالة للحصص المباشرة
        'live_lesson_statuses': [
            ('live', 'جارية الآن'),
            ('scheduled', 'مجدولة'),
            ('completed', 'مكتملة'),
            ('cancelled', 'ملغية'),
        ],
    }

    return render(request, 'admin/lessons.html', context)


@login_required
def admin_quality_monitoring(request):
    """صفحة مراقبة جودة الحصص للمديرين"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    return render(request, 'admin/quality_monitoring.html')


@login_required
def teacher_schedule(request):
    """صفحة جدول المعلم - عرض شامل للحصص مع التقويم"""
    if not request.user.is_teacher():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.utils import timezone
    from datetime import timedelta
    from subscriptions.models import ScheduledLesson, StudentSubscription, SubscriptionPlan
    from lessons.models import LiveLesson
    from django.db.models import Count, Q

    now = timezone.now()
    today = now.date()

    # جلب جميع الحصص المجدولة للمعلم - فقط المخصصة له
    scheduled_lessons = ScheduledLesson.objects.filter(
        teacher=request.user,  # المعلم المحدد لهذه الحصص
        subscription__status='active'  # الاشتراكات النشطة فقط
    ).select_related(
        'subscription__student',
        'subscription__plan'
    ).order_by('scheduled_date')

    # جلب الحصص المباشرة للمعلم - فقط المخصصة له
    live_lessons = LiveLesson.objects.filter(
        teacher=request.user
    ).select_related('student').order_by('scheduled_date')

    # إحصائيات سريعة
    # الحصص هذا الشهر
    month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)
    month_end = month_end.replace(hour=23, minute=59, second=59, microsecond=999999)

    this_month_lessons = scheduled_lessons.filter(
        scheduled_date__gte=month_start,
        scheduled_date__lte=month_end
    )

    # إحصائيات
    stats = {
        'total_this_month': this_month_lessons.count(),
        'completed_this_month': this_month_lessons.filter(status='completed').count(),
        'upcoming_this_month': this_month_lessons.filter(
            status='scheduled',
            scheduled_date__gte=now
        ).count(),
        'active_students': StudentSubscription.objects.filter(
            scheduled_lessons__teacher=request.user,
            status='active'
        ).distinct().count()
    }

    # الحصص القادمة (7 أيام) - فقط المجدولة (ليس المكتملة)
    week_end = now + timedelta(days=7)
    upcoming_lessons = scheduled_lessons.filter(
        status='scheduled',  # فقط المجدولة (ليس المكتملة)
        scheduled_date__gte=now,  # من الآن فصاعداً
        scheduled_date__lte=week_end
    )[:10]

    # حصص اليوم - فقط الجارية والقادمة (ليس المكتملة)
    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
    today_end = now.replace(hour=23, minute=59, second=59, microsecond=999999)
    today_lessons = scheduled_lessons.filter(
        status='scheduled',  # فقط المجدولة (ليس المكتملة)
        scheduled_date__gte=now,  # من الآن فصاعداً
        scheduled_date__lte=today_end
    )

    # الطلاب النشطين للفلترة
    active_students = User.objects.filter(
        user_type='student',
        subscriptions__scheduled_lessons__teacher=request.user
    ).distinct()

    # الباقات المتاحة للفلترة (بدون أسعار)
    available_plans = SubscriptionPlan.objects.filter(
        subscriptions__scheduled_lessons__teacher=request.user
    ).distinct()

    context = {
        'scheduled_lessons': scheduled_lessons,
        'live_lessons': live_lessons,
        'stats': stats,
        'upcoming_lessons': upcoming_lessons,
        'today_lessons': today_lessons,
        'active_students': active_students,
        'available_plans': available_plans,
        'current_month': now.strftime('%Y-%m'),
        'today': today,
    }

    return render(request, 'teacher/schedule.html', context)


@login_required
def teacher_calendar_api(request):
    """API للتقويم - إرجاع الحصص بصيغة JSON للتقويم مع دعم الفلترة"""
    if not request.user.is_teacher():
        return JsonResponse({'error': 'غير مصرح'}, status=403)

    from django.utils import timezone
    from datetime import timedelta
    from subscriptions.models import ScheduledLesson
    from lessons.models import LiveLesson

    # جلب معاملات الفلترة
    student_id = request.GET.get('student_id')
    plan_id = request.GET.get('plan_id')
    status_filter = request.GET.get('status')

    # بناء استعلام الحصص المجدولة - فقط الحصص التي تم تعيين المعلم لها
    scheduled_query = ScheduledLesson.objects.filter(
        teacher=request.user,  # المعلم المحدد لهذه الحصص
        subscription__status='active'  # الاشتراكات النشطة فقط
    ).select_related(
        'subscription__student',
        'subscription__plan'
    ).prefetch_related(
        'subscription__student__profile'  # لتحسين الأداء
    )

    # تطبيق الفلاتر
    if student_id:
        scheduled_query = scheduled_query.filter(subscription__student_id=student_id)
    if plan_id:
        scheduled_query = scheduled_query.filter(subscription__plan_id=plan_id)
    if status_filter:
        scheduled_query = scheduled_query.filter(status=status_filter)

    # ترتيب حسب التاريخ
    scheduled_query = scheduled_query.order_by('scheduled_date')

    # بناء استعلام الحصص المباشرة
    live_query = LiveLesson.objects.filter(
        teacher=request.user
    ).select_related('student')

    if student_id:
        live_query = live_query.filter(student_id=student_id)
    if status_filter:
        live_query = live_query.filter(status=status_filter)

    events = []

    # إضافة الحصص المجدولة
    for lesson in scheduled_query:
        # تحديد اللون حسب الحالة
        color = {
            'scheduled': '#3498db',           # أزرق - مجدولة
            'completed': '#27ae60',           # أخضر - مكتملة
            'cancelled': '#e74c3c',           # أحمر - ملغية
            'rescheduled': '#f39c12',         # برتقالي - معاد جدولتها
            'no_show': '#95a5a6',             # رمادي - غياب
            'converted_to_live': '#9b59b6'    # بنفسجي - تم تحويلها
        }.get(lesson.status, '#3498db')

        # تحديد العنوان بناءً على الحالة
        if lesson.status == 'completed':
            title = f'✅ حصة رقم {lesson.lesson_number} - {lesson.subscription.student.first_name}'
        elif lesson.status == 'cancelled':
            title = f'❌ حصة رقم {lesson.lesson_number} - {lesson.subscription.student.first_name}'
        elif lesson.status == 'no_show':
            title = f'⚠️ حصة رقم {lesson.lesson_number} - {lesson.subscription.student.first_name}'
        elif lesson.status == 'converted_to_live':
            title = f'🔄 حصة رقم {lesson.lesson_number} - {lesson.subscription.student.first_name}'
        else:
            title = f'📚 حصة رقم {lesson.lesson_number} - {lesson.subscription.student.first_name}'

        events.append({
            'id': f'scheduled_{lesson.id}',
            'title': title,
            'start': lesson.scheduled_date.isoformat(),
            'end': (lesson.scheduled_date + timedelta(minutes=lesson.duration_minutes)).isoformat(),
            'color': color,
            'borderColor': color,
            'textColor': '#ffffff',
            'extendedProps': {
                'type': 'scheduled',
                'lesson_id': lesson.id,
                'lesson_number': lesson.lesson_number,
                'student_id': lesson.subscription.student.id,
                'student_name': lesson.subscription.student.get_full_name(),
                'plan_id': lesson.subscription.plan.id,
                'plan_name': lesson.subscription.plan.name,
                'status': lesson.status,
                'duration': lesson.duration_minutes,
                'notes': lesson.notes or '',
                'subscription_id': lesson.subscription.id
            }
        })

    # إضافة الحصص المباشرة
    for lesson in live_query:
        # تحديد اللون حسب الحالة
        color = {
            'scheduled': '#2980b9',    # أزرق داكن - مجدولة
            'live': '#e67e22',         # برتقالي - مباشرة الآن
            'completed': '#16a085',    # أخضر داكن - مكتملة
            'cancelled': '#c0392b'     # أحمر داكن - ملغية
        }.get(lesson.status, '#2980b9')

        # تحديد العنوان بناءً على الحالة
        if lesson.status == 'live':
            title = f'🔴 LIVE - {lesson.student.first_name}'
        elif lesson.status == 'completed':
            title = f'✅ مباشرة - {lesson.student.first_name}'
        elif lesson.status == 'cancelled':
            title = f'❌ مباشرة - {lesson.student.first_name}'
        else:
            title = f'📹 مباشرة - {lesson.student.first_name}'

        events.append({
            'id': f'live_{lesson.id}',
            'title': title,
            'start': lesson.scheduled_date.isoformat() if lesson.scheduled_date else lesson.started_at.isoformat(),
            'end': ((lesson.scheduled_date or lesson.started_at) + timedelta(minutes=lesson.duration_minutes)).isoformat(),
            'color': color,
            'borderColor': color,
            'textColor': '#ffffff',
            'extendedProps': {
                'type': 'live',
                'lesson_id': lesson.id,
                'student_id': lesson.student.id,
                'student_name': lesson.student.get_full_name(),
                'status': lesson.status,
                'duration': lesson.duration_minutes,
                'description': lesson.description or '',
                'title': lesson.title
            }
        })

    return JsonResponse({
        'events': events,
        'total_events': len(events),
        'filters_applied': {
            'student_id': student_id,
            'plan_id': plan_id,
            'status': status_filter
        }
    })

@login_required
def student_subscriptions(request):
    """صفحة الاشتراكات والباقات للطالب"""
    if not request.user.is_student():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import SubscriptionPlan, StudentSubscription, SubscriptionPayment
    from django.utils import timezone
    from .payment_service import payment_service

    # جلب جميع الباقات النشطة
    available_plans = SubscriptionPlan.objects.filter(is_active=True).order_by('plan_type', 'price')

    # جلب اشتراكات الطالب
    student_subscriptions = StudentSubscription.objects.filter(
        student=request.user
    ).select_related('plan').order_by('-created_at')

    # الاشتراك الحالي (أولوية للنشط، ثم في انتظار الموافقة، ثم في انتظار الدفع، ثم الملغي)
    current_subscription = None

    # أولاً: البحث عن اشتراك نشط
    active_subscription = student_subscriptions.filter(
        status='active',
        end_date__gte=timezone.now().date()
    ).first()

    if active_subscription:
        current_subscription = active_subscription
    else:
        # ثانياً: البحث عن اشتراك في انتظار الموافقة
        pending_approval = student_subscriptions.filter(
            status='pending_approval'
        ).first()

        if pending_approval:
            current_subscription = pending_approval
        else:
            # ثالثاً: البحث عن اشتراك في انتظار الدفع
            payment_pending = student_subscriptions.filter(
                status='payment_pending'
            ).first()

            if payment_pending:
                current_subscription = payment_pending
            else:
                # رابعاً: البحث عن اشتراك ملغي (لإظهار رسالة الإلغاء)
                cancelled_subscription = student_subscriptions.filter(
                    status='cancelled'
                ).order_by('-updated_at').first()  # أحدث اشتراك ملغي

                if cancelled_subscription:
                    current_subscription = cancelled_subscription

    # تاريخ الدفعات
    payment_history = SubscriptionPayment.objects.filter(
        subscription__student=request.user
    ).select_related('subscription__plan').order_by('-payment_date')

    # الحصول على وسائل الدفع المتاحة
    available_payment_methods = payment_service.get_available_payment_methods()
    all_payment_methods = payment_service.get_all_payment_methods()

    context = {
        'available_plans': available_plans,
        'student_subscriptions': student_subscriptions,
        'current_subscription': current_subscription,
        'payment_history': payment_history,
        'available_payment_methods': available_payment_methods,
        'all_payment_methods': all_payment_methods,
    }

    return render(request, 'student/subscriptions.html', context)

@login_required
def subscribe_to_plan(request, plan_id):
    """اشتراك في باقة معينة"""
    if not request.user.is_student():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import SubscriptionPlan, StudentSubscription, SubscriptionPayment
    from django.shortcuts import get_object_or_404
    from django.utils import timezone
    from datetime import timedelta

    plan = get_object_or_404(SubscriptionPlan, id=plan_id, is_active=True)

    # التحقق من عدم وجود اشتراك نشط
    existing_active_subscription = StudentSubscription.objects.filter(
        student=request.user,
        status='active',
        end_date__gte=timezone.now().date()
    ).first()

    if existing_active_subscription and not existing_active_subscription.is_lessons_exhausted():
        messages.error(request, f"لديك اشتراك نشط بالفعل في باقة '{existing_active_subscription.plan.name}' مع {existing_active_subscription.remaining_lessons} حصة متبقية. لا يمكنك الاشتراك في باقة أخرى حتى انتهاء الحصص المتبقية.")
        return redirect('student_subscriptions')

    if request.method == 'POST':
        payment_method = request.POST.get('payment_method')

        # التحقق من تفعيل وسيلة الدفع المختارة
        from .payment_service import payment_service

        if not payment_service.is_payment_method_enabled(payment_method):
            error_message = payment_service.get_disabled_payment_message(payment_method)
            messages.error(request, error_message)
            return redirect('student_subscriptions')

        # إنشاء الاشتراك (في انتظار الدفع أولاً، ثم موافقة المدير)
        start_date = timezone.now().date()
        end_date = start_date + timedelta(days=plan.duration_days)

        subscription = StudentSubscription.objects.create(
            student=request.user,
            plan=plan,
            start_date=start_date,
            end_date=end_date,
            remaining_lessons=plan.lessons_count,
            amount_paid=plan.get_discounted_price(),
            status='payment_pending'  # في انتظار إكمال الدفع
        )

        # إرسال إشعار إنشاء الاشتراك
        from notifications.utils import SubscriptionNotificationService
        SubscriptionNotificationService.notify_subscription_created(subscription)

        # إنشاء دفعة
        payment = SubscriptionPayment.objects.create(
            subscription=subscription,
            amount=plan.get_discounted_price(),
            payment_method=payment_method,
            status='pending'
        )

        # توجيه حسب طريقة الدفع
        if payment_method == 'paypal':
            return redirect('paypal_payment', payment_id=payment.id)
        elif payment_method == 'stripe':
            return redirect('stripe_payment', payment_id=payment.id)
        elif payment_method == 'bank_transfer':
            return redirect('bank_transfer_payment', payment_id=payment.id)
        else:
            messages.error(request, "طريقة دفع غير صحيحة.")
            return redirect('student_subscriptions')

    context = {
        'plan': plan,
    }

    return render(request, 'student/subscribe_plan.html', context)

@login_required
def bank_transfer_payment(request, payment_id):
    """صفحة دفع التحويل البنكي"""
    if not request.user.is_student():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import SubscriptionPayment, BankTransferProof
    from django.shortcuts import get_object_or_404
    from .payment_service import payment_service

    # التحقق من تفعيل التحويل البنكي
    if not payment_service.is_bank_transfer_enabled():
        messages.error(request, "التحويل البنكي غير متاح في الوقت الحالي. يرجى اختيار وسيلة دفع أخرى.")
        return redirect('student_subscriptions')

    payment = get_object_or_404(
        SubscriptionPayment,
        id=payment_id,
        subscription__student=request.user,
        payment_method='bank_transfer',
        status='pending'
    )

    if request.method == 'POST':
        # رفع إثبات التحويل
        transfer_receipt = request.FILES.get('transfer_receipt')
        sender_name = request.POST.get('sender_name')
        transfer_amount = request.POST.get('transfer_amount')
        transfer_date = request.POST.get('transfer_date')
        bank_name = request.POST.get('bank_name')
        reference_number = request.POST.get('reference_number', '')

        if transfer_receipt and sender_name and transfer_amount and transfer_date and bank_name:
            BankTransferProof.objects.create(
                payment=payment,
                transfer_receipt=transfer_receipt,
                sender_name=sender_name,
                transfer_amount=transfer_amount,
                transfer_date=transfer_date,
                bank_name=bank_name,
                reference_number=reference_number
            )

            # تحديث حالة الدفع
            payment.status = 'processing'
            payment.save()

            # إرسال إشعار للمدير بالحوالة البنكية
            from notifications.utils import SubscriptionNotificationService
            SubscriptionNotificationService.notify_bank_transfer_submitted(payment.subscription)

            messages.success(request, "تم رفع إثبات التحويل بنجاح. سيتم مراجعته والموافقة عليه خلال 24 ساعة.")
            return redirect('student_subscriptions')
        else:
            messages.error(request, "يرجى ملء جميع الحقول المطلوبة ورفع إثبات التحويل.")

    # الحصول على بيانات البنك من الإعدادات
    bank_info = payment_service.get_bank_details()

    if not bank_info:
        messages.error(request, "إعدادات التحويل البنكي غير مكتملة. يرجى التواصل مع الإدارة.")
        return redirect('student_subscriptions')

    context = {
        'payment': payment,
        'bank_info': bank_info
    }

    return render(request, 'student/bank_transfer_payment.html', context)


@login_required
def paypal_payment(request, payment_id):
    """صفحة دفع PayPal"""
    if not request.user.is_student():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import SubscriptionPayment
    from django.shortcuts import get_object_or_404
    from .payment_service import payment_service
    from .models import PaymentGatewaySettings
    import json

    # التحقق من تفعيل PayPal
    if not payment_service.is_paypal_enabled():
        messages.error(request, "الدفع عن طريق PayPal غير متاح في الوقت الحالي. يرجى اختيار وسيلة دفع أخرى.")
        return redirect('student_subscriptions')

    payment = get_object_or_404(
        SubscriptionPayment,
        id=payment_id,
        subscription__student=request.user,
        payment_method='paypal',
        status='pending'
    )

    # الحصول على إعدادات PayPal
    paypal_settings = PaymentGatewaySettings.get_settings()

    if request.method == 'POST':
        # التحقق من نوع المحتوى
        if request.content_type == 'application/json':
            # معالجة PayPal SDK response
            try:
                data = json.loads(request.body)
                order_id = data.get('orderID')
                details = data.get('details')

                if order_id and details:
                    # تحديث معلومات الدفع
                    payment.status = 'completed'
                    payment.transaction_id = order_id
                    payment.payment_details = json.dumps(details)
                    payment.processed_at = timezone.now()
                    payment.save()

                    # تحديث حالة الاشتراك من payment_pending إلى pending_approval
                    if payment.subscription.status == 'payment_pending':
                        payment.subscription.status = 'pending_approval'
                        payment.subscription.save()

                    return JsonResponse({
                        'success': True,
                        'message': 'تم الدفع بنجاح عبر PayPal! سيتم تفعيل الاشتراك بعد موافقة المدير.'
                    })
                else:
                    return JsonResponse({
                        'success': False,
                        'message': 'بيانات الدفع غير مكتملة'
                    })

            except json.JSONDecodeError:
                return JsonResponse({
                    'success': False,
                    'message': 'بيانات غير صحيحة'
                })
        else:
            # معالجة النموذج التقليدي (fallback)
            payment.status = 'completed'
            payment.transaction_id = f"PAYPAL_{payment.id}_{timezone.now().strftime('%Y%m%d%H%M%S')}"
            payment.processed_at = timezone.now()
            payment.save()

            # تحديث حالة الاشتراك من payment_pending إلى pending_approval
            subscription = payment.subscription
            if subscription.status == 'payment_pending':
                subscription.status = 'pending_approval'
                subscription.save()

                # إرسال إشعار استلام الدفعة للطالب
                from subscriptions.email_service import SubscriptionEmailService
                email_service = SubscriptionEmailService()

                # إرسال إشعار للطالب أن الدفع تم وفي انتظار الموافقة
                email_service.send_payment_pending_approval(subscription)

                # إرسال إشعار بريد إلكتروني للمدير
                email_service.send_payment_received_to_admin(subscription)

            messages.success(request, "تم الدفع بنجاح عبر PayPal! سيتم تفعيل الاشتراك بعد موافقة المدير.")
            return redirect('student_subscriptions')

    context = {
        'payment': payment,
        'paypal_settings': paypal_settings,
    }

    return render(request, 'student/paypal_payment.html', context)


@login_required
def stripe_payment(request, payment_id):
    """صفحة دفع Stripe"""
    if not request.user.is_student():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import SubscriptionPayment
    from django.shortcuts import get_object_or_404
    from .payment_service import payment_service
    from .models import PaymentGatewaySettings
    import json

    # التحقق من تفعيل Stripe
    if not payment_service.is_stripe_enabled():
        messages.error(request, "الدفع عن طريق Stripe غير متاح في الوقت الحالي. يرجى اختيار وسيلة دفع أخرى.")
        return redirect('student_subscriptions')

    payment = get_object_or_404(
        SubscriptionPayment,
        id=payment_id,
        subscription__student=request.user,
        payment_method='stripe',
        status='pending'
    )

    # الحصول على إعدادات Stripe
    stripe_settings = PaymentGatewaySettings.get_settings()

    if request.method == 'POST':
        # التحقق من نوع المحتوى
        if request.content_type == 'application/json':
            # معالجة Stripe Elements response
            try:
                data = json.loads(request.body)
                payment_method_id = data.get('payment_method_id')
                amount = data.get('amount')
                currency = data.get('currency')

                if payment_method_id and amount:
                    try:
                        # تكامل حقيقي مع Stripe API
                        import stripe

                        # إعداد Stripe
                        stripe.api_key = stripe_settings.stripe_secret_key

                        # إنشاء Payment Intent
                        intent = stripe.PaymentIntent.create(
                            amount=int(float(amount) * 100),  # تحويل إلى سنت
                            currency=currency.lower(),
                            payment_method=payment_method_id,
                            confirmation_method='manual',
                            confirm=True,
                            return_url=request.build_absolute_uri('/dashboard/student/subscriptions/'),
                            metadata={
                                'payment_id': payment.id,
                                'student_id': request.user.id,
                                'subscription_id': payment.subscription.id,
                                'plan_name': payment.subscription.plan.name
                            }
                        )

                        # التحقق من حالة الدفع
                        if intent.status == 'succeeded':
                            # الدفع نجح
                            payment.status = 'completed'
                            payment.transaction_id = intent.id
                            payment.payment_details = json.dumps({
                                'payment_method_id': payment_method_id,
                                'payment_intent_id': intent.id,
                                'amount': amount,
                                'currency': currency,
                                'stripe_status': intent.status
                            })
                            payment.processed_at = timezone.now()
                            payment.save()

                            # تحديث حالة الاشتراك من payment_pending إلى pending_approval
                            if payment.subscription.status == 'payment_pending':
                                payment.subscription.status = 'pending_approval'
                                payment.subscription.save()

                            return JsonResponse({
                                'success': True,
                                'message': 'تم الدفع بنجاح عبر Stripe! سيتم تفعيل الاشتراك بعد موافقة المدير.',
                                'payment_id': payment.id,
                                'requires_action': False,
                                'redirect_url': f'/dashboard/student/payment/success/{payment.id}/'
                            })

                        elif intent.status == 'requires_action':
                            # يحتاج إلى 3D Secure
                            return JsonResponse({
                                'success': True,
                                'requires_action': True,
                                'payment_intent_client_secret': intent.client_secret,
                                'payment_id': payment.id
                            })

                        else:
                            # فشل الدفع
                            return JsonResponse({
                                'success': False,
                                'message': f'فشل في معالجة الدفع: {intent.status}'
                            })

                    except stripe.error.CardError as e:
                        # خطأ في البطاقة
                        return JsonResponse({
                            'success': False,
                            'message': f'خطأ في البطاقة: {e.user_message}'
                        })

                    except stripe.error.RateLimitError as e:
                        # تم تجاوز الحد المسموح
                        return JsonResponse({
                            'success': False,
                            'message': 'تم تجاوز الحد المسموح للطلبات. يرجى المحاولة لاحقاً.'
                        })

                    except stripe.error.InvalidRequestError as e:
                        # طلب غير صحيح
                        return JsonResponse({
                            'success': False,
                            'message': f'خطأ في الطلب: {str(e)}'
                        })

                    except stripe.error.AuthenticationError as e:
                        # خطأ في المصادقة
                        return JsonResponse({
                            'success': False,
                            'message': 'خطأ في إعدادات Stripe. يرجى التواصل مع الإدارة.'
                        })

                    except stripe.error.APIConnectionError as e:
                        # خطأ في الاتصال
                        return JsonResponse({
                            'success': False,
                            'message': 'خطأ في الاتصال بخدمة الدفع. يرجى المحاولة لاحقاً.'
                        })

                    except stripe.error.StripeError as e:
                        # خطأ عام من Stripe
                        return JsonResponse({
                            'success': False,
                            'message': f'خطأ في معالجة الدفع: {str(e)}'
                        })

                    except Exception as e:
                        # خطأ عام
                        return JsonResponse({
                            'success': False,
                            'message': f'حدث خطأ غير متوقع: {str(e)}'
                        })
                else:
                    return JsonResponse({
                        'success': False,
                        'message': 'بيانات الدفع غير مكتملة'
                    })

            except json.JSONDecodeError:
                return JsonResponse({
                    'success': False,
                    'message': 'بيانات غير صحيحة'
                })
        else:
            # معالجة النموذج التقليدي (fallback)
            payment.status = 'completed'
            payment.transaction_id = f"STRIPE_{payment.id}_{timezone.now().strftime('%Y%m%d%H%M%S')}"
            payment.processed_at = timezone.now()
            payment.save()

            # تحديث حالة الاشتراك من payment_pending إلى pending_approval
            subscription = payment.subscription
            if subscription.status == 'payment_pending':
                subscription.status = 'pending_approval'
                subscription.save()

                # إرسال إشعار استلام الدفعة للطالب
                from subscriptions.email_service import SubscriptionEmailService
                email_service = SubscriptionEmailService()

                # إرسال إشعار للطالب أن الدفع تم وفي انتظار الموافقة
                email_service.send_payment_pending_approval(subscription)

                # إرسال إشعار بريد إلكتروني للمدير
                email_service.send_payment_received_to_admin(subscription)

            messages.success(request, "تم الدفع بنجاح عبر Stripe! سيتم تفعيل الاشتراك بعد موافقة المدير.")
            return redirect('student_subscriptions')

    context = {
        'payment': payment,
        'stripe_settings': stripe_settings,
    }

    return render(request, 'student/stripe_payment_working.html', context)

@login_required
def student_lessons(request):
    """صفحة حصص الطالب مع التقويم والحصص المجدولة من الاشتراكات"""
    if not request.user.is_student():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from lessons.models import Lesson, LessonRating
    from subscriptions.models import StudentSubscription, ScheduledLesson
    from django.db.models import Q, Avg
    from django.utils import timezone

    # جلب الاشتراك النشط للطالب
    active_subscription = StudentSubscription.objects.filter(
        student=request.user,
        status='active'
    ).select_related('plan').first()

    # جلب الحصص المجدولة من الاشتراك
    scheduled_lessons = []
    subscription_stats = {}

    if active_subscription:
        scheduled_lessons = ScheduledLesson.objects.filter(
            subscription=active_subscription
        ).select_related('teacher').order_by('scheduled_date')

        # إحصائيات الاشتراك
        subscription_stats = {
            'total_lessons': scheduled_lessons.count(),
            'completed_lessons': scheduled_lessons.filter(status='completed').count(),
            'upcoming_lessons': scheduled_lessons.filter(
                status='scheduled',
                scheduled_date__gte=timezone.now()
            ).count(),
            'remaining_lessons': active_subscription.remaining_lessons,
            'subscription_end_date': active_subscription.end_date,
        }

    # جلب الحصص العادية (النظام القديم)
    regular_lessons = Lesson.objects.filter(
        student=request.user
    ).select_related('teacher').order_by('-scheduled_date')

    # فلترة الحصص
    status_filter = request.GET.get('status', '')
    if status_filter:
        regular_lessons = regular_lessons.filter(status=status_filter)
        if active_subscription:
            scheduled_lessons = scheduled_lessons.filter(status=status_filter)

    # الحصص القادمة (من الاشتراك والنظام العادي)
    upcoming_scheduled = []
    upcoming_regular = []

    if active_subscription:
        upcoming_scheduled = scheduled_lessons.filter(
            status='scheduled',
            scheduled_date__gte=timezone.now()
        )[:5]

    upcoming_regular = regular_lessons.filter(
        status='scheduled',
        scheduled_date__gte=timezone.now()
    )[:5]

    # الحصص المكتملة
    completed_scheduled = []
    completed_regular = regular_lessons.filter(status='completed')[:10]

    if active_subscription:
        completed_scheduled = scheduled_lessons.filter(status='completed')[:10]

    # إحصائيات عامة
    total_regular = regular_lessons.count()
    total_scheduled = scheduled_lessons.count() if active_subscription else 0

    general_stats = {
        'total_lessons': total_regular + total_scheduled,
        'completed_lessons': completed_regular.count() + (completed_scheduled.count() if active_subscription else 0),
        'upcoming_lessons': upcoming_regular.count() + (upcoming_scheduled.count() if active_subscription else 0),
        'cancelled_lessons': regular_lessons.filter(status='cancelled').count() +
                           (scheduled_lessons.filter(status='cancelled').count() if active_subscription else 0),
    }

    # متوسط التقييمات المعطاة
    avg_rating = LessonRating.objects.filter(
        student=request.user
    ).aggregate(avg=Avg('teacher_rating'))['avg'] or 0

    # جلب حصص اليوم والحصص القادمة والمكتملة
    today_lessons = []
    upcoming_lessons = []
    completed_lessons = []

    if active_subscription:
        from django.utils import timezone
        today = timezone.now().date()

        # حصص اليوم
        today_lessons = ScheduledLesson.objects.filter(
            subscription=active_subscription,
            scheduled_date__date=today
        ).select_related('teacher').order_by('scheduled_date')[:5]

        # الحصص القادمة (غير اليوم)
        upcoming_lessons = ScheduledLesson.objects.filter(
            subscription=active_subscription,
            scheduled_date__date__gt=today,
            status='scheduled'
        ).select_related('teacher').order_by('scheduled_date')[:5]

        # الحصص المكتملة مؤخراً مع التقييمات الموحدة
        completed_lessons = ScheduledLesson.objects.filter(
            subscription=active_subscription,
            status='completed'
        ).select_related('teacher').prefetch_related(
            'unified_ratings'
        ).order_by('-scheduled_date')[:5]

        # إضافة التقييمات الموحدة للحصص
        for lesson in completed_lessons:
            try:
                lesson.unified_rating = lesson.unified_ratings.filter(student=request.user).first()
            except:
                lesson.unified_rating = None

    context = {
        # الاشتراك والحصص المجدولة
        'active_subscription': active_subscription,
        'scheduled_lessons': scheduled_lessons,
        'subscription_stats': subscription_stats,
        'upcoming_scheduled': upcoming_scheduled,
        'completed_scheduled': completed_scheduled,

        # الحصص العادية
        'regular_lessons': regular_lessons[:20],
        'upcoming_regular': upcoming_regular,
        'completed_regular': completed_regular,

        # حصص اليوم والقادمة والمكتملة
        'today_lessons': today_lessons,
        'upcoming_lessons': upcoming_lessons,
        'completed_lessons': completed_lessons,

        # إحصائيات عامة
        'general_stats': general_stats,
        'avg_rating': round(avg_rating, 1),
        'status_filter': status_filter,
        'status_choices': Lesson.STATUS_CHOICES,
    }

    return render(request, 'student/lessons.html', context)





@login_required
def student_lessons_calendar_data(request):
    """API لجلب بيانات التقويم للطالب"""
    if not request.user.is_student():
        return JsonResponse({'error': 'غير مصرح'}, status=403)

    try:
        from subscriptions.models import StudentSubscription, ScheduledLesson
        from lessons.models import Lesson
        from django.utils import timezone
        from datetime import timedelta

        events = []

        # جلب الاشتراك النشط
        active_subscription = StudentSubscription.objects.filter(
            student=request.user,
            status='active'
        ).first()

        # Debug logging
        print(f"DEBUG: Student {request.user.id} ({request.user.get_full_name()}) active subscription: {active_subscription}")

        # إضافة الحصص المجدولة من الاشتراك
        if active_subscription:
            scheduled_lessons = ScheduledLesson.objects.filter(
                subscription=active_subscription
            ).select_related('teacher').order_by('scheduled_date')

            print(f"DEBUG: Found {scheduled_lessons.count()} scheduled lessons for subscription {active_subscription.id}")

            for lesson in scheduled_lessons:
                try:
                    # تحديد لون الحدث حسب الحالة
                    color = {
                        'scheduled': '#3498db',  # أزرق للمجدولة
                        'completed': '#27ae60',  # أخضر للمكتملة
                        'cancelled': '#e74c3c',  # أحمر للملغية
                        'rescheduled': '#f39c12',  # برتقالي للمعاد جدولتها
                        'no_show': '#95a5a6'  # رمادي للغياب
                    }.get(lesson.status, '#3498db')

                    teacher_name = lesson.teacher.get_full_name() if lesson.teacher else 'لم يتم تعيين معلم'

                    event_data = {
                        'id': f'scheduled_{lesson.id}',
                        'title': f'حصة رقم {lesson.lesson_number}',
                        'start': lesson.scheduled_date.isoformat(),
                        'end': (lesson.scheduled_date + timedelta(minutes=lesson.duration_minutes)).isoformat(),
                        'color': color,
                        'extendedProps': {
                            'type': 'scheduled_lesson',
                            'lesson_id': lesson.id,
                            'lesson_number': lesson.lesson_number,
                            'teacher_name': teacher_name,
                            'status': lesson.status,
                            'duration': lesson.duration_minutes,
                            'subscription_plan': active_subscription.plan.name
                        }
                    }

                    events.append(event_data)
                    print(f"DEBUG: Added event for lesson {lesson.id}: {event_data['title']} at {lesson.scheduled_date}")

                except Exception as lesson_error:
                    print(f"DEBUG: Error processing lesson {lesson.id}: {str(lesson_error)}")
                    continue

        else:
            print(f"DEBUG: No active subscription found for student {request.user.id}")

        print(f"DEBUG: Returning {len(events)} events")
        return JsonResponse({
            'events': events,
            'debug_info': {
                'student_id': request.user.id,
                'student_name': request.user.get_full_name(),
                'has_subscription': bool(active_subscription),
                'subscription_id': active_subscription.id if active_subscription else None,
                'events_count': len(events)
            }
        })

    except Exception as e:
        print(f"DEBUG: Error in student_lessons_calendar_data: {str(e)}")
        return JsonResponse({
            'error': f'خطأ في جلب بيانات التقويم: {str(e)}',
            'events': []
        }, status=500)


@login_required
def create_test_lessons(request):
    """إنشاء حصص تجريبية للاختبار"""
    if not request.user.is_admin():
        return JsonResponse({'error': 'غير مصرح'}, status=403)

    try:
        from subscriptions.models import StudentSubscription, ScheduledLesson, SubscriptionPlan
        from django.utils import timezone
        from datetime import datetime, timedelta

        # البحث عن اشتراك نشط
        active_subscription = StudentSubscription.objects.filter(status='active').first()

        if not active_subscription:
            # إنشاء باقة تجريبية
            plan, created = SubscriptionPlan.objects.get_or_create(
                name='باقة تجريبية',
                defaults={
                    'description': 'باقة للاختبار',
                    'lessons_count': 5,
                    'lesson_duration': 45,
                    'price_usd': 50.0,
                    'duration_type': 'monthly',
                    'is_active': True
                }
            )

            # إنشاء اشتراك تجريبي
            student = User.objects.filter(user_type='student').first()
            if not student:
                return JsonResponse({'error': 'لا يوجد طلاب في النظام'}, status=400)

            active_subscription = StudentSubscription.objects.create(
                student=student,
                plan=plan,
                status='active',
                start_date=timezone.now().date(),
                end_date=(timezone.now() + timedelta(days=30)).date(),
                amount_paid=plan.price_usd
            )

        # حذف الحصص الموجودة
        ScheduledLesson.objects.filter(subscription=active_subscription).delete()

        # إنشاء حصص تجريبية
        teacher = User.objects.filter(user_type='teacher').first()
        if not teacher:
            return JsonResponse({'error': 'لا يوجد معلمين في النظام'}, status=400)

        lessons_created = []
        base_date = timezone.now() + timedelta(days=1)  # ابدأ من غداً

        for i in range(5):
            lesson_date = base_date + timedelta(days=i*2)  # كل يومين
            lesson_date = lesson_date.replace(hour=10, minute=0, second=0, microsecond=0)

            # تجنب عطلة نهاية الأسبوع
            while lesson_date.weekday() in [4, 5]:  # الجمعة والسبت
                lesson_date += timedelta(days=1)

            lesson = ScheduledLesson.objects.create(
                subscription=active_subscription,
                lesson_number=i+1,
                scheduled_date=lesson_date,
                duration_minutes=45,
                teacher=teacher,
                status='scheduled'
            )
            lessons_created.append(lesson)

        return JsonResponse({
            'success': True,
            'message': f'تم إنشاء {len(lessons_created)} حصة تجريبية',
            'subscription_id': active_subscription.id,
            'student': active_subscription.student.get_full_name(),
            'lessons': [
                {
                    'id': lesson.id,
                    'number': lesson.lesson_number,
                    'date': lesson.scheduled_date.isoformat(),
                    'teacher': lesson.teacher.get_full_name()
                }
                for lesson in lessons_created
            ]
        })

    except Exception as e:
        return JsonResponse({
            'error': f'خطأ في إنشاء الحصص التجريبية: {str(e)}'
        }, status=500)


@login_required
def delete_test_lessons(request):
    """حذف جميع الحصص التجريبية"""
    if not request.user.is_admin():
        return JsonResponse({'error': 'غير مصرح'}, status=403)

    try:
        from subscriptions.models import StudentSubscription, ScheduledLesson, SubscriptionPlan

        # حذف جميع الحصص المجدولة
        deleted_lessons = ScheduledLesson.objects.all().delete()

        # حذف الباقات التجريبية
        deleted_plans = SubscriptionPlan.objects.filter(name__icontains='تجريبية').delete()

        # حذف الاشتراكات التجريبية
        deleted_subscriptions = StudentSubscription.objects.filter(
            plan__name__icontains='تجريبية'
        ).delete()

        return JsonResponse({
            'success': True,
            'message': 'تم حذف جميع البيانات التجريبية بنجاح',
            'deleted': {
                'lessons': deleted_lessons[0] if deleted_lessons[0] else 0,
                'plans': deleted_plans[0] if deleted_plans[0] else 0,
                'subscriptions': deleted_subscriptions[0] if deleted_subscriptions[0] else 0
            }
        })

    except Exception as e:
        return JsonResponse({
            'error': f'خطأ في حذف البيانات التجريبية: {str(e)}'
        }, status=500)


@login_required
def create_test_completed_lessons(request):
    """إنشاء حصص مكتملة تجريبية للاختبار"""
    if not request.user.is_admin():
        return JsonResponse({'error': 'غير مصرح'}, status=403)

    try:
        from subscriptions.models import StudentSubscription, ScheduledLesson, SubscriptionPlan
        from django.utils import timezone
        from datetime import datetime, timedelta

        # البحث عن اشتراك نشط أو إنشاء واحد
        active_subscription = StudentSubscription.objects.filter(status='active').first()

        if not active_subscription:
            # إنشاء باقة تجريبية
            plan, created = SubscriptionPlan.objects.get_or_create(
                name='باقة تجريبية',
                defaults={
                    'description': 'باقة للاختبار',
                    'lessons_count': 10,
                    'lesson_duration': 45,
                    'price_usd': 100.0,
                    'duration_type': 'monthly',
                    'is_active': True
                }
            )

            # إنشاء اشتراك تجريبي
            student = User.objects.filter(user_type='student').first()
            if not student:
                return JsonResponse({'error': 'لا يوجد طلاب في النظام'}, status=400)

            active_subscription = StudentSubscription.objects.create(
                student=student,
                plan=plan,
                status='active',
                start_date=timezone.now().date(),
                end_date=(timezone.now() + timedelta(days=30)).date(),
                amount_paid=plan.price_usd
            )

        # إنشاء حصص مكتملة
        teacher = User.objects.filter(user_type='teacher').first()
        if not teacher:
            return JsonResponse({'error': 'لا يوجد معلمين في النظام'}, status=400)

        lessons_created = []
        base_date = timezone.now() - timedelta(days=7)  # ابدأ من أسبوع مضى

        for i in range(3):
            lesson_date = base_date + timedelta(days=i*2)  # كل يومين
            lesson_date = lesson_date.replace(hour=10, minute=0, second=0, microsecond=0)

            lesson = ScheduledLesson.objects.create(
                subscription=active_subscription,
                lesson_number=i+1,
                scheduled_date=lesson_date,
                duration_minutes=45,
                teacher=teacher,
                status='completed'  # حالة مكتملة
            )
            lessons_created.append(lesson)

        return JsonResponse({
            'success': True,
            'message': f'تم إنشاء {len(lessons_created)} حصة مكتملة تجريبية',
            'subscription_id': active_subscription.id,
            'student': active_subscription.student.get_full_name(),
            'lessons': [
                {
                    'id': lesson.id,
                    'number': lesson.lesson_number,
                    'date': lesson.scheduled_date.isoformat(),
                    'teacher': lesson.teacher.get_full_name(),
                    'status': lesson.status
                }
                for lesson in lessons_created
            ]
        })

    except Exception as e:
        return JsonResponse({
            'error': f'خطأ في إنشاء الحصص المكتملة التجريبية: {str(e)}'
        }, status=500)


@login_required
def submit_lesson_rating(request):
    """إرسال تقييم الحصة من الطالب"""
    if not request.user.is_student():
        return JsonResponse({'error': 'غير مصرح'}, status=403)

    if request.method != 'POST':
        return JsonResponse({'error': 'طريقة غير مدعومة'}, status=405)

    try:
        import json
        from subscriptions.models import ScheduledLesson
        from lessons.models import LessonRating

        data = json.loads(request.body)
        lesson_id = data.get('lesson_id')
        teacher_rating = data.get('teacher_rating')
        comment = data.get('comment', '').strip()

        # التحقق من صحة البيانات
        if not lesson_id:
            return JsonResponse({'error': 'معرف الحصة مطلوب'}, status=400)

        if not teacher_rating or teacher_rating < 1 or teacher_rating > 5:
            return JsonResponse({'error': 'تقييم المعلم يجب أن يكون بين 1 و 5'}, status=400)

        # التحقق من وجود الحصة وأنها تخص الطالب
        try:
            lesson = ScheduledLesson.objects.get(
                id=lesson_id,
                subscription__student=request.user,
                status='completed'
            )
        except ScheduledLesson.DoesNotExist:
            return JsonResponse({'error': 'الحصة غير موجودة أو غير مكتملة'}, status=404)

        # التحقق من وجود معلم للحصة
        if not lesson.teacher:
            return JsonResponse({'error': 'لا يمكن تقييم حصة بدون معلم'}, status=400)

        # إنشاء أو تحديث التقييم
        rating, created = LessonRating.objects.update_or_create(
            lesson_id=lesson_id,  # استخدام lesson_id بدلاً من lesson
            student=request.user,
            teacher=lesson.teacher,
            defaults={
                'teacher_rating': teacher_rating,
                'comment': comment
            }
        )

        action = 'تم إنشاء' if created else 'تم تحديث'

        return JsonResponse({
            'success': True,
            'message': f'{action} التقييم بنجاح',
            'rating_id': rating.id,
            'teacher_rating': rating.teacher_rating,
            'created': created
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'بيانات JSON غير صحيحة'}, status=400)
    except Exception as e:
        return JsonResponse({
            'error': f'خطأ في إرسال التقييم: {str(e)}'
        }, status=500)


@login_required
def submit_unified_rating(request):
    """API موحد لتقييم الحصص المجدولة والمباشرة"""
    if not request.user.is_student():
        return JsonResponse({'error': 'غير مصرح'}, status=403)

    if request.method != 'POST':
        return JsonResponse({'error': 'طريقة غير مدعومة'}, status=405)

    try:
        import json
        from subscriptions.models import ScheduledLesson
        from lessons.models import LiveLesson, UnifiedLessonRating

        data = json.loads(request.body)
        lesson_type = data.get('lesson_type')  # 'scheduled' أو 'live'
        lesson_id = data.get('lesson_id')

        # التحقق من صحة البيانات الأساسية
        if not lesson_type or lesson_type not in ['scheduled', 'live']:
            return JsonResponse({'error': 'نوع الحصة غير صحيح'}, status=400)

        if not lesson_id:
            return JsonResponse({'error': 'معرف الحصة مطلوب'}, status=400)

        # التحقق من التقييمات
        required_ratings = ['overall_rating', 'lesson_quality', 'teacher_interaction', 'technical_quality']
        for rating_field in required_ratings:
            rating_value = data.get(rating_field)
            if not rating_value or rating_value < 1 or rating_value > 5:
                return JsonResponse({'error': f'تقييم {rating_field} يجب أن يكون بين 1 و 5'}, status=400)

        # جلب الحصة حسب النوع
        lesson = None
        teacher = None

        if lesson_type == 'scheduled':
            try:
                lesson = ScheduledLesson.objects.get(
                    id=lesson_id,
                    subscription__student=request.user,
                    status='completed'
                )
                teacher = lesson.teacher

                if not teacher:
                    return JsonResponse({'error': 'لا يمكن تقييم حصة بدون معلم'}, status=400)

            except ScheduledLesson.DoesNotExist:
                return JsonResponse({'error': 'الحصة المجدولة غير موجودة أو غير مكتملة'}, status=404)

        elif lesson_type == 'live':
            try:
                lesson = LiveLesson.objects.get(
                    id=lesson_id,
                    student=request.user,
                    status='ended'
                )
                teacher = lesson.teacher

            except LiveLesson.DoesNotExist:
                return JsonResponse({'error': 'الحصة المباشرة غير موجودة أو لم تنته بعد'}, status=404)

        # التحقق من عدم وجود تقييم سابق
        existing_rating = UnifiedLessonRating.objects.filter(
            student=request.user,
            **{f'{lesson_type}_lesson': lesson}
        ).first()

        if existing_rating:
            # تحديث التقييم الموجود
            existing_rating.overall_rating = data['overall_rating']
            existing_rating.lesson_quality = data['lesson_quality']
            existing_rating.teacher_interaction = data['teacher_interaction']
            existing_rating.technical_quality = data['technical_quality']
            existing_rating.comment = data.get('comment', '').strip()
            existing_rating.save()

            return JsonResponse({
                'success': True,
                'message': 'تم تحديث التقييم بنجاح',
                'rating_id': existing_rating.id,
                'average_rating': existing_rating.average_rating,
                'action': 'updated'
            })
        else:
            # إنشاء تقييم جديد
            rating_data = {
                'student': request.user,
                'teacher': teacher,
                'lesson_type': lesson_type,
                'overall_rating': data['overall_rating'],
                'lesson_quality': data['lesson_quality'],
                'teacher_interaction': data['teacher_interaction'],
                'technical_quality': data['technical_quality'],
                'comment': data.get('comment', '').strip()
            }

            # إضافة الحصة المناسبة
            if lesson_type == 'scheduled':
                rating_data['scheduled_lesson'] = lesson
            else:
                rating_data['live_lesson'] = lesson

            rating = UnifiedLessonRating.objects.create(**rating_data)

            return JsonResponse({
                'success': True,
                'message': 'تم إرسال التقييم بنجاح',
                'rating_id': rating.id,
                'average_rating': rating.average_rating,
                'action': 'created'
            })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'بيانات JSON غير صحيحة'}, status=400)
    except Exception as e:
        return JsonResponse({
            'error': f'خطأ في إرسال التقييم: {str(e)}'
        }, status=500)


@login_required
def admin_manage_lesson_schedules(request):
    """إدارة جدولة الحصص للمدير"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import StudentSubscription, ScheduledLesson
    from subscriptions.services import LessonSchedulingService
    from django.db.models import Count, Q
    from django.utils import timezone

    # جلب جميع الاشتراكات النشطة
    active_subscriptions = StudentSubscription.objects.filter(
        status='active'
    ).select_related('student', 'plan').annotate(
        total_scheduled_lessons=Count('scheduled_lessons'),
        completed_lessons=Count('scheduled_lessons', filter=Q(scheduled_lessons__status='completed')),
        upcoming_lessons=Count('scheduled_lessons', filter=Q(
            scheduled_lessons__status='scheduled',
            scheduled_lessons__scheduled_date__gte=timezone.now()
        ))
    ).order_by('-created_at')

    # معالجة الطلبات
    if request.method == 'POST':
        action = request.POST.get('action')
        subscription_id = request.POST.get('subscription_id')

        try:
            subscription = StudentSubscription.objects.get(id=subscription_id, status='active')

            if action == 'regenerate_schedule':
                # إعادة إنشاء الجدولة
                success, message = LessonSchedulingService.create_automatic_schedule(subscription)
                if success:
                    messages.success(request, f"تم إعادة إنشاء جدولة حصص {subscription.student.get_full_name()} بنجاح!")
                else:
                    messages.error(request, f"فشل في إعادة إنشاء الجدولة: {message}")

            elif action == 'assign_teacher':
                teacher_id = request.POST.get('teacher_id')
                if teacher_id:
                    from django.contrib.auth import get_user_model
                    User = get_user_model()
                    teacher = User.objects.get(id=teacher_id, user_type='teacher')
                    success, message = LessonSchedulingService.assign_teacher_to_lessons(subscription, teacher)
                    if success:
                        messages.success(request, f"تم تعيين المعلم {teacher.get_full_name()} لحصص {subscription.student.get_full_name()}")
                    else:
                        messages.error(request, f"فشل في تعيين المعلم: {message}")

        except Exception as e:
            messages.error(request, f"حدث خطأ: {str(e)}")

        return redirect('admin_manage_lesson_schedules')

    # المعلمين المتاحين
    available_teachers = LessonSchedulingService.get_available_teachers()

    context = {
        'active_subscriptions': active_subscriptions,
        'available_teachers': available_teachers,
    }

    return render(request, 'admin/lesson_schedules.html', context)


@login_required
def admin_save_scheduled_lessons(request, subscription_id):
    """حفظ الحصص المجدولة من التقويم"""
    if not request.user.is_admin():
        return JsonResponse({'error': 'غير مصرح'}, status=403)

    if request.method != 'POST':
        return JsonResponse({'error': 'طريقة غير مدعومة'}, status=405)

    try:
        from subscriptions.models import StudentSubscription, ScheduledLesson
        from django.utils import timezone
        from datetime import datetime
        import json

        subscription = get_object_or_404(StudentSubscription, id=subscription_id)

        # جلب البيانات من الطلب
        data = json.loads(request.body)
        lessons_data = data.get('lessons', [])
        teacher_id = data.get('teacher_id')
        operation_type = data.get('operation_type', 'schedule')
        force_schedule = data.get('force_schedule', False)  # للتأكيد على الجدولة رغم التحذير

        # التحقق من حالة الاشتراك أولاً
        if subscription.status != 'active':
            return JsonResponse({
                'error': 'subscription_not_active',
                'message': f'تحذير: اشتراك الطالب {subscription.student.get_full_name()} غير نشط. حالة الاشتراك: {subscription.get_status_display()}',
                'student_name': subscription.student.get_full_name(),
                'plan_name': subscription.plan.name,
                'subscription_status': subscription.get_status_display(),
                'subscription_end_date': subscription.end_date.strftime('%Y-%m-%d'),
                'requires_confirmation': False
            }, status=400)

        # التحقق من انتهاء الاشتراك بالتاريخ
        if subscription.end_date < timezone.now().date():
            return JsonResponse({
                'error': 'subscription_expired',
                'message': f'تحذير: اشتراك الطالب {subscription.student.get_full_name()} منتهي الصلاحية. انتهى في {subscription.end_date.strftime("%Y-%m-%d")}',
                'student_name': subscription.student.get_full_name(),
                'plan_name': subscription.plan.name,
                'subscription_end_date': subscription.end_date.strftime('%Y-%m-%d'),
                'requires_confirmation': False
            }, status=400)

        # التحقق من حالة الاشتراك والحصص المتبقية (فقط إذا كانت هناك حصص للجدولة)
        if len(lessons_data) > 0 and subscription.is_lessons_exhausted() and not force_schedule:
            return JsonResponse({
                'error': 'lessons_exhausted',
                'message': f'تحذير: الطالب {subscription.student.get_full_name()} قد أكمل جميع حصص باقته ({subscription.plan.lessons_count} حصة). لا توجد حصص متبقية في الاشتراك الحالي.',
                'student_name': subscription.student.get_full_name(),
                'plan_name': subscription.plan.name,
                'total_lessons': subscription.plan.lessons_count,
                'remaining_lessons': subscription.remaining_lessons,
                'completed_lessons': subscription.get_completed_lessons_count(),
                'subscription_end_date': subscription.end_date.strftime('%Y-%m-%d'),
                'requires_confirmation': True
            }, status=400)

        # تحذير إضافي إذا كانت الحصص المتبقية قليلة (أقل من عدد الحصص المراد جدولتها)
        if len(lessons_data) > subscription.remaining_lessons and not force_schedule:
            return JsonResponse({
                'error': 'insufficient_lessons',
                'message': f'تحذير: تحاول جدولة {len(lessons_data)} حصة لكن الطالب {subscription.student.get_full_name()} لديه {subscription.remaining_lessons} حصة متبقية فقط في باقته.',
                'student_name': subscription.student.get_full_name(),
                'plan_name': subscription.plan.name,
                'lessons_to_schedule': len(lessons_data),
                'remaining_lessons': subscription.remaining_lessons,
                'requires_confirmation': True
            }, status=400)

        # حذف الحصص الموجودة أولاً
        deleted_count = ScheduledLesson.objects.filter(subscription=subscription).count()
        ScheduledLesson.objects.filter(subscription=subscription).delete()

        # إذا كانت العملية مسح فقط
        if operation_type == 'clear' or len(lessons_data) == 0:
            return JsonResponse({
                'success': True,
                'message': f'تم مسح {deleted_count} حصة بنجاح',
                'deleted_count': deleted_count,
                'lessons_count': 0,
                'subscription_id': subscription.id,
                'student_name': subscription.student.get_full_name(),
                'operation_type': 'clear'
            })

        # التحقق من المعلم فقط إذا كانت هناك حصص للإنشاء
        teacher = None
        if teacher_id:
            teacher = get_object_or_404(User, id=teacher_id, user_type='teacher')

        # إنشاء الحصص الجديدة
        created_lessons = []
        for lesson_data in lessons_data:
            lesson_datetime = datetime.fromisoformat(lesson_data['datetime'].replace('Z', '+00:00'))

            # التأكد من أن التاريخ timezone-aware
            if timezone.is_naive(lesson_datetime):
                lesson_datetime = timezone.make_aware(lesson_datetime)

            lesson = ScheduledLesson.objects.create(
                subscription=subscription,
                lesson_number=lesson_data['number'],
                scheduled_date=lesson_datetime,
                duration_minutes=subscription.plan.lesson_duration,
                teacher=teacher,  # يمكن أن يكون None
                status='scheduled'
            )
            created_lessons.append(lesson)

        return JsonResponse({
            'success': True,
            'message': f'تم حفظ {len(created_lessons)} حصة بنجاح',
            'lessons_count': len(created_lessons),
            'deleted_count': deleted_count,
            'subscription_id': subscription.id,
            'student_name': subscription.student.get_full_name(),
            'teacher_name': teacher.get_full_name() if teacher else 'لم يتم تعيين معلم',
            'operation_type': operation_type
        })

    except Exception as e:
        return JsonResponse({
            'error': f'خطأ في حفظ الحصص: {str(e)}'
        }, status=500)


@login_required
def admin_manual_schedule_lessons(request, subscription_id):
    """الجدولة اليدوية للحصص من قبل المدير"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import StudentSubscription, ScheduledLesson
    from subscriptions.services import LessonSchedulingService
    from django.shortcuts import get_object_or_404
    from django.utils import timezone
    from datetime import datetime, timedelta
    import json

    subscription = get_object_or_404(StudentSubscription, id=subscription_id, status='active')

    # معالجة حفظ الجدولة اليدوية
    if request.method == 'POST':
        try:
            lessons_data_raw = request.POST.get('lessons_data', '[]')
            lessons_data = json.loads(lessons_data_raw)
            teacher_id = request.POST.get('teacher_id')

            # Debug logging
            print(f"DEBUG: Received lessons_data: {lessons_data}")
            print(f"DEBUG: Teacher ID: {teacher_id}")
            print(f"DEBUG: Number of lessons: {len(lessons_data)}")

            if not teacher_id:
                messages.error(request, "يجب اختيار معلم للحصص")
                return redirect('admin_manual_schedule_lessons', subscription_id=subscription_id)

            if not lessons_data:
                messages.error(request, "لم يتم العثور على بيانات الحصص. يرجى إضافة حصص أولاً.")
                return redirect('admin_manual_schedule_lessons', subscription_id=subscription_id)

            # التحقق من المعلم
            from django.contrib.auth import get_user_model
            User = get_user_model()
            teacher = User.objects.get(id=teacher_id, user_type='teacher')

            # حذف الحصص المجدولة السابقة
            ScheduledLesson.objects.filter(subscription=subscription).delete()

            # إنشاء الحصص الجديدة
            created_lessons = []
            for i, lesson_data in enumerate(lessons_data, 1):
                try:
                    # معالجة التاريخ والوقت
                    lesson_datetime_str = lesson_data['datetime']
                    if lesson_datetime_str.endswith('Z'):
                        lesson_datetime_str = lesson_datetime_str[:-1] + '+00:00'

                    lesson_datetime = datetime.fromisoformat(lesson_datetime_str)

                    # التأكد من أن التاريخ timezone-aware
                    if timezone.is_naive(lesson_datetime):
                        lesson_datetime = timezone.make_aware(lesson_datetime)

                    # إنشاء الحصة المجدولة
                    scheduled_lesson = ScheduledLesson.objects.create(
                        subscription=subscription,
                        lesson_number=i,
                        scheduled_date=lesson_datetime,
                        duration_minutes=subscription.plan.lesson_duration,
                        teacher=teacher,
                        status='scheduled'
                    )
                    created_lessons.append(scheduled_lesson)

                    # Debug logging
                    print(f"DEBUG: Created lesson {i}: ID={scheduled_lesson.id}, Date={lesson_datetime}, Student={subscription.student.get_full_name()}, Teacher={teacher.get_full_name()}")

                    # Verify the lesson was saved
                    saved_lesson = ScheduledLesson.objects.get(id=scheduled_lesson.id)
                    print(f"DEBUG: Verified lesson {i} saved: {saved_lesson.scheduled_date}")

                except Exception as lesson_error:
                    messages.error(request, f"خطأ في إنشاء الحصة رقم {i}: {str(lesson_error)}")
                    continue

            if created_lessons:
                # لا نقوم بتفعيل الاشتراك تلقائياً - يجب أن يكون معتمد من المدير مسبقاً
                # إذا كان الاشتراك غير نشط، نعرض تحذير
                if subscription.status != 'active':
                    messages.warning(request, f"تم إنشاء {len(created_lessons)} حصة بنجاح، لكن الاشتراك ليس نشطاً. يرجى تفعيل الاشتراك أولاً.")
                    print(f"DEBUG: Created lessons for inactive subscription {subscription.id} with status: {subscription.status}")
                else:
                    messages.success(request, f"تم إنشاء {len(created_lessons)} حصة بنجاح للطالب {subscription.student.get_full_name()}")
                    print(f"DEBUG: Successfully created {len(created_lessons)} lessons for active subscription {subscription.id}")
            else:
                messages.error(request, "لم يتم إنشاء أي حصص. يرجى المحاولة مرة أخرى.")

            return redirect('admin_subscriptions_list')

        except Exception as e:
            messages.error(request, f"حدث خطأ أثناء إنشاء الجدولة: {str(e)}")

    # جلب المعلمين المتاحين
    available_teachers = LessonSchedulingService.get_available_teachers()

    # جلب الحصص المجدولة الحالية (إن وجدت)
    existing_lessons = ScheduledLesson.objects.filter(subscription=subscription).order_by('lesson_number')

    context = {
        'subscription': subscription,
        'available_teachers': available_teachers,
        'existing_lessons': existing_lessons,
        'plan': subscription.plan,
        'today': timezone.now().date(),
    }

    return render(request, 'admin/manual_schedule_lessons.html', context)


@login_required
def admin_get_subscription_schedule_data(request, subscription_id):
    """API لجلب بيانات الحصص المجدولة للاشتراك"""
    if not request.user.is_admin():
        return JsonResponse({'error': 'غير مصرح'}, status=403)

    from subscriptions.models import StudentSubscription, ScheduledLesson
    from django.shortcuts import get_object_or_404
    from datetime import timedelta

    subscription = get_object_or_404(StudentSubscription, id=subscription_id)
    lessons = ScheduledLesson.objects.filter(subscription=subscription)

    events = []
    for lesson in lessons:
        events.append({
            'id': lesson.id,
            'title': f'حصة رقم {lesson.lesson_number}',
            'start': lesson.scheduled_date.isoformat(),
            'end': (lesson.scheduled_date + timedelta(minutes=lesson.duration_minutes)).isoformat(),
            'color': {
                'scheduled': '#3498db',
                'completed': '#27ae60',
                'cancelled': '#e74c3c',
                'rescheduled': '#f39c12',
                'no_show': '#95a5a6'
            }.get(lesson.status, '#3498db'),
            'extendedProps': {
                'lesson_number': lesson.lesson_number,
                'teacher_name': lesson.teacher.get_full_name() if lesson.teacher else 'لم يتم تعيين معلم',
                'status': lesson.status,
                'duration': lesson.duration_minutes
            }
        })

    return JsonResponse({'events': events})


@login_required
def admin_lesson_report_view(request, lesson_id):
    """عرض تقرير المعلم عن الحصة للمدير"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from lessons.models import LiveLesson, TeacherLessonReport
    from django.shortcuts import get_object_or_404

    lesson = get_object_or_404(LiveLesson, id=lesson_id)

    try:
        report = lesson.teacher_report
        context = {
            'lesson': lesson,
            'report': report,
        }
        return render(request, 'admin/lesson_report_view.html', context)
    except TeacherLessonReport.DoesNotExist:
        messages.error(request, "لم يتم إنشاء تقرير لهذه الحصة بعد.")
        return redirect('admin_lessons')


@login_required
def student_invoice_view(request, invoice_id):
    """عرض الفاتورة للطالب"""
    if not request.user.is_student():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.invoice_models import Invoice
    from subscriptions.invoice_utils import get_invoice_context
    from django.shortcuts import get_object_or_404

    # التأكد من أن الفاتورة تخص الطالب
    invoice = get_object_or_404(
        Invoice,
        id=invoice_id,
        subscription__student=request.user
    )

    context = get_invoice_context(invoice)
    return render(request, 'student/invoice_view.html', context)


@login_required
def student_invoice_print(request, invoice_id):
    """طباعة الفاتورة للطالب"""
    if not request.user.is_student():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.invoice_models import Invoice
    from subscriptions.invoice_utils import get_invoice_context
    from django.shortcuts import get_object_or_404

    # التأكد من أن الفاتورة تخص الطالب
    invoice = get_object_or_404(
        Invoice,
        id=invoice_id,
        subscription__student=request.user
    )

    context = get_invoice_context(invoice)
    return render(request, 'student/invoice_print.html', context)


@login_required
def student_invoice_pdf(request, invoice_id):
    """تحميل الفاتورة كـ PDF للطالب"""
    if not request.user.is_student():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.invoice_models import Invoice
    from subscriptions.invoice_utils import get_invoice_context
    from django.shortcuts import get_object_or_404
    from django.http import HttpResponse
    from django.template.loader import render_to_string

    try:
        # محاولة استيراد مكتبة PDF
        from weasyprint import HTML, CSS
        from django.conf import settings
        import os

        # التأكد من أن الفاتورة تخص الطالب
        invoice = get_object_or_404(
            Invoice,
            id=invoice_id,
            subscription__student=request.user
        )

        context = get_invoice_context(invoice)

        # إنشاء HTML للفاتورة
        html_string = render_to_string('student/invoice_pdf.html', context)

        # تحويل HTML إلى PDF
        html = HTML(string=html_string)
        pdf = html.write_pdf()

        # إرجاع PDF كاستجابة
        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="invoice_{invoice.invoice_number}.pdf"'
        return response

    except ImportError:
        # في حالة عدم توفر مكتبة PDF، إرجاع رسالة خطأ
        messages.error(request, "عذراً، خدمة تحميل PDF غير متوفرة حالياً.")
        return redirect('student_invoice_view', invoice_id=invoice_id)
    except Exception as e:
        messages.error(request, f"حدث خطأ أثناء إنشاء الفاتورة: {str(e)}")
        return redirect('student_invoice_view', invoice_id=invoice_id)


# ===============================
# Admin Subscriptions Management Views
# ===============================

@login_required
def admin_subscriptions(request):
    """صفحة إدارة الاشتراكات والباقات للمدير"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import SubscriptionPlan, StudentSubscription, SubscriptionPayment, BankTransferProof
    from django.db.models import Count, Sum, Avg
    from django.utils import timezone

    # إحصائيات عامة
    total_plans = SubscriptionPlan.objects.count()
    active_plans = SubscriptionPlan.objects.filter(is_active=True).count()
    total_subscriptions = StudentSubscription.objects.count()
    active_subscriptions = StudentSubscription.objects.filter(status='active').count()

    # إحصائيات مالية
    total_revenue = SubscriptionPayment.objects.filter(status='completed').aggregate(
        total=Sum('amount')
    )['total'] or 0

    pending_payments = SubscriptionPayment.objects.filter(status='pending').count()

    # الباقات مع إحصائيات
    plans_with_stats = []
    for plan in SubscriptionPlan.objects.all():
        plan_subscriptions = StudentSubscription.objects.filter(plan=plan)
        plan_revenue = SubscriptionPayment.objects.filter(
            subscription__plan=plan,
            status='completed'
        ).aggregate(total=Sum('amount'))['total'] or 0

        plans_with_stats.append({
            'plan': plan,
            'total_subscriptions': plan_subscriptions.count(),
            'active_subscriptions': plan_subscriptions.filter(status='active').count(),
            'revenue': plan_revenue
        })

    # أحدث الاشتراكات
    recent_subscriptions = StudentSubscription.objects.select_related(
        'student', 'plan'
    ).order_by('-created_at')[:10]

    # الاشتراكات المعلقة التي تحتاج موافقة
    pending_approvals = StudentSubscription.objects.filter(
        status='pending_approval'
    ).select_related('student', 'plan').order_by('-created_at')[:10]

    # التحويلات البنكية المعلقة
    pending_transfers = BankTransferProof.objects.filter(
        is_verified=False
    ).select_related('payment__subscription__student', 'payment__subscription__plan')[:5]

    context = {
        'total_plans': total_plans,
        'active_plans': active_plans,
        'total_subscriptions': total_subscriptions,
        'active_subscriptions': active_subscriptions,
        'total_revenue': total_revenue,
        'pending_payments': pending_payments,
        'plans_with_stats': plans_with_stats,
        'recent_subscriptions': recent_subscriptions,
        'pending_approvals': pending_approvals,
        'pending_transfers': pending_transfers,
    }

    return render(request, 'admin/subscriptions/dashboard.html', context)


@login_required
def admin_plans_list(request):
    """صفحة قائمة الباقات للمدير"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import SubscriptionPlan, StudentSubscription, SubscriptionPayment
    from django.db.models import Count, Sum, Q, Avg

    plans = SubscriptionPlan.objects.annotate(
        subscriptions_count=Count('subscriptions'),
        active_subscriptions_count=Count('subscriptions', filter=Q(subscriptions__status='active'))
    ).order_by('-created_at')

    # حساب الإحصائيات الإجمالية
    total_plans = plans.count()
    active_plans = plans.filter(is_active=True).count()

    # حساب إجمالي الاشتراكات
    total_subscriptions = StudentSubscription.objects.count()
    active_subscriptions = StudentSubscription.objects.filter(status='active').count()

    # حساب إجمالي الإيرادات من الباقات
    total_revenue = SubscriptionPayment.objects.filter(
        status='completed'
    ).aggregate(total=Sum('amount'))['total'] or 0

    context = {
        'plans': plans,
        'total_plans': total_plans,
        'active_plans': active_plans,
        'total_subscriptions': total_subscriptions,
        'active_subscriptions': active_subscriptions,
        'total_revenue': total_revenue,
    }

    return render(request, 'admin/subscriptions/plans_list.html', context)


@login_required
def admin_plan_create(request):
    """صفحة إنشاء باقة جديدة"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import SubscriptionPlan
    import json

    if request.method == 'POST':
        try:
            # جمع البيانات من النموذج
            name = request.POST.get('name')
            description = request.POST.get('description')
            plan_type = request.POST.get('plan_type')
            duration_type = request.POST.get('duration_type')
            duration_days = int(request.POST.get('duration_days', 30))
            price_str = request.POST.get('price', '0')
            price = float(price_str) if price_str.strip() else 0.0
            currency = request.POST.get('currency', 'SAR')
            lessons_count = int(request.POST.get('lessons_count', 1))
            lesson_duration = int(request.POST.get('lesson_duration', 45))
            discount_percentage_str = request.POST.get('discount_percentage', '0')
            discount_percentage = float(discount_percentage_str) if discount_percentage_str.strip() else 0.0
            is_active = request.POST.get('is_active') == 'on'
            is_featured = request.POST.get('is_featured') == 'on'

            # جمع المميزات
            features = []
            feature_inputs = request.POST.getlist('features[]')
            for feature in feature_inputs:
                if feature.strip():
                    features.append(feature.strip())

            # إنشاء الباقة
            plan = SubscriptionPlan.objects.create(
                name=name,
                description=description,
                plan_type=plan_type,
                duration_type=duration_type,
                duration_days=duration_days,
                price=price,
                currency=currency,
                lessons_count=lessons_count,
                lesson_duration=lesson_duration,
                features=features,
                discount_percentage=discount_percentage,
                is_active=is_active,
                is_featured=is_featured
            )

            messages.success(request, f"تم إنشاء الباقة '{plan.name}' بنجاح!")
            return redirect('admin_plans_list')

        except Exception as e:
            messages.error(request, f"حدث خطأ أثناء إنشاء الباقة: {str(e)}")

    context = {
        'plan_types': SubscriptionPlan.PLAN_TYPES,
        'duration_types': SubscriptionPlan.DURATION_TYPES,
        'currency_choices': SubscriptionPlan.CURRENCY_CHOICES,
        'lesson_durations': [(30, '30 دقيقة'), (45, '45 دقيقة'), (60, '60 دقيقة')],
    }

    return render(request, 'admin/subscriptions/plan_create.html', context)


@login_required
def admin_plan_detail(request, plan_id):
    """صفحة عرض تفاصيل الباقة"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import SubscriptionPlan, StudentSubscription
    from django.shortcuts import get_object_or_404
    from django.db.models import Count, Sum

    plan = get_object_or_404(SubscriptionPlan, id=plan_id)

    # إحصائيات الباقة
    subscriptions = StudentSubscription.objects.filter(plan=plan)
    plan_stats = {
        'total_subscriptions': subscriptions.count(),
        'active_subscriptions': subscriptions.filter(status='active').count(),
        'expired_subscriptions': subscriptions.filter(status='expired').count(),
        'cancelled_subscriptions': subscriptions.filter(status='cancelled').count(),
        'total_revenue': subscriptions.aggregate(total=Sum('amount_paid'))['total'] or 0,
    }

    # أحدث الاشتراكات
    recent_subscriptions = subscriptions.select_related('student').order_by('-created_at')[:10]

    context = {
        'plan': plan,
        'plan_stats': plan_stats,
        'recent_subscriptions': recent_subscriptions,
    }

    return render(request, 'admin/subscriptions/plan_detail.html', context)


@login_required
def admin_plan_edit(request, plan_id):
    """صفحة تعديل الباقة"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import SubscriptionPlan
    from django.shortcuts import get_object_or_404
    import json

    plan = get_object_or_404(SubscriptionPlan, id=plan_id)

    if request.method == 'POST':
        try:
            # جمع البيانات من النموذج
            plan.name = request.POST.get('name')
            plan.description = request.POST.get('description')
            plan.plan_type = request.POST.get('plan_type')
            plan.duration_type = request.POST.get('duration_type')
            plan.duration_days = int(request.POST.get('duration_days'))
            plan.price = float(request.POST.get('price'))
            plan.currency = request.POST.get('currency')
            plan.lessons_count = int(request.POST.get('lessons_count'))
            plan.lesson_duration = int(request.POST.get('lesson_duration'))
            plan.discount_percentage = float(request.POST.get('discount_percentage', 0))
            plan.is_active = request.POST.get('is_active') == 'on'
            plan.is_featured = request.POST.get('is_featured') == 'on'

            # جمع المميزات
            features = []
            feature_inputs = request.POST.getlist('features[]')
            for feature in feature_inputs:
                if feature.strip():
                    features.append(feature.strip())
            plan.features = features

            plan.save()

            messages.success(request, f"تم تحديث الباقة '{plan.name}' بنجاح!")
            return redirect('admin_plan_detail', plan_id=plan.id)

        except Exception as e:
            messages.error(request, f"حدث خطأ أثناء تحديث الباقة: {str(e)}")

    context = {
        'plan': plan,
        'plan_types': SubscriptionPlan.PLAN_TYPES,
        'duration_types': SubscriptionPlan.DURATION_TYPES,
        'currency_choices': SubscriptionPlan.CURRENCY_CHOICES,
        'lesson_durations': [(30, '30 دقيقة'), (45, '45 دقيقة'), (60, '60 دقيقة')],
    }

    return render(request, 'admin/subscriptions/plan_edit.html', context)


@login_required
def admin_plan_delete(request, plan_id):
    """حذف الباقة"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import SubscriptionPlan, StudentSubscription
    from django.shortcuts import get_object_or_404

    plan = get_object_or_404(SubscriptionPlan, id=plan_id)

    # التحقق من وجود اشتراكات نشطة
    active_subscriptions = StudentSubscription.objects.filter(
        plan=plan,
        status='active'
    ).count()

    if request.method == 'POST':
        if active_subscriptions > 0:
            messages.error(request, f"لا يمكن حذف الباقة '{plan.name}' لأن لديها {active_subscriptions} اشتراك نشط.")
            return redirect('admin_plan_detail', plan_id=plan.id)

        plan_name = plan.name
        plan.delete()
        messages.success(request, f"تم حذف الباقة '{plan_name}' بنجاح!")
        return redirect('admin_plans_list')

    context = {
        'plan': plan,
        'active_subscriptions': active_subscriptions,
    }

    return render(request, 'admin/subscriptions/plan_delete.html', context)


@login_required
def admin_approve_subscription(request, subscription_id):
    """موافقة المدير على الاشتراك"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import StudentSubscription
    from django.shortcuts import get_object_or_404

    subscription = get_object_or_404(StudentSubscription, id=subscription_id)

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'approve':
            # استخدام خدمة الموافقة على الاشتراك مع الجدولة التلقائية
            from subscriptions.services import SubscriptionApprovalService

            success, message = SubscriptionApprovalService.approve_subscription(
                subscription,
                approved_by=request.user
            )

            if success:
                # إنشاء فاتورة للاشتراك
                from subscriptions.invoice_utils import create_invoice_for_subscription, mark_invoice_as_paid
                invoice = create_invoice_for_subscription(subscription)
                if invoice:
                    mark_invoice_as_paid(subscription)

                messages.success(request, f"تم تفعيل اشتراك {subscription.student.get_full_name()} في باقة {subscription.plan.name} بنجاح! {message}")
            else:
                messages.error(request, f"فشل في تفعيل الاشتراك: {message}")

        elif action == 'reject':
            # رفض الاشتراك
            subscription.status = 'cancelled'
            subscription.notes = "تم رفض الاشتراك من قبل المدير"
            subscription.save()

            # إرسال إشعار إلغاء الاشتراك
            from notifications.utils import SubscriptionNotificationService
            SubscriptionNotificationService.notify_subscription_cancelled(subscription, "تم رفض الاشتراك من قبل المدير")

            messages.warning(request, f"تم رفض اشتراك {subscription.student.get_full_name()} في باقة {subscription.plan.name}.")

        return redirect('admin_subscriptions')

    context = {
        'subscription': subscription,
    }

    return render(request, 'admin/subscriptions/approve_subscription.html', context)


@login_required
def admin_subscriptions_list(request):
    """صفحة قائمة الاشتراكات للمدير"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import StudentSubscription
    from django.db.models import Q

    # فلترة الاشتراكات
    status_filter = request.GET.get('status', '')
    search_query = request.GET.get('search', '')

    subscriptions = StudentSubscription.objects.select_related(
        'student', 'plan'
    ).order_by('-created_at')

    if status_filter:
        subscriptions = subscriptions.filter(status=status_filter)

    if search_query:
        subscriptions = subscriptions.filter(
            Q(student__first_name__icontains=search_query) |
            Q(student__last_name__icontains=search_query) |
            Q(plan__name__icontains=search_query)
        )

    context = {
        'subscriptions': subscriptions,
        'status_filter': status_filter,
        'search_query': search_query,
        'status_choices': StudentSubscription.STATUS_CHOICES,
    }

    return render(request, 'admin/subscriptions/subscriptions_list.html', context)


@login_required
def admin_payments_list(request):
    """صفحة قائمة المدفوعات للمدير"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import SubscriptionPayment, BankTransferProof
    from django.db.models import Q

    # فلترة المدفوعات
    status_filter = request.GET.get('status', '')
    method_filter = request.GET.get('method', '')
    search_query = request.GET.get('search', '')

    payments = SubscriptionPayment.objects.select_related(
        'subscription__student', 'subscription__plan'
    ).order_by('-payment_date')

    if status_filter:
        payments = payments.filter(status=status_filter)

    if method_filter:
        payments = payments.filter(payment_method=method_filter)

    if search_query:
        payments = payments.filter(
            Q(subscription__student__first_name__icontains=search_query) |
            Q(subscription__student__last_name__icontains=search_query) |
            Q(transaction_id__icontains=search_query)
        )

    # التحويلات البنكية المعلقة
    pending_transfers = BankTransferProof.objects.filter(
        is_verified=False
    ).select_related('payment__subscription__student', 'payment__subscription__plan')

    context = {
        'payments': payments,
        'pending_transfers': pending_transfers,
        'status_filter': status_filter,
        'method_filter': method_filter,
        'search_query': search_query,
        'status_choices': SubscriptionPayment.STATUS_CHOICES,
        'method_choices': SubscriptionPayment.PAYMENT_METHODS,
    }

    return render(request, 'admin/subscriptions/payments_list.html', context)


@login_required
def admin_verify_transfer(request, transfer_id):
    """التحقق من التحويل البنكي"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import BankTransferProof
    from django.utils import timezone

    try:
        transfer = BankTransferProof.objects.get(id=transfer_id)

        if request.method == 'POST':
            action = request.POST.get('action')

            if action == 'approve':
                # الموافقة على التحويل
                transfer.is_verified = True
                transfer.verified_by = request.user
                transfer.verified_at = timezone.now()
                transfer.save()

                # تحديث حالة الدفع
                payment = transfer.payment
                payment.status = 'completed'
                payment.processed_at = timezone.now()
                payment.save()

                # تفعيل الاشتراك مع الجدولة التلقائية
                subscription = payment.subscription
                from subscriptions.services import SubscriptionApprovalService

                # تحديث حالة الاشتراك لتكون في انتظار الموافقة أولاً
                subscription.status = 'pending_approval'
                subscription.save()

                # ثم الموافقة عليها مع الجدولة التلقائية
                success, message = SubscriptionApprovalService.approve_subscription(
                    subscription,
                    approved_by=request.user
                )

                # إنشاء فاتورة للاشتراك
                from subscriptions.invoice_utils import create_invoice_for_subscription, mark_invoice_as_paid
                invoice = create_invoice_for_subscription(subscription)
                if invoice:
                    mark_invoice_as_paid(subscription)

                if success:
                    messages.success(request, f"تم الموافقة على التحويل وتفعيل الاشتراك بنجاح! {message}")
                else:
                    messages.warning(request, f"تم الموافقة على التحويل لكن: {message}")

            elif action == 'reject':
                # رفض التحويل
                payment = transfer.payment
                payment.status = 'failed'
                payment.processed_at = timezone.now()
                payment.save()

                subscription = payment.subscription
                subscription.status = 'cancelled'
                subscription.notes = "تم رفض التحويل البنكي"
                subscription.save()

                # إرسال إشعار إلغاء الاشتراك
                from notifications.utils import SubscriptionNotificationService
                SubscriptionNotificationService.notify_subscription_cancelled(subscription, "تم رفض التحويل البنكي")

                transfer.delete()

                messages.warning(request, "تم رفض التحويل وإلغاء الاشتراك.")

            return redirect('admin_payments_list')

    except BankTransferProof.DoesNotExist:
        messages.error(request, "التحويل البنكي غير موجود.")
        return redirect('admin_payments_list')

    context = {
        'transfer': transfer,
    }

    return render(request, 'admin/subscriptions/verify_transfer.html', context)


# ===============================
# Admin Invoices Management Views
# ===============================

@login_required
def admin_invoices(request):
    """صفحة إدارة الفواتير للمدير"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.invoice_models import Invoice
    from django.db.models import Q, Count, Sum
    from django.utils import timezone

    # فلترة الفواتير
    status_filter = request.GET.get('status', '')
    search_query = request.GET.get('search', '')

    invoices = Invoice.objects.select_related(
        'subscription__student', 'subscription__plan'
    ).order_by('-created_at')

    if status_filter:
        invoices = invoices.filter(status=status_filter)

    if search_query:
        invoices = invoices.filter(
            Q(student_name__icontains=search_query) |
            Q(invoice_number__icontains=search_query) |
            Q(plan_name__icontains=search_query)
        )

    # إحصائيات الفواتير
    total_invoices = Invoice.objects.count()
    paid_invoices = Invoice.objects.filter(status='paid').count()
    pending_invoices = Invoice.objects.filter(status='sent').count()
    overdue_invoices = Invoice.objects.filter(
        status='sent',
        due_date__lt=timezone.now()
    ).count()

    # إجمالي الإيرادات
    total_revenue = Invoice.objects.filter(status='paid').aggregate(
        total=Sum('total_amount')
    )['total'] or 0

    context = {
        'invoices': invoices,
        'status_filter': status_filter,
        'search_query': search_query,
        'status_choices': Invoice.STATUS_CHOICES,
        'total_invoices': total_invoices,
        'paid_invoices': paid_invoices,
        'pending_invoices': pending_invoices,
        'overdue_invoices': overdue_invoices,
        'total_revenue': total_revenue,
    }

    return render(request, 'admin/invoices/invoices_list.html', context)


@login_required
def admin_invoice_detail(request, invoice_id):
    """صفحة عرض تفاصيل الفاتورة"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.invoice_models import Invoice
    from subscriptions.invoice_utils import get_invoice_context
    from django.shortcuts import get_object_or_404

    invoice = get_object_or_404(Invoice, id=invoice_id)
    context = get_invoice_context(invoice)

    return render(request, 'admin/invoices/invoice_detail.html', context)


@login_required
def admin_invoice_print(request, invoice_id):
    """صفحة طباعة الفاتورة"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.invoice_models import Invoice
    from subscriptions.invoice_utils import get_invoice_context
    from django.shortcuts import get_object_or_404

    invoice = get_object_or_404(Invoice, id=invoice_id)
    context = get_invoice_context(invoice)

    return render(request, 'admin/invoices/invoice_print.html', context)


@login_required
def admin_invoice_pdf(request, invoice_id):
    """تحميل الفاتورة كملف PDF"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.invoice_models import Invoice
    from subscriptions.invoice_utils import get_invoice_context
    from django.shortcuts import get_object_or_404
    from django.http import HttpResponse
    from django.template.loader import render_to_string

    try:
        # محاولة استيراد مكتبة PDF
        from weasyprint import HTML, CSS
        from django.conf import settings
        import os

        invoice = get_object_or_404(Invoice, id=invoice_id)
        context = get_invoice_context(invoice)

        # رندر القالب
        html_string = render_to_string('admin/invoices/invoice_pdf.html', context)

        # إنشاء PDF
        html = HTML(string=html_string)
        pdf = html.write_pdf()

        # إرجاع الاستجابة
        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="invoice_{invoice.invoice_number}.pdf"'
        return response

    except ImportError:
        # في حالة عدم توفر مكتبة PDF
        messages.error(request, "مكتبة PDF غير متوفرة. يرجى تثبيت weasyprint.")
        return redirect('admin_invoice_detail', invoice_id=invoice_id)
    except Exception as e:
        messages.error(request, f"حدث خطأ أثناء إنشاء PDF: {str(e)}")
        return redirect('admin_invoice_detail', invoice_id=invoice_id)









@login_required
def notifications_redirect(request):
    """إعادة توجيه لنظام الإشعارات الجديد"""
    return redirect('/notifications/')


@login_required
def thank_you_page(request, payment_id):
    """صفحة الشكر بعد إتمام الدفع"""
    if not request.user.is_student():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import SubscriptionPayment
    from django.shortcuts import get_object_or_404

    try:
        # جلب تفاصيل الدفع
        payment = get_object_or_404(
            SubscriptionPayment,
            id=payment_id,
            student=request.user
        )

        # تحديد طريقة الدفع بالعربية
        payment_method_ar = {
            'paypal': 'PayPal',
            'stripe': 'Stripe',
            'bank_transfer': 'تحويل بنكي'
        }.get(payment.payment_method, payment.payment_method)

        payment_details = {
            'id': payment.id,
            'amount': payment.amount,
            'payment_method': payment_method_ar,
            'created_at': payment.created_at,
            'status': payment.status
        }

        context = {
            'payment_details': payment_details,
            'page_title': 'شكراً لك',
        }

        return render(request, 'student/thank_you.html', context)

    except Exception as e:
        messages.error(request, "حدث خطأ في عرض صفحة الشكر.")
        return redirect('student_subscriptions')


@login_required
def payment_success(request, payment_id):
    """صفحة نجاح الدفع الجديدة"""
    if not request.user.is_student():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from subscriptions.models import SubscriptionPayment
    from django.shortcuts import get_object_or_404

    try:
        # جلب تفاصيل الدفع
        payment = get_object_or_404(
            SubscriptionPayment,
            id=payment_id,
            student=request.user
        )

        # التأكد من أن الدفع تم بنجاح
        if payment.status != 'completed':
            messages.error(request, "عملية الدفع لم تكتمل بعد.")
            return redirect('student_subscriptions')

        context = {
            'payment': payment,
            'subscription': payment.subscription,
            'plan': payment.subscription.plan
        }

        return render(request, 'student/payment_success.html', context)

    except Exception as e:
        messages.error(request, "حدث خطأ في عرض صفحة نجاح الدفع.")
        return redirect('student_subscriptions')


@login_required
@require_http_methods(["POST"])
def check_payment_status_api(request):
    """API للتحقق من حالة الدفع"""
    if not request.user.is_student():
        return JsonResponse({'success': False, 'message': 'غير مصرح'})

    try:
        import json
        from subscriptions.models import SubscriptionPayment

        data = json.loads(request.body)
        payment_data = data.get('payment_data', {})
        start_time = data.get('start_time')

        # البحث عن عمليات الدفع الحديثة للطالب
        recent_payments = SubscriptionPayment.objects.filter(
            student=request.user,
            created_at__gte=timezone.now() - timedelta(minutes=30)
        ).order_by('-created_at')

        for payment in recent_payments:
            if payment.status == 'completed':
                return JsonResponse({
                    'success': True,
                    'completed': True,
                    'data': {
                        'payment_id': payment.id,
                        'amount': str(payment.amount),
                        'status': payment.status,
                        'payment_method': payment.payment_method
                    }
                })
            elif payment.status == 'failed':
                return JsonResponse({
                    'success': True,
                    'completed': False,
                    'failed': True,
                    'data': {
                        'payment_id': payment.id,
                        'message': 'فشل في عملية الدفع'
                    }
                })

        # لم يتم العثور على عملية دفع مكتملة
        return JsonResponse({
            'success': True,
            'completed': False,
            'failed': False,
            'message': 'لم يتم العثور على عملية دفع مكتملة'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'خطأ في التحقق من حالة الدفع: {str(e)}'
        })


@login_required
@require_http_methods(["POST"])
def payment_event_api(request):
    """API لتسجيل أحداث الدفع"""
    try:
        import json

        data = json.loads(request.body)
        event_type = data.get('event_type')
        event_data = data.get('data', {})
        timestamp = data.get('timestamp')

        # تسجيل الحدث في قاعدة البيانات أو ملف السجل
        import logging
        logger = logging.getLogger('payment_events')

        logger.info(f"Payment Event - User: {request.user.id}, Type: {event_type}, Data: {event_data}, Time: {timestamp}")

        return JsonResponse({'success': True, 'message': 'تم تسجيل الحدث'})

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'خطأ في تسجيل الحدث: {str(e)}'
        })


# ===============================
# Cross-Dashboard Integration Views
# ===============================

@login_required
def admin_user_detail(request, user_id):
    """عرض تفاصيل مستخدم مع جميع العلاقات المترابطة"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.shortcuts import get_object_or_404
    from django.db.models import Count, Avg, Sum
    # تم إزالة التبعية على courses app
    from lessons.models import Lesson, LessonRating

    user = get_object_or_404(User, id=user_id)

    # إحصائيات حسب نوع المستخدم
    user_data = {
        'user': user,
        'enrollments': [],
        'lessons': [],
        'ratings': [],
        'earnings': [],
        'stats': {}
    }

    if user.user_type == 'student':
        # بيانات الطالب - تم تعطيل نظام التسجيلات بعد حذف courses app
        lessons = Lesson.objects.filter(student=user).select_related('teacher')
        ratings_given = LessonRating.objects.filter(student=user).select_related('lesson')

        user_data.update({
            'enrollments': [],  # تم تعطيل نظام التسجيلات
            'lessons': lessons,
            'ratings': ratings_given,
            'stats': {
                'total_enrollments': 0,  # تم تعطيل نظام التسجيلات
                'active_enrollments': 0,  # تم تعطيل نظام التسجيلات
                'completed_lessons': lessons.filter(status='completed').count(),
                'total_lessons': lessons.count(),
                'avg_rating_given': ratings_given.aggregate(avg=Avg('teacher_rating'))['avg'] or 0,
            }
        })

    elif user.user_type == 'teacher':
        # بيانات المعلم - تم تعطيل نظام التسجيلات بعد حذف courses app
        lessons = Lesson.objects.filter(teacher=user).select_related('student')
        ratings_received = LessonRating.objects.filter(lesson__teacher=user).select_related('lesson', 'student')

        # حساب عدد الطلاب الفريدين من الحصص
        unique_students = lessons.values('student').distinct().count()

        user_data.update({
            'enrollments': [],  # تم تعطيل نظام التسجيلات
            'lessons': lessons,
            'ratings': ratings_received,
            'teacher_rating': None,  # تم تعطيل نظام تقييم المعلمين الإجمالي
            'stats': {
                'total_students': unique_students,
                'active_enrollments': 0,  # تم تعطيل نظام التسجيلات
                'completed_lessons': lessons.filter(status='completed').count(),
                'total_lessons': lessons.count(),
                'avg_rating_received': ratings_received.aggregate(avg=Avg('teacher_rating'))['avg'] or 0,
            }
        })

    return render(request, 'admin/user_detail.html', user_data)





@login_required
def admin_schedule_lesson(request):
    """جدولة حصة جديدة - ربط التسجيل بحصة"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    # تم تعطيل نظام جدولة الحصص بعد حذف courses app
    messages.error(request, "نظام جدولة الحصص غير متاح حالياً بعد إزالة نظام الدورات.")
    return redirect('/dashboard/')





@login_required
def admin_subscription_detail(request, subscription_id):
    """عرض تفاصيل الاشتراك"""
    if not request.user.is_admin():
        return JsonResponse({'error': 'ليس لديك صلاحية للوصول إلى هذه الصفحة.'}, status=403)

    from subscriptions.models import StudentSubscription
    from django.shortcuts import get_object_or_404
    from django.utils import timezone

    try:
        subscription = get_object_or_404(
            StudentSubscription.objects.select_related('student', 'plan'),
            id=subscription_id
        )

        # حساب إحصائيات الاشتراك
        from lessons.models import Lesson
        # البحث عن الحصص بناءً على الطالب (لأن نموذج Lesson لا يحتوي على حقل subscription)
        total_lessons = Lesson.objects.filter(student=subscription.student).count()
        completed_lessons = Lesson.objects.filter(
            student=subscription.student,
            status='completed'
        ).count()
        remaining_lessons = max(0, subscription.plan.lessons_count - completed_lessons)

        # حساب الأيام المتبقية
        if subscription.end_date:
            remaining_days = (subscription.end_date - timezone.now().date()).days
            remaining_days = max(0, remaining_days)
        else:
            remaining_days = 0

        # معلومات الدفع
        payment_info = None
        latest_payment = subscription.payments.order_by('-payment_date').first()
        if latest_payment:
            payment_info = {
                'amount': latest_payment.amount,
                'currency': 'USD',  # أو من إعدادات النظام
                'method': latest_payment.get_payment_method_display(),
                'status': latest_payment.get_status_display(),
                'created_at': latest_payment.payment_date.strftime('%Y-%m-%d %H:%M'),
                'processed_at': latest_payment.processed_at.strftime('%Y-%m-%d %H:%M') if latest_payment.processed_at else None
            }

        data = {
            'success': True,
            'subscription': {
                'id': subscription.id,
                'student_name': subscription.student.get_full_name(),
                'student_email': subscription.student.email,
                'plan_name': subscription.plan.name,
                'plan_description': subscription.plan.description,
                'status': subscription.get_status_display(),
                'status_class': subscription.status,
                'created_at': subscription.created_at.strftime('%Y-%m-%d %H:%M'),
                'start_date': subscription.start_date.strftime('%Y-%m-%d') if subscription.start_date else None,
                'end_date': subscription.end_date.strftime('%Y-%m-%d') if subscription.end_date else None,
                'remaining_days': remaining_days,
                'total_lessons': subscription.plan.lessons_count,
                'completed_lessons': completed_lessons,
                'remaining_lessons': remaining_lessons,
                'payment': payment_info
            }
        }

        return JsonResponse(data)

    except Exception as e:
        return JsonResponse({'error': f'حدث خطأ: {str(e)}'}, status=500)


@login_required
def admin_cancel_subscription(request, subscription_id):
    """إلغاء الاشتراك"""
    if not request.user.is_admin():
        return JsonResponse({'error': 'ليس لديك صلاحية للوصول إلى هذه الصفحة.'}, status=403)

    if request.method != 'POST':
        return JsonResponse({'error': 'طريقة الطلب غير صحيحة.'}, status=405)

    from subscriptions.models import StudentSubscription
    from django.shortcuts import get_object_or_404
    from django.utils import timezone

    try:
        subscription = get_object_or_404(
            StudentSubscription.objects.select_related('student', 'plan'),
            id=subscription_id
        )

        if subscription.status == 'cancelled':
            return JsonResponse({'error': 'الاشتراك ملغى بالفعل.'}, status=400)

        # إلغاء الاشتراك
        subscription.status = 'cancelled'
        # ملاحظة: إذا كان النموذج لا يحتوي على هذه الحقول، سيتم تجاهلها
        if hasattr(subscription, 'cancelled_at'):
            subscription.cancelled_at = timezone.now()
        if hasattr(subscription, 'cancelled_by'):
            subscription.cancelled_by = request.user
        subscription.save()

        # إرسال إشعار للطالب بإلغاء الاشتراك
        from subscriptions.email_service import SubscriptionEmailService
        email_service = SubscriptionEmailService()
        email_service.send_subscription_cancelled(subscription, "تم إلغاء الاشتراك من قبل الإدارة")

        # إلغاء الحصص المجدولة المستقبلية
        from lessons.models import Lesson
        future_lessons = Lesson.objects.filter(
            student=subscription.student,
            status='scheduled',
            scheduled_date__gt=timezone.now()
        )

        cancelled_lessons_count = future_lessons.count()
        future_lessons.update(status='cancelled')

        messages.success(
            request,
            f"تم إلغاء اشتراك {subscription.student.get_full_name()} بنجاح. "
            f"تم إلغاء {cancelled_lessons_count} حصة مجدولة."
        )

        return JsonResponse({
            'success': True,
            'message': f'تم إلغاء الاشتراك بنجاح. تم إلغاء {cancelled_lessons_count} حصة مجدولة.',
            'cancelled_lessons': cancelled_lessons_count
        })

    except Exception as e:
        return JsonResponse({'error': f'حدث خطأ: {str(e)}'}, status=500)


@login_required
def admin_enrollment_detail(request, enrollment_id):
    """عرض تفاصيل التسجيل مع إمكانية التعديل - تم تعطيله بعد حذف courses app"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    # تم تعطيل نظام التسجيلات بعد حذف courses app
    messages.error(request, "نظام إدارة التسجيلات غير متاح حالياً بعد إزالة نظام الدورات.")
    return redirect('/dashboard/')


@login_required
def messages_view(request):
    """صفحة الرسائل"""
    return render(request, 'common/messages.html')



def about(request):
    """صفحة حول الموقع"""
    return render(request, 'static_pages/about.html')

def privacy(request):
    """صفحة سياسة الخصوصية"""
    return render(request, 'static_pages/privacy.html')

def terms(request):
    """صفحة شروط الاستخدام"""
    return render(request, 'static_pages/terms.html')

@login_required
def user_profile(request):
    """صفحة الملف الشخصي مع إمكانية التحديث"""
    from django.contrib import messages
    from django.shortcuts import redirect
    from .forms import ProfileUpdateForm, UserProfileUpdateForm, PasswordChangeForm
    from .models import UserProfile

    # إنشاء أو الحصول على الملف الشخصي الإضافي
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        form_type = request.POST.get('form_type')

        if form_type == 'profile':
            # تحديث المعلومات الأساسية
            form = ProfileUpdateForm(
                request.POST,
                request.FILES,
                instance=request.user,
                user=request.user
            )
            if form.is_valid():
                form.save()
                messages.success(request, 'تم تحديث المعلومات الأساسية بنجاح.')
                return redirect('profile')
            else:
                # إظهار أخطاء مفصلة
                for field, errors in form.errors.items():
                    for error in errors:
                        if field == '__all__':
                            messages.error(request, f'خطأ: {error}')
                        else:
                            field_label = form.fields.get(field, {}).label or field
                            messages.error(request, f'{field_label}: {error}')
                if not form.errors:
                    messages.error(request, 'يرجى تصحيح الأخطاء في النموذج.')

        elif form_type == 'additional':
            # تحديث المعلومات الإضافية
            additional_form = UserProfileUpdateForm(
                request.POST,
                instance=profile
            )
            if additional_form.is_valid():
                additional_form.save()
                messages.success(request, 'تم تحديث المعلومات الإضافية بنجاح.')
                return redirect('profile')
            else:
                messages.error(request, 'يرجى تصحيح الأخطاء في النموذج.')

        elif form_type == 'password':
            # تغيير كلمة المرور
            password_form = PasswordChangeForm(
                request.user,
                request.POST
            )
            if password_form.is_valid():
                password_form.save()
                # إعادة تسجيل الدخول بعد تغيير كلمة المرور
                from django.contrib.auth import update_session_auth_hash
                update_session_auth_hash(request, request.user)
                messages.success(request, 'تم تغيير كلمة المرور بنجاح.')
                return redirect('profile')
            else:
                messages.error(request, 'يرجى تصحيح الأخطاء في كلمة المرور.')

    # إنشاء النماذج للعرض
    form = ProfileUpdateForm(instance=request.user, user=request.user)
    additional_form = UserProfileUpdateForm(instance=profile)
    password_form = PasswordChangeForm(request.user)

    # إحصائيات المستخدم حسب النوع
    user_stats = {}

    if request.user.user_type == 'student':
        # تم تعطيل نظام التسجيلات بعد حذف courses app
        from lessons.models import Lesson

        lessons = Lesson.objects.filter(student=request.user)

        user_stats = {
            'total_enrollments': 0,  # تم تعطيل نظام التسجيلات
            'active_enrollments': 0,  # تم تعطيل نظام التسجيلات
            'completed_lessons': lessons.filter(status='completed').count(),
            'total_lessons': lessons.count(),
        }

    elif request.user.user_type == 'teacher':
        # تم تعطيل نظام التسجيلات والأرباح بعد حذف courses app
        from lessons.models import Lesson, LessonRating
        from django.db.models import Avg

        lessons = Lesson.objects.filter(teacher=request.user)
        ratings = LessonRating.objects.filter(lesson__teacher=request.user)

        # حساب عدد الطلاب الفريدين من الحصص
        unique_students = lessons.values('student').distinct().count()

        user_stats = {
            'total_students': unique_students,
            'active_enrollments': 0,  # تم تعطيل نظام التسجيلات
            'completed_lessons': lessons.filter(status='completed').count(),
            'total_lessons': lessons.count(),
            'avg_rating': ratings.aggregate(avg=Avg('teacher_rating'))['avg'] or 0,
            'total_earnings': 0,  # تم تعطيل نظام الأرباح
        }

    elif request.user.user_type == 'admin':
        from django.contrib.auth import get_user_model
        from lessons.models import Lesson

        User = get_user_model()

        user_stats = {
            'total_users': User.objects.count(),
            'total_students': User.objects.filter(user_type='student').count(),
            'total_teachers': User.objects.filter(user_type='teacher').count(),
            'total_courses': 0,  # تم تعطيل نظام الدورات
            'total_enrollments': 0,  # تم تعطيل نظام التسجيلات
            'total_lessons': Lesson.objects.count(),
        }

    context = {
        'form': form,
        'additional_form': additional_form,
        'password_form': password_form,
        'user_stats': user_stats,
        'profile': profile,
    }

    return render(request, 'users/profile.html', context)

def register(request):
    """صفحة تسجيل حساب جديد مع نظام التحقق"""

    if request.method == 'POST':
        # جلب البيانات من النموذج
        user_type = request.POST.get('user_type')
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        username = request.POST.get('username')
        email = request.POST.get('email')
        phone = request.POST.get('phone')
        password1 = request.POST.get('password1')
        password2 = request.POST.get('password2')
        terms = request.POST.get('terms')
        student_level = request.POST.get('student_level')

        # التحقق من البيانات
        errors = []

        # التحقق من الحقول المطلوبة
        if not all([user_type, first_name, last_name, username, email, password1, password2]):
            errors.append("جميع الحقول مطلوبة")

        # التحقق من نوع المستخدم
        if user_type not in ['student', 'teacher']:
            errors.append("نوع المستخدم غير صحيح")

        # التحقق من تطابق كلمات المرور
        if password1 != password2:
            errors.append("كلمات المرور غير متطابقة")

        # التحقق من طول كلمة المرور
        if len(password1) < 8:
            errors.append("كلمة المرور يجب أن تكون 8 أحرف على الأقل")

        # التحقق من الموافقة على الشروط
        if not terms:
            errors.append("يجب الموافقة على شروط الاستخدام")

        # التحقق من عدم وجود اسم المستخدم (مع استثناء المرفوضين غير المحظورين)
        existing_user_by_username = User.objects.filter(username=username).first()
        if existing_user_by_username:
            if not existing_user_by_username.can_register_again():
                if existing_user_by_username.is_currently_banned():
                    errors.append(f"اسم المستخدم محظور. {existing_user_by_username.get_ban_status_display()}")
                else:
                    errors.append("اسم المستخدم موجود بالفعل")

        # التحقق من عدم وجود البريد الإلكتروني (مع استثناء المرفوضين غير المحظورين)
        existing_user_by_email = User.objects.filter(email=email).first()
        if existing_user_by_email:
            if not existing_user_by_email.can_register_again():
                if existing_user_by_email.is_currently_banned():
                    errors.append(f"البريد الإلكتروني محظور. {existing_user_by_email.get_ban_status_display()}")
                else:
                    errors.append("البريد الإلكتروني مسجل بالفعل")

        if errors:
            for error in errors:
                messages.error(request, error)
            return render(request, 'auth/register.html', {
                'form_data': request.POST
            })

        try:
            # التحقق من وجود مستخدم مرفوض يمكن إعادة تسجيله
            existing_user = None
            if existing_user_by_username and existing_user_by_username.can_register_again():
                existing_user = existing_user_by_username
            elif existing_user_by_email and existing_user_by_email.can_register_again():
                existing_user = existing_user_by_email

            if existing_user:
                # إعادة تسجيل المستخدم المرفوض
                existing_user.first_name = first_name
                existing_user.last_name = last_name
                existing_user.username = username
                existing_user.email = email
                existing_user.phone = phone
                existing_user.user_type = user_type
                existing_user.set_password(password1)
                existing_user.verification_status = 'pending'
                existing_user.is_active = False
                existing_user.rejection_reason = None
                existing_user.verified_by = None
                existing_user.verified_at = None

                # تحديث مستوى الطالب إذا كان المستخدم طالباً
                if user_type == 'student' and student_level:
                    existing_user.student_level = student_level

                existing_user.save()
                user = existing_user

                messages.info(
                    request,
                    "تم تحديث طلبك السابق وإعادة إرساله للمراجعة."
                )
            else:
                # إنشاء المستخدم الجديد
                user = User.objects.create_user(
                    username=username,
                    email=email,
                    password=password1,
                    first_name=first_name,
                    last_name=last_name,
                    user_type=user_type,
                    phone=phone,
                    is_active=False,  # غير نشط حتى الموافقة
                    verification_status='pending'  # في انتظار المراجعة
                )

                # حفظ مستوى الطالب إذا كان المستخدم طالباً
                if user_type == 'student' and student_level:
                    user.student_level = student_level
                    user.save()

            # إشعار المديرين بالتسجيل الجديد
            _notify_admins_new_registration(user)

            # إرسال رسالة ترحيبية للمستخدم الجديد
            _send_welcome_message(user)

            # توجيه لصفحة نجاح التسجيل مع معرف المستخدم
            return redirect('registration_success', user_id=user.id)

        except Exception as e:
            # التعامل مع خطأ البريد الإلكتروني المكرر
            if 'UNIQUE constraint failed' in str(e) and 'email' in str(e):
                messages.error(request, "البريد الإلكتروني مسجل بالفعل في النظام.")
            elif 'UNIQUE constraint failed' in str(e) and 'username' in str(e):
                messages.error(request, "اسم المستخدم موجود بالفعل في النظام.")
            elif 'duplicate key value violates unique constraint' in str(e).lower():
                if 'email' in str(e).lower():
                    messages.error(request, "البريد الإلكتروني مسجل بالفعل في النظام.")
                elif 'username' in str(e).lower():
                    messages.error(request, "اسم المستخدم موجود بالفعل في النظام.")
                else:
                    messages.error(request, "البيانات المدخلة موجودة بالفعل في النظام.")
            else:
                messages.error(request, f"حدث خطأ أثناء إنشاء الحساب: {str(e)}")

            return render(request, 'auth/register.html', {
                'form_data': request.POST
            })

    return render(request, 'auth/register.html')


def registration_success(request, user_id):
    """صفحة نجاح التسجيل"""
    try:
        user = User.objects.get(id=user_id)

        # التأكد من أن المستخدم في حالة انتظار
        if user.verification_status != 'pending':
            return redirect('login')

        context = {
            'user': user,
            'user_type_display': 'معلم' if user.user_type == 'teacher' else 'طالب'
        }

        return render(request, 'auth/registration_success.html', context)

    except User.DoesNotExist:
        messages.error(request, "المستخدم غير موجود.")
        return redirect('register')


def _notify_admins_new_registration(user):
    """إشعار المديرين بتسجيل جديد"""
    try:
        from notifications.models import Notification
        admins = User.objects.filter(user_type='admin', is_active=True)

        user_type_ar = 'معلم' if user.user_type == 'teacher' else 'طالب'

        for admin in admins:
            Notification.objects.create(
                recipient=admin,
                title=f'طلب تسجيل {user_type_ar} جديد',
                message=f'تم تسجيل {user_type_ar} جديد: {user.get_full_name() or user.username} ({user.email}). يرجى مراجعة الطلب.',
                notification_type='user_registration',
                is_read=False
            )
    except Exception as e:
        print(f"خطأ في إرسال الإشعارات: {str(e)}")  # للتطوير فقط


def _send_welcome_message(user, is_approved=False):
    """إرسال رسالة ترحيبية للمستخدم الجديد

    Args:
        user: المستخدم المراد إرسال الرسالة له
        is_approved: هل تم الموافقة على الحساب بالفعل؟
    """
    try:
        from notifications.models import Message
        from django.contrib.auth import get_user_model

        User = get_user_model()

        # الحصول على مستخدم مدير لإرسال الرسالة منه
        admin_user = User.objects.filter(user_type='admin', is_active=True).first()

        if not admin_user:
            print("لا يوجد مدير نشط لإرسال رسالة الترحيب")
            return

        # الحصول على إعدادات الأكاديمية
        from users.models import AcademySettings
        academy_settings = AcademySettings.get_settings()
        academy_name = academy_settings.academy_name
        academy_slogan = academy_settings.academy_slogan
        support_email = academy_settings.academy_support_email

        # تحديد محتوى الرسالة حسب نوع المستخدم ووضع الحساب
        if user.user_type == 'teacher':
            if is_approved:
                subject = f"تهانينا! تم تفعيل حسابك في {academy_name}"
                content = f"""
                <p>السلام عليكم ورحمة الله وبركاته</p>
                <p>أهلاً بك {user.get_full_name() or user.username} في {academy_name}!</p>
                <p>يسرنا إعلامك بأنه تمت الموافقة على حسابك كمعلم في {academy_slogan}. يمكنك الآن الدخول إلى حسابك والاستفادة من جميع مميزات المنصة.</p>
                <p>يمكنك الآن:</p>
                <ul>
                    <li>إدارة الحصص الخاصة بك</li>
                    <li>متابعة تقدم طلابك</li>
                    <li>تسجيل الحضور والغياب</li>
                    <li>التواصل مع الطلاب</li>
                    <li>الاطلاع على جدولك الدراسي</li>
                </ul>
                <p>إذا كان لديك أي استفسار، يرجى التواصل مع الدعم الفني على البريد الإلكتروني: {support_email}</p>
                <p>مع أطيب التمنيات بالتوفيق،</p>
                <p>إدارة {academy_name}</p>
                """
            else:
                subject = f"مرحباً بك في {academy_name}"
                content = f"""
                <p>السلام عليكم ورحمة الله وبركاته</p>
                <p>أهلاً بك {user.get_full_name() or user.username} في {academy_name}!</p>
                <p>نحن سعداء بانضمامك إلى فريق المعلمين لدينا في {academy_slogan}. سيتم مراجعة طلبك من قبل إدارة النظام وسيتم إعلامك عند الموافقة عليه.</p>
                <p>بعد الموافقة على حسابك، ستتمكن من:</p>
                <ul>
                    <li>إدارة الحصص الخاصة بك</li>
                    <li>متابعة تقدم طلابك</li>
                    <li>تسجيل الحضور والغياب</li>
                    <li>التواصل مع الطلاب</li>
                    <li>الاطلاع على جدولك الدراسي</li>
                </ul>
                <p>إذا كان لديك أي استفسار، يرجى التواصل مع الدعم الفني على البريد الإلكتروني: {support_email}</p>
                <p>مع أطيب التمنيات بالتوفيق،</p>
                <p>إدارة {academy_name}</p>
                """
        else:  # طالب
            if is_approved:
                subject = f"تهانينا! تم تفعيل حسابك في {academy_name}"
                content = f"""
                <p>السلام عليكم ورحمة الله وبركاته</p>
                <p>أهلاً بك {user.get_full_name() or user.username} في {academy_name}!</p>
                <p>يسرنا إعلامك بأنه تمت الموافقة على حسابك كطالب في {academy_slogan}. يمكنك الآن الدخول إلى حسابك والاستفادة من جميع مميزات المنصة.</p>
                <p>يمكنك الآن:</p>
                <ul>
                    <li>حضور الحصص المجدولة</li>
                    <li>متابعة تقدمك الشخصي</li>
                    <li>الاطلاع على الدرجات والتقييمات</li>
                    <li>التواصل مع المعلمين</li>
                    <li>الوصول إلى أرشيف الحصص السابقة</li>
                </ul>
                <p>إذا كان لديك أي استفسار، يرجى التواصل مع الدعم الفني على البريد الإلكتروني: {support_email}</p>
                <p>مع أطيب التمنيات بالتوفيق،</p>
                <p>إدارة {academy_name}</p>
                """
            else:
                subject = f"مرحباً بك في {academy_name}"
                content = f"""
                <p>السلام عليكم ورحمة الله وبركاته</p>
                <p>أهلاً بك {user.get_full_name() or user.username} في {academy_name}!</p>
                <p>نحن سعداء بانضمامك إلينا في {academy_slogan}. سيتم مراجعة طلبك من قبل إدارة النظام وسيتم إعلامك عند الموافقة عليه.</p>
                <p>بعد الموافقة على حسابك، ستتمكن من:</p>
                <ul>
                    <li>حضور الحصص المجدولة</li>
                    <li>متابعة تقدمك الشخصي</li>
                    <li>الاطلاع على الدرجات والتقييمات</li>
                    <li>التواصل مع المعلمين</li>
                    <li>الوصول إلى أرشيف الحصص السابقة</li>
                </ul>
                <p>إذا كان لديك أي استفسار، يرجى التواصل مع الدعم الفني على البريد الإلكتروني: {support_email}</p>
                <p>مع أطيب التمنيات بالتوفيق،</p>
                <p>إدارة {academy_name}</p>
                """

        # إنشاء الرسالة
        message = Message.objects.create(
            sender=admin_user,
            recipient=user,
            subject=subject,
            content=content,
            message_type='welcome'
        )

        # إنشاء إشعار للمستخدم (سيظهر عند تفعيل الحساب)
        from notifications.utils import create_notification
        create_notification(
            recipient=user,
            notification_type='new_message',
            title=f'رسالة ترحيبية: {subject}',
            message='تم إرسال رسالة ترحيبية من إدارة النظام',
            sender=admin_user,
            priority='medium',
            action_url=f'/notifications/messages/{message.id}/',
            action_text='عرض الرسالة'
        )

        return message

    except Exception as e:
        print(f"خطأ في إرسال رسالة الترحيب: {str(e)}")  # للتطوير فقط
        return None


def verification_pending(request):
    """صفحة انتظار المراجعة - توجيه للصفحة العامة"""
    # حفظ معلومات المستخدم في الجلسة إذا كان مسجل دخول
    if request.user.is_authenticated:
        request.session['pending_username'] = request.user.username
        request.session['pending_user_type'] = request.user.user_type

    # توجيه للصفحة العامة
    return redirect('public_pending_page')


def verification_rejected(request):
    """صفحة رفض التحقق - توجيه للصفحة العامة"""
    # حفظ معلومات المستخدم في الجلسة إذا كان مسجل دخول
    if request.user.is_authenticated:
        request.session['rejected_username'] = request.user.username
        request.session['rejected_user_type'] = request.user.user_type
        request.session['rejection_reason'] = request.user.rejection_reason

    # توجيه للصفحة العامة
    return redirect('public_rejected_page')


def user_banned(request):
    """صفحة الحظر - توجيه للصفحة العامة"""
    # حفظ معلومات المستخدم في الجلسة إذا كان مسجل دخول
    if request.user.is_authenticated:
        user = request.user
        request.session['banned_username'] = user.username
        request.session['ban_reason'] = user.ban_reason
        request.session['ban_type'] = user.ban_type
        if user.banned_until:
            request.session['banned_until'] = user.banned_until.strftime('%Y-%m-%d %H:%M')

    # توجيه للصفحة العامة
    return redirect('public_banned_page')


@login_required
def admin_user_verifications(request):
    """لوحة إدارة طلبات التحقق"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.db.models import Q
    from django.contrib.auth import get_user_model

    User = get_user_model()

    # فلترة حسب الحالة
    status_filter = request.GET.get('status', 'pending')
    user_type_filter = request.GET.get('user_type', '')
    search_query = request.GET.get('search', '')
    ban_status_filter = request.GET.get('ban_status', '')
    registration_period_filter = request.GET.get('registration_period', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    # جلب المستخدمين حسب الفلاتر مع تحسين الأداء
    users = User.objects.exclude(user_type='admin').select_related(
        'verified_by', 'banned_by'
    ).only(
        'id', 'username', 'email', 'first_name', 'last_name',
        'user_type', 'verification_status', 'verified_by', 'verified_at',
        'is_banned', 'ban_type', 'banned_by', 'banned_until', 'created_at',
        'is_active', 'phone', 'profile_picture'
    ).order_by('-created_at')

    # فلترة حسب حالة التحقق
    if status_filter:
        users = users.filter(verification_status=status_filter)

    # فلترة حسب نوع المستخدم
    if user_type_filter:
        users = users.filter(user_type=user_type_filter)

    # فلترة حسب حالة الحظر
    if ban_status_filter:
        if ban_status_filter == 'not_banned':
            users = users.filter(is_banned=False)
        elif ban_status_filter == 'banned':
            users = users.filter(is_banned=True)
        elif ban_status_filter == 'temporary':
            users = users.filter(is_banned=True, ban_type='temporary')
        elif ban_status_filter == 'permanent':
            users = users.filter(is_banned=True, ban_type='permanent')

    # فلترة حسب فترة التسجيل
    from django.utils import timezone
    from datetime import timedelta

    if registration_period_filter:
        now = timezone.now()
        if registration_period_filter == 'today':
            start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
            users = users.filter(created_at__gte=start_date)
        elif registration_period_filter == 'week':
            start_date = now - timedelta(days=now.weekday())
            start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
            users = users.filter(created_at__gte=start_date)
        elif registration_period_filter == 'month':
            start_date = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            users = users.filter(created_at__gte=start_date)
        elif registration_period_filter == 'year':
            start_date = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            users = users.filter(created_at__gte=start_date)

    # فلترة حسب تاريخ التسجيل
    from datetime import datetime

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').replace(tzinfo=timezone.get_current_timezone())
            users = users.filter(created_at__gte=date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').replace(hour=23, minute=59, second=59, tzinfo=timezone.get_current_timezone())
            users = users.filter(created_at__lte=date_to_obj)
        except ValueError:
            pass

    # فلترة حسب البحث
    if search_query:
        users = users.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(username__icontains=search_query) |
            Q(email__icontains=search_query) |
            Q(phone__icontains=search_query)
        )

    # إحصائيات محسنة بدون استعلامات متعددة
    from django.db.models import Count, Case, When, IntegerField

    stats_query = User.objects.exclude(user_type='admin').aggregate(
        pending=Count(Case(When(verification_status='pending', then=1), output_field=IntegerField())),
        approved=Count(Case(When(verification_status='approved', then=1), output_field=IntegerField())),
        rejected=Count(Case(When(verification_status='rejected', then=1), output_field=IntegerField())),
        under_review=Count(Case(When(verification_status='under_review', then=1), output_field=IntegerField())),
        temporary_banned=Count(Case(When(is_banned=True, ban_type='temporary', then=1), output_field=IntegerField())),
        permanent_banned=Count(Case(When(is_banned=True, ban_type='permanent', then=1), output_field=IntegerField())),
    )

    stats = stats_query

    # معالجة الإجراءات
    if request.method == 'POST':
        action = request.POST.get('action')

        # معالجة الإجراءات الجماعية
        if action.startswith('bulk_'):
            user_ids = request.POST.get('user_ids', '').split(',')
            user_ids = [uid.strip() for uid in user_ids if uid.strip()]

            if not user_ids:
                messages.error(request, "لم يتم تحديد أي حسابات.")
                return redirect('admin_user_verifications')

            users = User.objects.filter(id__in=user_ids)

            if action == 'bulk_approve':
                notes = request.POST.get('notes', '')
                from .bulk_operations import safe_bulk_approve
                safe_bulk_approve(request, users, notes)

            elif action == 'bulk_reject':
                reason = request.POST.get('reason', '')
                notes = request.POST.get('notes', '')
                if not reason:
                    messages.error(request, "يرجى إدخال سبب الرفض.")
                else:
                    from .bulk_operations import safe_bulk_reject
                    safe_bulk_reject(request, users, reason, notes)

            elif action == 'bulk_under_review':
                from .bulk_operations import safe_bulk_under_review
                safe_bulk_under_review(request, users)

            elif action == 'bulk_ban':
                ban_type = request.POST.get('ban_type', '')
                ban_reason = request.POST.get('ban_reason', '')

                if not ban_reason:
                    messages.error(request, "يرجى إدخال سبب الحظر.")
                else:
                    banned_until = None
                    if ban_type == 'temporary':
                        ban_duration = request.POST.get('ban_duration', '')
                        ban_unit = request.POST.get('ban_unit', 'days')

                        if not ban_duration:
                            messages.error(request, "يرجى إدخال مدة الحظر.")
                            return redirect('admin_user_verifications')

                        try:
                            from django.utils import timezone
                            from datetime import timedelta

                            duration = int(ban_duration)
                            if ban_unit == 'hours':
                                banned_until = timezone.now() + timedelta(hours=duration)
                            elif ban_unit == 'days':
                                banned_until = timezone.now() + timedelta(days=duration)
                            elif ban_unit == 'weeks':
                                banned_until = timezone.now() + timedelta(weeks=duration)
                            elif ban_unit == 'months':
                                banned_until = timezone.now() + timedelta(days=duration * 30)
                            else:
                                banned_until = timezone.now() + timedelta(days=duration)

                        except ValueError:
                            messages.error(request, "مدة الحظر يجب أن تكون رقماً صحيحاً.")
                            return redirect('admin_user_verifications')

                    from .bulk_operations import safe_bulk_ban
                    safe_bulk_ban(request, users, ban_type, ban_reason, banned_until)

            elif action == 'bulk_delete':
                delete_reason = request.POST.get('delete_reason', '')
                confirm_delete = request.POST.get('confirm_delete', '')

                if not delete_reason or not confirm_delete:
                    messages.error(request, 'يرجى تحديد سبب الحذف وتأكيد العملية')
                else:
                    from .bulk_operations import safe_bulk_delete
                    safe_bulk_delete(request, users, delete_reason)

            return redirect('admin_user_verifications')

        # معالجة الإجراءات الفردية
        user_id = request.POST.get('user_id')

        try:
            user = User.objects.get(id=user_id)

            if action == 'approve':
                notes = request.POST.get('notes', '')
                user.approve_verification(request.user, notes)
                messages.success(request, f"تم قبول طلب {user.get_full_name()} بنجاح.")

            elif action == 'reject':
                reason = request.POST.get('reason', '')
                notes = request.POST.get('notes', '')
                if not reason:
                    messages.error(request, "يرجى إدخال سبب الرفض.")
                else:
                    user.reject_verification(request.user, reason, notes)
                    messages.success(request, f"تم رفض طلب {user.get_full_name()}.")

            elif action == 'under_review':
                user.verification_status = 'under_review'
                user.save()
                messages.info(request, f"تم وضع طلب {user.get_full_name()} قيد المراجعة.")

            elif action == 'ban_temporary':
                ban_reason = request.POST.get('ban_reason', '')
                ban_duration = request.POST.get('ban_duration', '')
                ban_unit = request.POST.get('ban_unit', 'days')

                if not ban_reason:
                    messages.error(request, "يرجى إدخال سبب الحظر.")
                elif not ban_duration:
                    messages.error(request, "يرجى إدخال مدة الحظر.")
                else:
                    try:
                        from django.utils import timezone
                        from datetime import timedelta

                        duration = int(ban_duration)
                        if ban_unit == 'hours':
                            banned_until = timezone.now() + timedelta(hours=duration)
                        elif ban_unit == 'days':
                            banned_until = timezone.now() + timedelta(days=duration)
                        elif ban_unit == 'weeks':
                            banned_until = timezone.now() + timedelta(weeks=duration)
                        elif ban_unit == 'months':
                            banned_until = timezone.now() + timedelta(days=duration * 30)
                        else:
                            banned_until = timezone.now() + timedelta(days=duration)

                        user.ban_user(request.user, 'temporary', ban_reason, banned_until)
                        messages.success(request, f"تم حظر {user.get_full_name()} مؤقتاً حتى {banned_until.strftime('%Y-%m-%d %H:%M')}.")

                    except ValueError:
                        messages.error(request, "مدة الحظر يجب أن تكون رقماً صحيحاً.")
                    except Exception as e:
                        messages.error(request, f"حدث خطأ أثناء الحظر: {str(e)}")

            elif action == 'ban_permanent':
                ban_reason = request.POST.get('ban_reason', '')

                if not ban_reason:
                    messages.error(request, "يرجى إدخال سبب الحظر.")
                else:
                    user.ban_user(request.user, 'permanent', ban_reason)
                    messages.success(request, f"تم حظر {user.get_full_name()} نهائياً.")

            elif action == 'unban':
                user.unban_user()
                messages.success(request, f"تم إلغاء حظر {user.get_full_name()}.")

            elif action == 'delete_user':
                delete_reason = request.POST.get('delete_reason', '')
                confirm_delete = request.POST.get('confirm_delete', '')

                if delete_reason and confirm_delete:
                    try:
                        # استخدام دالة الحذف الآمنة
                        from .bulk_operations import safe_bulk_delete
                        from django.contrib.auth import get_user_model
                        User = get_user_model()

                        # إنشاء QuerySet يحتوي على المستخدم الواحد
                        single_user_queryset = User.objects.filter(id=user.id)
                        safe_bulk_delete(request, single_user_queryset, delete_reason)

                    except Exception as e:
                        messages.error(request, f"حدث خطأ أثناء الحذف: {str(e)}")
                else:
                    messages.error(request, 'يرجى تحديد سبب الحذف وتأكيد العملية')

        except User.DoesNotExist:
            messages.error(request, "المستخدم غير موجود.")
        except Exception as e:
            messages.error(request, f"حدث خطأ: {str(e)}")

        return redirect('admin_user_verifications')

    # إضافة pagination لتحسين الأداء
    from django.core.paginator import Paginator

    paginator = Paginator(users, 25)  # 25 مستخدم في كل صفحة
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'users': page_obj,
        'stats': stats,
        'status_filter': status_filter,
        'user_type_filter': user_type_filter,
        'search_query': search_query,
        'ban_status_filter': ban_status_filter,
        'registration_period_filter': registration_period_filter,
        'date_from': date_from,
        'date_to': date_to,
        'status_choices': User.VERIFICATION_STATUS_CHOICES,
        'user_type_choices': [('student', 'طالب'), ('teacher', 'معلم')],
        'paginator': paginator,
        'page_obj': page_obj,
    }

    return render(request, 'admin/user_verifications.html', context)


@login_required
def admin_create_live_lesson(request):
    """إنشاء حصة مباشرة جديدة مع Jitsi Meet"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from lessons.models import LiveLesson
    from django.utils import timezone
    from datetime import datetime, timedelta

    if request.method == 'POST':
        title = request.POST.get('title')
        description = request.POST.get('description', '')
        teacher_id = request.POST.get('teacher_id')
        student_id = request.POST.get('student_id')
        scheduled_date = request.POST.get('scheduled_date')
        duration_minutes = request.POST.get('duration_minutes', 45)
        meeting_platform = 'jitsi'  # استخدام Jitsi فقط

        try:
            # التحقق من وجود المعلم والطالب
            teacher = User.objects.get(id=teacher_id, user_type='teacher', is_active=True)
            student = User.objects.get(id=student_id, user_type='student', is_active=True)

            # تحويل التاريخ والوقت
            if scheduled_date:
                scheduled_datetime = datetime.fromisoformat(scheduled_date.replace('T', ' '))
            else:
                scheduled_datetime = timezone.now()

            # إنشاء الحصة المباشرة
            live_lesson = LiveLesson.objects.create(
                title=title,
                description=description,
                teacher=teacher,
                student=student,
                scheduled_date=scheduled_datetime,
                duration_minutes=int(duration_minutes),
                meeting_platform=meeting_platform,
                status='scheduled',
                created_by=request.user
            )

            messages.success(request, f"تم إنشاء الحصة المباشرة '{title}' بنجاح!")
            messages.info(request, f"معرف غرفة Jitsi: {live_lesson.jitsi_room_id}")
            messages.info(request, f"كلمة مرور الغرفة: {live_lesson.jitsi_room_password}")

            return redirect('admin_live_lessons')

        except User.DoesNotExist:
            messages.error(request, "المعلم أو الطالب غير موجود أو غير نشط.")
        except ValueError as e:
            messages.error(request, f"خطأ في التاريخ والوقت: {str(e)}")
        except Exception as e:
            messages.error(request, f"حدث خطأ: {str(e)}")

    # جلب المعلمين والطلاب النشطين
    teachers = User.objects.filter(
        user_type='teacher',
        is_active=True,
        verification_status='approved'
    ).order_by('first_name', 'last_name')

    students = User.objects.filter(
        user_type='student',
        is_active=True,
        verification_status='approved'
    ).order_by('first_name', 'last_name')

    # الوقت الافتراضي (الآن + 30 دقيقة)
    default_time = timezone.now() + timedelta(minutes=30)

    context = {
        'teachers': teachers,
        'students': students,
        'default_time': default_time.strftime('%Y-%m-%dT%H:%M'),
        'duration_choices': [
            (30, '30 دقيقة'),
            (45, '45 دقيقة'),
            (60, '60 دقيقة'),
            (90, '90 دقيقة'),
        ],
    }

    return render(request, 'admin/create_live_lesson.html', context)


@login_required
def live_lesson_room(request, lesson_id):
    """صفحة غرفة الحصة المباشرة مع Jitsi Meet"""
    from lessons.models import LiveLesson
    from django.shortcuts import get_object_or_404

    live_lesson = get_object_or_404(LiveLesson, id=lesson_id)

    # التحقق من الصلاحيات
    if not (request.user.is_admin() or
            request.user == live_lesson.teacher or
            request.user == live_lesson.student):
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الحصة.")
        return redirect('/dashboard/')

    context = {
        'live_lesson': live_lesson,
    }

    return render(request, 'live_lesson_room.html', context)


import json

@login_required
@require_http_methods(["POST"])
def api_start_live_lesson(request, lesson_id):
    """API لبدء الحصة المباشرة"""
    from lessons.models import LiveLesson
    from django.shortcuts import get_object_or_404

    try:
        live_lesson = get_object_or_404(LiveLesson, id=lesson_id)

        # التحقق من الصلاحيات
        if not (request.user.is_admin() or request.user == live_lesson.teacher):
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية لبدء هذه الحصة'})

        if live_lesson.status != 'scheduled':
            return JsonResponse({'success': False, 'message': 'لا يمكن بدء هذه الحصة'})

        live_lesson.start_lesson()

        return JsonResponse({
            'success': True,
            'message': 'تم بدء الحصة بنجاح',
            'status': live_lesson.status
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


# تم نقل API إنهاء الحصة إلى users/api_views.py لتجنب التكرار


@login_required
@require_http_methods(["GET"])
def api_live_lesson_status(request, lesson_id):
    """API للحصول على حالة الحصة المباشرة"""
    from lessons.models import LiveLesson
    from django.shortcuts import get_object_or_404

    try:
        live_lesson = get_object_or_404(LiveLesson, id=lesson_id)

        # التحقق من الصلاحيات
        if not (request.user.is_admin() or
                request.user == live_lesson.teacher or
                request.user == live_lesson.student):
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الحصة'})

        # التحقق من ضرورة إنهاء الحصة تلقائياً
        auto_ended = live_lesson.auto_end_if_needed()

        response_data = {
            'success': True,
            'status': live_lesson.status,
            'started_at': live_lesson.started_at.isoformat() if live_lesson.started_at else None,
            'ended_at': live_lesson.ended_at.isoformat() if live_lesson.ended_at else None,
            'auto_ended': auto_ended
        }

        if auto_ended:
            response_data['message'] = 'تم إنهاء الحصة تلقائياً بعد انقضاء المدة المحددة'

        return JsonResponse(response_data)

    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@login_required
@require_http_methods(["GET"])
def api_admin_live_lessons_status(request):
    """API للمدير لمراقبة حالة جميع الحصص المباشرة"""
    if not request.user.is_admin():
        return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه البيانات'})

    from lessons.models import LiveLesson

    try:
        # جلب جميع الحصص المباشرة الجارية
        live_lessons = LiveLesson.objects.filter(status='live').select_related('teacher', 'student')

        lessons_data = []
        auto_ended_count = 0

        for lesson in live_lessons:
            # التحقق من ضرورة إنهاء الحصة تلقائياً
            auto_ended = lesson.auto_end_if_needed()
            if auto_ended:
                auto_ended_count += 1

            lessons_data.append({
                'id': lesson.id,
                'title': lesson.title,
                'teacher': lesson.teacher.get_full_name(),
                'student': lesson.student.get_full_name(),
                'status': lesson.status,
                'started_at': lesson.started_at.isoformat() if lesson.started_at else None,
                'duration_minutes': lesson.duration_minutes,
                'auto_ended': auto_ended
            })

        return JsonResponse({
            'success': True,
            'lessons': lessons_data,
            'auto_ended_count': auto_ended_count,
            'total_live_lessons': len([l for l in lessons_data if l['status'] == 'live'])
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@login_required
def teacher_live_lesson(request, lesson_id):
    """صفحة الحصة المباشرة للمعلم مع النظام الجديد"""
    from lessons.models import LiveLesson, UniversalLessonMonitoring
    from lessons.universal_manager import UniversalLessonManager
    from django.shortcuts import get_object_or_404

    live_lesson = get_object_or_404(LiveLesson, id=lesson_id)

    # التحقق من أن المستخدم هو المعلم المسؤول عن الحصة
    if not (request.user.is_admin() or request.user == live_lesson.teacher):
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الحصة.")
        return redirect('/dashboard/')

    # التحقق من وجود سجل مراقبة سابق
    existing_monitoring = UniversalLessonMonitoring.objects.filter(
        live_lesson=live_lesson,
        user=request.user
    ).first()

    # جلب سجلات مراقبة الطلاب
    student_monitoring = UniversalLessonMonitoring.objects.filter(
        live_lesson=live_lesson,
        user_role='student'
    ).select_related('user').first()

    context = {
        'live_lesson': live_lesson,
        'existing_monitoring': existing_monitoring,
        'student_monitoring': student_monitoring,
        'monitoring_settings': UniversalLessonManager.HEARTBEAT_SETTINGS,
    }

    return render(request, 'teacher/live_lesson_new.html', context)


@login_required
def teacher_scheduled_lesson(request, lesson_id):
    """صفحة الحصة المجدولة للمعلم"""
    from subscriptions.models import ScheduledLesson
    from lessons.models import LiveLesson
    from django.shortcuts import get_object_or_404
    from django.utils import timezone

    # التحقق من أن المستخدم معلم
    if not request.user.is_teacher():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    # الحصول على الحصة المجدولة
    scheduled_lesson = get_object_or_404(ScheduledLesson,
                                       id=lesson_id,
                                       teacher=request.user,
                                       status='scheduled')

    # التحقق من توقيت الحصة
    now = timezone.now()
    lesson_time = scheduled_lesson.scheduled_date
    time_diff = (lesson_time - now).total_seconds() / 60  # بالدقائق

    # إذا كانت الحصة قبل موعدها بأكثر من 5 دقائق (للمعلم) أو 15 دقيقة (للطالب)
    max_early_minutes = 5 if request.user.is_teacher() else 15
    if time_diff > max_early_minutes:
        messages.warning(request, f"لا يمكن دخول الحصة قبل {int(time_diff)} دقيقة من موعدها. {'يمكنك الدخول قبل 5 دقائق فقط من الموعد.' if request.user.is_teacher() else 'يمكنك الدخول قبل 15 دقيقة من الموعد.'}")
        return redirect('/dashboard/')

    # التحقق من وجود حصة مباشرة مرتبطة بالحصة المجدولة
    if hasattr(scheduled_lesson, 'live_lesson_id') and scheduled_lesson.live_lesson_id:
        try:
            live_lesson = LiveLesson.objects.get(id=scheduled_lesson.live_lesson_id)
            return redirect('teacher_live_lesson', lesson_id=live_lesson.id)
        except LiveLesson.DoesNotExist:
            pass

    # إنشاء حصة مباشرة جديدة من الحصة المجدولة
    live_lesson = LiveLesson.objects.create(
        title=f"حصة رقم {scheduled_lesson.lesson_number}",
        description=f"حصة من باقة {scheduled_lesson.subscription.plan.name}",
        teacher=scheduled_lesson.teacher,
        student=scheduled_lesson.subscription.student,
        scheduled_date=scheduled_lesson.scheduled_date,
        duration_minutes=scheduled_lesson.duration_minutes,
        status='scheduled',
        created_by=request.user
    )

    # تحديث الحصة المجدولة لربطها بالحصة المباشرة
    scheduled_lesson.status = 'converted_to_live'
    scheduled_lesson.live_lesson_id = live_lesson.id
    scheduled_lesson.save()

    # إرسال إشعار للطالب
    from notifications.models import Notification
    try:
        Notification.objects.create(
            user=scheduled_lesson.subscription.student,
            title="حصة مباشرة جديدة",
            message=f"بدأ المعلم {request.user.get_full_name()} الحصة رقم {scheduled_lesson.lesson_number}",
            notification_type='lesson_started'
        )
    except Exception:
        pass  # تجاهل أخطاء الإشعارات

    # إعادة توجيه إلى صفحة الحصة المباشرة
    return redirect('teacher_live_lesson', lesson_id=live_lesson.id)


@login_required
def student_live_lesson(request, lesson_id):
    """صفحة الحصة المباشرة للطالب مع النظام الجديد"""
    from lessons.models import LiveLesson, UniversalLessonMonitoring
    from lessons.universal_manager import UniversalLessonManager
    from django.shortcuts import get_object_or_404

    live_lesson = get_object_or_404(LiveLesson, id=lesson_id)

    # التحقق من أن المستخدم هو الطالب المسجل في الحصة
    if not (request.user.is_admin() or request.user == live_lesson.student):
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الحصة.")
        return redirect('/dashboard/')

    # التحقق من وجود سجل مراقبة سابق
    existing_monitoring = UniversalLessonMonitoring.objects.filter(
        live_lesson=live_lesson,
        user=request.user
    ).first()

    context = {
        'live_lesson': live_lesson,
        'existing_monitoring': existing_monitoring,
        'monitoring_settings': UniversalLessonManager.HEARTBEAT_SETTINGS,
    }

    # استخدام Template الجديد
    return render(request, 'student/live_lesson_new.html', context)


@login_required
@require_http_methods(["GET"])
def api_live_lesson_status(request, lesson_id):
    """API للتحقق من حالة الحصة المباشرة"""
    from lessons.models import LiveLesson
    from django.shortcuts import get_object_or_404

    try:
        live_lesson = get_object_or_404(LiveLesson, id=lesson_id)

        # التحقق من الصلاحيات
        if not (request.user.is_admin() or
                request.user == live_lesson.teacher or
                request.user == live_lesson.student):
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الحصة'})

        return JsonResponse({
            'success': True,
            'status': live_lesson.status,
            'started_at': live_lesson.started_at.isoformat() if live_lesson.started_at else None,
            'ended_at': live_lesson.ended_at.isoformat() if live_lesson.ended_at else None,
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@login_required
@require_http_methods(["GET"])
def api_admin_live_lessons_status(request):
    """API للحصول على حالة الحصص المباشرة للمدير"""
    if not request.user.is_admin():
        return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه البيانات'})

    try:
        from lessons.models import LiveLesson
        from django.utils import timezone

        # الحصص المباشرة الجارية
        live_count = LiveLesson.objects.filter(status='live').count()

        # الحصص المباشرة المجدولة
        scheduled_count = LiveLesson.objects.filter(
            status='scheduled',
            scheduled_date__gte=timezone.now()
        ).count()

        # الحصص المباشرة اليوم
        today = timezone.now().date()
        today_count = LiveLesson.objects.filter(
            scheduled_date__date=today
        ).count()

        # المعلمون النشطون في الحصص المباشرة
        active_teachers = LiveLesson.objects.filter(
            status='live'
        ).values('teacher').distinct().count()

        return JsonResponse({
            'success': True,
            'live_count': live_count,
            'scheduled_count': scheduled_count,
            'today_count': today_count,
            'active_teachers': active_teachers,
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


# ==================== إعدادات SMTP والإشعارات البريدية ====================

@login_required
def smtp_settings(request):
    """صفحة إعدادات SMTP"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    settings = AcademySettings.objects.first()
    if not settings:
        settings = AcademySettings.objects.create()

    if request.method == 'POST':
        # حفظ إعدادات SMTP
        settings.smtp_enabled = request.POST.get('smtp_enabled') == 'on'
        settings.smtp_provider = request.POST.get('smtp_provider', 'custom')
        settings.smtp_host = request.POST.get('smtp_host', '')
        settings.smtp_port = int(request.POST.get('smtp_port', 587))
        settings.smtp_username = request.POST.get('smtp_username', '')

        # تشفير كلمة المرور إذا تم إدخالها
        password = request.POST.get('smtp_password', '')
        if password:
            encryption = PasswordEncryption()
            settings.smtp_password = encryption.encrypt_password(password)

        settings.smtp_use_tls = request.POST.get('smtp_use_tls') == 'on'
        settings.smtp_use_ssl = request.POST.get('smtp_use_ssl') == 'on'
        settings.smtp_from_email = request.POST.get('smtp_from_email', '')
        settings.smtp_from_name = request.POST.get('smtp_from_name', '')
        settings.smtp_reply_to = request.POST.get('smtp_reply_to', '')
        settings.smtp_max_attachments = int(request.POST.get('smtp_max_attachments', 5))
        settings.smtp_max_attachment_size = int(request.POST.get('smtp_max_attachment_size', 25))
        settings.email_tracking_enabled = request.POST.get('email_tracking_enabled') == 'on'
        settings.manual_approval_required = request.POST.get('manual_approval_required') == 'on'

        settings.save()
        messages.success(request, 'تم حفظ إعدادات SMTP بنجاح')
        return redirect('smtp_settings')

    # إعدادات مسبقة لمزودي الخدمة
    from .services import SMTP_PRESETS

    context = {
        'settings': settings,
        'smtp_presets': SMTP_PRESETS,
    }

    return render(request, 'admin/smtp_settings.html', context)


@login_required
@require_http_methods(["POST"])
def test_smtp_connection(request):
    """اختبار الاتصال بخادم SMTP"""
    if not request.user.is_admin():
        return JsonResponse({'success': False, 'message': 'غير مصرح'})

    try:
        import json
        data = json.loads(request.body)

        # استخراج البيانات من الطلب
        smtp_host = data.get('smtp_host', '').strip()
        smtp_port = int(data.get('smtp_port', 587))
        smtp_username = data.get('smtp_username', '').strip()
        smtp_password = data.get('smtp_password', '').strip()
        smtp_use_tls = data.get('smtp_use_tls', False)
        smtp_use_ssl = data.get('smtp_use_ssl', False)

        # التحقق من البيانات المطلوبة
        if not smtp_host or not smtp_username:
            return JsonResponse({
                'success': False,
                'message': 'يرجى إدخال خادم SMTP واسم المستخدم'
            })

        # إذا لم يتم إدخال كلمة مرور، استخدم المحفوظة
        if not smtp_password:
            settings = AcademySettings.objects.first()
            if settings and settings.smtp_password:
                try:
                    encryption = PasswordEncryption()
                    smtp_password = encryption.decrypt_password(settings.smtp_password)
                except:
                    return JsonResponse({
                        'success': False,
                        'message': 'كلمة المرور المحفوظة غير صالحة، يرجى إدخال كلمة مرور جديدة'
                    })

        if not smtp_password:
            return JsonResponse({
                'success': False,
                'message': 'يرجى إدخال كلمة المرور'
            })

        # اختبار الاتصال
        import smtplib
        from email.mime.text import MIMEText

        try:
            # إنشاء الاتصال
            if smtp_use_ssl:
                server = smtplib.SMTP_SSL(smtp_host, smtp_port)
            else:
                server = smtplib.SMTP(smtp_host, smtp_port)
                if smtp_use_tls:
                    server.starttls()

            # تسجيل الدخول
            server.login(smtp_username, smtp_password)
            server.quit()

            return JsonResponse({
                'success': True,
                'message': 'تم الاتصال بخادم SMTP بنجاح!'
            })

        except smtplib.SMTPAuthenticationError:
            return JsonResponse({
                'success': False,
                'message': 'خطأ في المصادقة: تحقق من اسم المستخدم وكلمة المرور'
            })
        except smtplib.SMTPConnectError:
            return JsonResponse({
                'success': False,
                'message': 'فشل الاتصال بالخادم: تحقق من عنوان الخادم والمنفذ'
            })
        except smtplib.SMTPServerDisconnected:
            return JsonResponse({
                'success': False,
                'message': 'انقطع الاتصال مع الخادم'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'خطأ في الاتصال: {str(e)}'
            })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'خطأ في تحليل البيانات'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'خطأ غير متوقع: {str(e)}'
        })


@login_required
def email_tracking_pixel(request, tracking_id):
    """صورة تتبع فتح الرسائل"""
    try:
        tracking = EmailTracking.objects.get(tracking_id=tracking_id)

        # تحديث معلومات التتبع
        if not tracking.is_opened:
            tracking.is_opened = True
            tracking.opened_at = timezone.now()

        tracking.open_count += 1
        tracking.ip_address = request.META.get('REMOTE_ADDR')
        tracking.user_agent = request.META.get('HTTP_USER_AGENT', '')
        tracking.save()

    except EmailTracking.DoesNotExist:
        pass

    # إرجاع صورة شفافة 1x1 بكسل
    pixel_data = base64.b64decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7')
    return HttpResponse(pixel_data, content_type='image/gif')


@login_required
def email_notifications_dashboard(request):
    """لوحة تحكم الإشعارات البريدية"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.db.models import Count, Q
    from django.core.paginator import Paginator

    # إحصائيات الإشعارات
    total_notifications = EmailNotification.objects.count()
    sent_notifications = EmailNotification.objects.filter(status='sent').count()
    pending_notifications = EmailNotification.objects.filter(status='pending').count()
    failed_notifications = EmailNotification.objects.filter(status='failed').count()

    # إحصائيات التتبع
    tracked_emails = EmailTracking.objects.count()
    opened_emails = EmailTracking.objects.filter(is_opened=True).count()
    open_rate = (opened_emails / tracked_emails * 100) if tracked_emails > 0 else 0

    # طلبات الموافقة المعلقة
    pending_approvals = EmailApprovalRequest.objects.filter(status='pending').count()

    # آخر الإشعارات
    recent_notifications = EmailNotification.objects.select_related('recipient').order_by('-created_at')[:10]

    # طلبات الموافقة المعلقة
    approval_requests = EmailApprovalRequest.objects.filter(
        status='pending'
    ).select_related('created_by').order_by('-created_at')[:5]

    context = {
        'total_notifications': total_notifications,
        'sent_notifications': sent_notifications,
        'pending_notifications': pending_notifications,
        'failed_notifications': failed_notifications,
        'tracked_emails': tracked_emails,
        'opened_emails': opened_emails,
        'open_rate': round(open_rate, 1),
        'pending_approvals': pending_approvals,
        'recent_notifications': recent_notifications,
        'approval_requests': approval_requests,
    }

    return render(request, 'admin/email_notifications_dashboard.html', context)





@login_required
@require_http_methods(["POST"])
def send_test_email(request):
    """إرسال رسالة تجريبية"""
    if not request.user.is_admin():
        return JsonResponse({'success': False, 'message': 'غير مصرح'})

    try:
        import json
        data = json.loads(request.body)

        recipient_email = data.get('recipient_email', '').strip()
        if not recipient_email:
            return JsonResponse({
                'success': False,
                'message': 'يرجى إدخال البريد الإلكتروني للمستلم'
            })

        # التحقق من إعدادات SMTP
        settings = AcademySettings.objects.first()
        if not settings or not settings.smtp_enabled:
            return JsonResponse({
                'success': False,
                'message': 'إعدادات SMTP غير مفعلة'
            })

        # إرسال رسالة تجريبية
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart

        try:
            # فك تشفير كلمة المرور
            encryption = PasswordEncryption()
            smtp_password = encryption.decrypt_password(settings.smtp_password)

            # إنشاء الرسالة
            msg = MIMEMultipart()

            # تنسيق حقل From بشكل صحيح حسب RFC 5322
            from email.utils import formataddr
            try:
                # تنظيف اسم المرسل من أي أحرف خاصة قد تسبب مشاكل
                clean_from_name = settings.smtp_from_name.replace('"', '').replace('<', '').replace('>', '').strip()
                msg['From'] = formataddr((clean_from_name, settings.smtp_from_email))
            except Exception as e:
                # في حالة فشل التنسيق، استخدم البريد الإلكتروني فقط
                msg['From'] = settings.smtp_from_email

            msg['To'] = recipient_email
            msg['Subject'] = "رسالة تجريبية من نظام إدارة التعلم"

            # محتوى الرسالة
            body = f"""
            السلام عليكم ورحمة الله وبركاته،

            هذه رسالة تجريبية من نظام إدارة التعلم لأكاديمية {settings.academy_name}.

            إذا وصلتك هذه الرسالة، فهذا يعني أن إعدادات SMTP تعمل بشكل صحيح.

            تاريخ الإرسال: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}

            مع أطيب التحيات،
            فريق {settings.academy_name}
            """

            msg.attach(MIMEText(body, 'plain', 'utf-8'))

            # إرسال الرسالة
            if settings.smtp_use_ssl:
                server = smtplib.SMTP_SSL(settings.smtp_host, settings.smtp_port)
            else:
                server = smtplib.SMTP(settings.smtp_host, settings.smtp_port)
                if settings.smtp_use_tls:
                    server.starttls()

            server.login(settings.smtp_username, smtp_password)
            server.send_message(msg)
            server.quit()

            return JsonResponse({
                'success': True,
                'message': f'تم إرسال الرسالة التجريبية بنجاح إلى {recipient_email}'
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'خطأ في إرسال الرسالة: {str(e)}'
            })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'خطأ في تحليل البيانات'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'خطأ غير متوقع: {str(e)}'
        })


@login_required
def technical_settings(request):
    """صفحة الإعدادات التقنية"""
    if not request.user.user_type == 'admin':
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from django.urls import reverse

    context = {
        'page_title': 'الإعدادات التقنية',
        'breadcrumb': [
            {'name': 'لوحة التحكم', 'url': reverse('admin_dashboard')},
            {'name': 'الإعدادات التقنية', 'url': None}
        ]
    }
    return render(request, 'admin/technical_settings.html', context)


@login_required
def payment_gateway_settings(request):
    """صفحة إعدادات بوابات الدفع"""
    if not request.user.user_type == 'admin':
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    from .models import PaymentGatewaySettings

    # الحصول على الإعدادات الحالية مباشرة من قاعدة البيانات
    try:
        payment_settings = PaymentGatewaySettings.objects.get(pk=1)
        # إعادة تحميل البيانات من قاعدة البيانات للتأكد من الحداثة
        payment_settings.refresh_from_db()
    except PaymentGatewaySettings.DoesNotExist:
        payment_settings = PaymentGatewaySettings.objects.create(
            pk=1,
            paypal_enabled=False,
            stripe_enabled=False,
            bank_transfer_enabled=True
        )



    context = {
        'payment_settings': payment_settings,
        'page_title': 'إعدادات بوابات الدفع',
    }
    return render(request, 'admin/payment_gateway_settings.html', context)


@login_required
@require_http_methods(["POST"])
def save_payment_gateway_settings(request):
    """حفظ إعدادات بوابات الدفع"""
    if not request.user.user_type == 'admin':
        return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الصفحة.'})

    from .models import PaymentGatewaySettings

    # الحصول على الإعدادات مباشرة من قاعدة البيانات
    try:
        settings = PaymentGatewaySettings.objects.get(pk=1)
        # إعادة تحميل البيانات من قاعدة البيانات للتأكد من الحداثة
        settings.refresh_from_db()
    except PaymentGatewaySettings.DoesNotExist:
        settings = PaymentGatewaySettings.objects.create(
            pk=1,
            paypal_enabled=False,
            stripe_enabled=False,
            bank_transfer_enabled=True,
            updated_by=request.user
        )

    gateway_type = request.POST.get('gateway_type')

    try:
        if gateway_type == 'paypal':
            # حفظ إعدادات PayPal
            settings.paypal_client_id = request.POST.get('paypal_client_id', '')
            settings.paypal_client_secret = request.POST.get('paypal_client_secret', '')
            settings.paypal_webhook_id = request.POST.get('paypal_webhook_id', '')
            settings.paypal_sandbox_mode = 'paypal_sandbox_mode' in request.POST
            settings.paypal_enabled = request.POST.get('paypal_enabled') == '1'

            success_message = "تم حفظ إعدادات PayPal بنجاح!"

        elif gateway_type == 'stripe':
            # حفظ إعدادات Stripe
            settings.stripe_publishable_key = request.POST.get('stripe_publishable_key', '')
            settings.stripe_secret_key = request.POST.get('stripe_secret_key', '')
            settings.stripe_webhook_secret = request.POST.get('stripe_webhook_secret', '')
            settings.stripe_enabled = request.POST.get('stripe_enabled') == '1'

            success_message = "تم حفظ إعدادات Stripe بنجاح!"

        elif gateway_type == 'bank':
            # حفظ إعدادات التحويل البنكي
            settings.bank_name = request.POST.get('bank_name', '')
            settings.bank_account_number = request.POST.get('bank_account_number', '')
            settings.bank_account_name = request.POST.get('bank_account_name', '')
            settings.bank_iban = request.POST.get('bank_iban', '')
            settings.bank_swift_code = request.POST.get('bank_swift_code', '')
            settings.bank_branch = request.POST.get('bank_branch', '')
            settings.bank_transfer_enabled = request.POST.get('bank_transfer_enabled') == '1'

            success_message = "تم حفظ إعدادات التحويل البنكي بنجاح!"
        else:
            return JsonResponse({'success': False, 'message': 'نوع بوابة الدفع غير صحيح'})

        # حفظ معلومات التحديث
        settings.updated_by = request.user

        # حفظ البيانات مع transaction للتأكد من الحفظ
        from django.db import transaction
        try:
            with transaction.atomic():
                settings.save()
                # التأكد من الحفظ بقراءة البيانات مرة أخرى
                settings.refresh_from_db()
        except Exception as save_error:
            return JsonResponse({'success': False, 'message': f'خطأ في حفظ الإعدادات: {str(save_error)}'})

        # مسح التخزين المؤقت للإعدادات
        from .payment_service import payment_service
        payment_service.clear_cache()

        # إرجاع الحالة الحقيقية من قاعدة البيانات
        current_status = {
            'paypal': settings.paypal_enabled,
            'stripe': settings.stripe_enabled,
            'bank': settings.bank_transfer_enabled
        }

        return JsonResponse({
            'success': True,
            'message': success_message,
            'gateway_type': gateway_type,
            'current_status': current_status
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ أثناء حفظ الإعدادات: {str(e)}'})


@login_required
@require_http_methods(["POST"])
def test_payment_gateway(request, gateway):
    """اختبار الاتصال الحقيقي مع بوابة دفع محددة"""
    if not request.user.user_type == 'admin':
        return JsonResponse({'success': False, 'message': 'غير مصرح لك بهذا الإجراء'})

    from .models import PaymentGatewaySettings
    import json
    import requests
    import time

    settings = PaymentGatewaySettings.get_settings()

    try:
        if gateway == 'paypal':
            # اختبار PayPal الحقيقي والشامل
            if not settings.paypal_client_id or not settings.paypal_client_secret:
                return JsonResponse({
                    'success': False,
                    'message': 'يرجى إدخال Client ID و Client Secret أولاً'
                })

            try:
                # تحديد URL بناءً على وضع التجربة
                if settings.paypal_sandbox_mode:
                    auth_url = "https://api.sandbox.paypal.com/v1/oauth2/token"
                    api_base = "https://api.sandbox.paypal.com"
                    mode_text = "وضع التجربة (Sandbox)"
                else:
                    auth_url = "https://api.paypal.com/v1/oauth2/token"
                    api_base = "https://api.paypal.com"
                    mode_text = "الوضع المباشر (Production)"

                # إعداد البيانات للمصادقة
                auth_data = {
                    'grant_type': 'client_credentials'
                }

                # إعداد الهيدر
                import base64
                credentials = f"{settings.paypal_client_id}:{settings.paypal_client_secret}"
                encoded_credentials = base64.b64encode(credentials.encode()).decode()

                headers = {
                    'Accept': 'application/json',
                    'Accept-Language': 'en_US',
                    'Authorization': f'Basic {encoded_credentials}',
                    'Content-Type': 'application/x-www-form-urlencoded'
                }

                # الخطوة 1: اختبار المصادقة
                response = requests.post(auth_url, data=auth_data, headers=headers, timeout=15)

                if response.status_code == 200:
                    token_data = response.json()
                    if 'access_token' in token_data:
                        access_token = token_data['access_token']
                        token_type = token_data.get('token_type', 'Bearer')
                        expires_in = token_data.get('expires_in', 'غير محدد')

                        # الخطوة 2: اختبار API باستخدام الرمز المميز
                        api_headers = {
                            'Content-Type': 'application/json',
                            'Authorization': f'{token_type} {access_token}',
                            'Accept': 'application/json'
                        }

                        # اختبار جلب معلومات الحساب
                        account_response = requests.get(
                            f'{api_base}/v1/identity/oauth2/userinfo?schema=paypalv1.1',
                            headers=api_headers,
                            timeout=10
                        )

                        if account_response.status_code == 200:
                            account_info = account_response.json()
                            user_id = account_info.get('user_id', 'غير محدد')

                            return JsonResponse({
                                'success': True,
                                'message': f'✅ تم الاتصال بنجاح مع PayPal!\n\n'
                                          f'🔹 الوضع: {mode_text}\n'
                                          f'🔹 معرف المستخدم: {user_id}\n'
                                          f'🔹 صلاحية الرمز: {expires_in} ثانية\n'
                                          f'🔹 حالة الاتصال: نشط ومتصل'
                            })
                        else:
                            # حتى لو فشل جلب معلومات الحساب، المصادقة نجحت
                            return JsonResponse({
                                'success': True,
                                'message': f'✅ تم الاتصال بنجاح مع PayPal!\n\n'
                                          f'🔹 الوضع: {mode_text}\n'
                                          f'🔹 المصادقة: نجحت\n'
                                          f'🔹 صلاحية الرمز: {expires_in} ثانية\n'
                                          f'🔹 حالة الاتصال: نشط ومتصل'
                            })
                    else:
                        return JsonResponse({
                            'success': False,
                            'message': '❌ فشل في الحصول على رمز المصادقة من PayPal'
                        })
                elif response.status_code == 401:
                    return JsonResponse({
                        'success': False,
                        'message': '❌ بيانات المصادقة غير صحيحة\nتحقق من Client ID و Client Secret'
                    })
                else:
                    error_data = response.json() if response.content else {}
                    error_message = error_data.get('error_description', error_data.get('error', 'خطأ غير معروف'))
                    return JsonResponse({
                        'success': False,
                        'message': f'❌ فشل الاتصال مع PayPal\nالخطأ: {error_message}'
                    })

            except requests.exceptions.Timeout:
                return JsonResponse({
                    'success': False,
                    'message': '❌ انتهت مهلة الاتصال مع PayPal\nتحقق من الاتصال بالإنترنت أو حاول مرة أخرى'
                })
            except requests.exceptions.ConnectionError:
                return JsonResponse({
                    'success': False,
                    'message': '❌ فشل الاتصال مع خوادم PayPal\nتحقق من الاتصال بالإنترنت'
                })
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'message': f'❌ خطأ في اختبار PayPal: {str(e)}'
                })

        elif gateway == 'stripe':
            # اختبار Stripe الحقيقي والشامل
            if not settings.stripe_secret_key:
                return JsonResponse({
                    'success': False,
                    'message': 'يرجى إدخال Secret Key أولاً'
                })

            try:
                # تحديد نوع المفتاح
                is_test_key = settings.stripe_secret_key.startswith('sk_test_')
                is_live_key = settings.stripe_secret_key.startswith('sk_live_')

                if not (is_test_key or is_live_key):
                    return JsonResponse({
                        'success': False,
                        'message': '❌ مفتاح Stripe غير صحيح\nيجب أن يبدأ بـ sk_test_ أو sk_live_'
                    })

                mode_text = "وضع التجربة (Test)" if is_test_key else "الوضع المباشر (Live)"

                # استخدام Stripe API لاختبار الاتصال
                headers = {
                    'Authorization': f'Bearer {settings.stripe_secret_key}',
                    'Content-Type': 'application/x-www-form-urlencoded'
                }

                # الخطوة 1: جلب معلومات الحساب
                account_response = requests.get('https://api.stripe.com/v1/account', headers=headers, timeout=15)

                if account_response.status_code == 200:
                    account_data = account_response.json()
                    account_id = account_data.get('id', 'غير محدد')
                    business_name = account_data.get('business_profile', {}).get('name', 'غير محدد')
                    country = account_data.get('country', 'غير محدد')
                    charges_enabled = account_data.get('charges_enabled', False)
                    payouts_enabled = account_data.get('payouts_enabled', False)

                    # الخطوة 2: اختبار إنشاء عميل تجريبي (لا يتم حفظه)
                    test_customer_data = {
                        'email': '<EMAIL>',
                        'name': 'Test Customer',
                        'description': 'Test customer for connection verification'
                    }

                    customer_response = requests.post(
                        'https://api.stripe.com/v1/customers',
                        headers=headers,
                        data=test_customer_data,
                        timeout=10
                    )

                    if customer_response.status_code == 200:
                        customer_data = customer_response.json()
                        customer_id = customer_data.get('id', 'غير محدد')

                        # حذف العميل التجريبي فوراً
                        requests.delete(
                            f'https://api.stripe.com/v1/customers/{customer_id}',
                            headers=headers,
                            timeout=5
                        )

                        # تحديد حالة الحساب
                        account_status = "✅ نشط ومفعل" if (charges_enabled and payouts_enabled) else "⚠️ يحتاج تفعيل"

                        return JsonResponse({
                            'success': True,
                            'message': f'✅ تم الاتصال بنجاح مع Stripe!\n\n'
                                      f'🔹 الوضع: {mode_text}\n'
                                      f'🔹 معرف الحساب: {account_id}\n'
                                      f'🔹 اسم النشاط: {business_name}\n'
                                      f'🔹 البلد: {country}\n'
                                      f'🔹 حالة الحساب: {account_status}\n'
                                      f'🔹 اختبار API: نجح (تم إنشاء وحذف عميل تجريبي)'
                        })
                    else:
                        # حتى لو فشل إنشاء العميل، معلومات الحساب نجحت
                        account_status = "✅ نشط ومفعل" if (charges_enabled and payouts_enabled) else "⚠️ يحتاج تفعيل"

                        return JsonResponse({
                            'success': True,
                            'message': f'✅ تم الاتصال بنجاح مع Stripe!\n\n'
                                      f'🔹 الوضع: {mode_text}\n'
                                      f'🔹 معرف الحساب: {account_id}\n'
                                      f'🔹 اسم النشاط: {business_name}\n'
                                      f'🔹 البلد: {country}\n'
                                      f'🔹 حالة الحساب: {account_status}\n'
                                      f'🔹 حالة الاتصال: نشط ومتصل'
                        })

                elif account_response.status_code == 401:
                    return JsonResponse({
                        'success': False,
                        'message': '❌ مفتاح Stripe غير صحيح أو منتهي الصلاحية\nتحقق من Secret Key'
                    })
                else:
                    error_data = account_response.json() if account_response.content else {}
                    error_message = error_data.get('error', {}).get('message', 'خطأ غير معروف')
                    return JsonResponse({
                        'success': False,
                        'message': f'❌ فشل الاتصال مع Stripe\nالخطأ: {error_message}'
                    })

            except requests.exceptions.Timeout:
                return JsonResponse({
                    'success': False,
                    'message': '❌ انتهت مهلة الاتصال مع Stripe\nتحقق من الاتصال بالإنترنت أو حاول مرة أخرى'
                })
            except requests.exceptions.ConnectionError:
                return JsonResponse({
                    'success': False,
                    'message': '❌ فشل الاتصال مع خوادم Stripe\nتحقق من الاتصال بالإنترنت'
                })
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'message': f'❌ خطأ في اختبار Stripe: {str(e)}'
                })

        else:
            return JsonResponse({
                'success': False,
                'message': 'بوابة دفع غير مدعومة'
            })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ أثناء اختبار الاتصال: {str(e)}'
        })





@login_required
def admin_convert_scheduled_lessons(request):
    """تحويل الحصص المجدولة إلى حصص مباشرة يدوياً"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    if request.method == 'POST':
        try:
            from subscriptions.services import ScheduledLessonToLiveService
            converted_count = ScheduledLessonToLiveService.check_and_convert_lessons()

            if converted_count > 0:
                messages.success(request, f"تم تحويل {converted_count} حصة مجدولة إلى حصص مباشرة بنجاح!")
            else:
                messages.info(request, "لا توجد حصص مجدولة جاهزة للتحويل في الوقت الحالي.")

        except Exception as e:
            messages.error(request, f"حدث خطأ أثناء تحويل الحصص: {str(e)}")

    return redirect('admin_live_lessons_monitoring')


@login_required
@require_http_methods(["POST"])
def start_subscription_lesson(request, lesson_id):
    """بدء حصة مجدولة من الاشتراكات (للمعلمين)"""
    if not request.user.is_teacher():
        return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية لبدء الحصص'})

    try:
        from subscriptions.models import ScheduledLesson
        from lessons.models import LiveLesson
        from django.utils import timezone

        # الحصول على الحصة المجدولة
        scheduled_lesson = get_object_or_404(ScheduledLesson,
                                           id=lesson_id,
                                           teacher=request.user,
                                           status='scheduled')

        # التحقق من أن موعد الحصة قد حان (5 دقائق قبل الموعد فقط للمعلم)
        now = timezone.now()
        lesson_time = scheduled_lesson.scheduled_date
        time_diff = (lesson_time - now).total_seconds() / 60  # بالدقائق

        # المدير يمكنه البدء في أي وقت، المعلم فقط قبل 5 دقائق
        if request.user.is_teacher() and time_diff > 5:
            return JsonResponse({
                'success': False,
                'message': f'لا يمكن بدء الحصة قبل {int(time_diff)} دقيقة من موعدها. يمكنك البدء قبل 5 دقائق فقط من الموعد المحدد.'
            })

        # إنشاء حصة مباشرة جديدة
        live_lesson = LiveLesson.objects.create(
            title=f"حصة رقم {scheduled_lesson.lesson_number} - {scheduled_lesson.subscription.plan.name}",
            teacher=request.user,
            student=scheduled_lesson.subscription.student,
            scheduled_date=scheduled_lesson.scheduled_date,
            status='scheduled',
            notes=f"تم إنشاؤها من الحصة المجدولة رقم {scheduled_lesson.id}"
        )

        # تحديث حالة الحصة المجدولة
        scheduled_lesson.status = 'converted_to_live'
        scheduled_lesson.live_lesson_id = live_lesson.id
        scheduled_lesson.save()

        # إرسال إشعار للطالب
        from notifications.models import Notification
        Notification.objects.create(
            user=scheduled_lesson.subscription.student,
            title="حصة مباشرة جديدة",
            message=f"بدأ المعلم {request.user.get_full_name()} الحصة رقم {scheduled_lesson.lesson_number}",
            notification_type='lesson_started'
        )

        return JsonResponse({
            'success': True,
            'message': 'تم بدء الحصة بنجاح',
            'live_lesson_url': f'/dashboard/teacher/live-lesson/{live_lesson.id}/'
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})


@login_required
@require_http_methods(["POST"])
def join_subscription_lesson(request, lesson_id):
    """الانضمام لحصة مجدولة من الاشتراكات (للطلاب)"""
    if not request.user.is_student():
        return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية للانضمام للحصص'})

    try:
        from subscriptions.models import ScheduledLesson
        from lessons.models import LiveLesson

        # الحصول على الحصة المجدولة
        scheduled_lesson = get_object_or_404(ScheduledLesson,
                                           id=lesson_id,
                                           subscription__student=request.user)

        # التحقق من حالة الحصة
        if scheduled_lesson.status == 'converted_to_live':
            # الحصة تم تحويلها لحصة مباشرة
            live_lesson = LiveLesson.objects.get(id=scheduled_lesson.live_lesson_id)
            return JsonResponse({
                'success': True,
                'message': 'تم الانضمام للحصة',
                'live_lesson_url': f'/lessons/live/{live_lesson.id}/student/'
            })
        elif scheduled_lesson.status == 'scheduled':
            # الحصة لم تبدأ بعد
            return JsonResponse({
                'success': False,
                'message': 'الحصة لم تبدأ بعد. انتظر حتى يبدأها المعلم'
            })
        else:
            return JsonResponse({
                'success': False,
                'message': 'هذه الحصة غير متاحة'
            })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})


@login_required
def debug_scheduled_lessons(request):
    """صفحة تجريبية للتحقق من الحصص المجدولة"""
    from subscriptions.models import ScheduledLesson, StudentSubscription
    from lessons.models import LiveLesson
    from django.utils import timezone
    from datetime import timedelta

    now = timezone.now()

    # جلب جميع الحصص المجدولة
    all_scheduled_lessons = ScheduledLesson.objects.all().select_related(
        'subscription__student', 'subscription__plan', 'teacher'
    ).order_by('-scheduled_date')

    # جلب الحصص المجدولة للمستخدم الحالي
    user_scheduled_lessons = []
    if request.user.is_teacher():
        user_scheduled_lessons = ScheduledLesson.objects.filter(
            teacher=request.user,
            status='scheduled',
            scheduled_date__gte=now - timedelta(hours=2)
        ).select_related('subscription__student', 'subscription__plan')
    elif request.user.is_student():
        user_scheduled_lessons = ScheduledLesson.objects.filter(
            subscription__student=request.user,
            status='scheduled',
            scheduled_date__gte=now - timedelta(hours=2)
        ).select_related('teacher', 'subscription__plan')

    # جلب الحصص المباشرة
    live_lessons = LiveLesson.objects.filter(
        Q(teacher=request.user) | Q(student=request.user)
    ).order_by('-created_at')[:5]

    # جلب الاشتراكات النشطة
    active_subscriptions = StudentSubscription.objects.filter(
        status='active'
    ).select_related('student', 'plan')

    context = {
        'all_scheduled_lessons': all_scheduled_lessons,
        'user_scheduled_lessons': user_scheduled_lessons,
        'live_lessons': live_lessons,
        'active_subscriptions': active_subscriptions,
        'current_time': now,
        'user_type': request.user.user_type,
    }

    return render(request, 'debug/scheduled_lessons.html', context)


@login_required
@require_http_methods(["POST"])
def reset_payment_gateway_settings(request):
    """إعادة تعيين إعدادات بوابات الدفع"""
    if not request.user.is_admin():
        return JsonResponse({'success': False, 'message': 'غير مصرح لك بهذا الإجراء'})

    try:
        from .models import PaymentGatewaySettings
        from .payment_service import payment_service

        # حذف جميع الإعدادات الموجودة
        PaymentGatewaySettings.objects.all().delete()

        # إنشاء إعدادات جديدة بالقيم الافتراضية
        settings = PaymentGatewaySettings.objects.create(
            paypal_enabled=False,
            stripe_enabled=False,
            bank_transfer_enabled=True,  # التحويل البنكي مفعل افتراضي<|im_start|>
            updated_by=request.user
        )

        # مسح التخزين المؤقت
        payment_service.clear_cache()

        return JsonResponse({
            'success': True,
            'message': 'تم إعادة تعيين إعدادات بوابات الدفع بنجاح',
            'current_status': {
                'paypal': settings.paypal_enabled,
                'stripe': settings.stripe_enabled,
                'bank': settings.bank_transfer_enabled
            }
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})


@login_required
def test_subscription_notifications(request):
    """صفحة اختبار إشعارات الاشتراكات"""
    if not request.user.is_admin():
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذه الصفحة.")
        return redirect('/dashboard/')

    if request.method == 'POST':
        action = request.POST.get('action')

        try:
            from subscriptions.models import StudentSubscription
            from notifications.utils import SubscriptionNotificationService
            from django.contrib.auth import get_user_model

            User = get_user_model()

            # البحث عن اشتراك للاختبار
            test_subscription = StudentSubscription.objects.filter(
                status__in=['active', 'pending_approval', 'payment_pending']
            ).first()

            if not test_subscription:
                messages.error(request, "لا يوجد اشتراكات للاختبار. يرجى إنشاء اشتراك أولاً.")
                return redirect('test_subscription_notifications')

            if action == 'test_created':
                SubscriptionNotificationService.notify_subscription_created(test_subscription)
                messages.success(request, f"تم إرسال إشعار إنشاء الاشتراك للطالب {test_subscription.student.get_full_name()}")

            elif action == 'test_payment_received':
                SubscriptionNotificationService.notify_payment_received(test_subscription)
                messages.success(request, f"تم إرسال إشعار استلام الدفعة للطالب والمدير")

            elif action == 'test_activated':
                SubscriptionNotificationService.notify_subscription_activated(test_subscription)
                messages.success(request, f"تم إرسال إشعار تفعيل الاشتراك للطالب {test_subscription.student.get_full_name()}")

            elif action == 'test_cancelled':
                SubscriptionNotificationService.notify_subscription_cancelled(test_subscription, "اختبار إلغاء الاشتراك")
                messages.success(request, f"تم إرسال إشعار إلغاء الاشتراك للطالب {test_subscription.student.get_full_name()}")

            elif action == 'test_expiring':
                SubscriptionNotificationService.notify_subscription_expiring_soon(test_subscription, 3)
                messages.success(request, f"تم إرسال إشعار اقتراب انتهاء الاشتراك (3 أيام)")

            elif action == 'test_lessons_exhausted':
                SubscriptionNotificationService.notify_lessons_exhausted(test_subscription)
                messages.success(request, f"تم إرسال إشعار انتهاء حصص الاشتراك")

            elif action == 'test_bank_transfer':
                SubscriptionNotificationService.notify_bank_transfer_submitted(test_subscription)
                messages.success(request, f"تم إرسال إشعار الحوالة البنكية للمدير")

            elif action == 'run_maintenance':
                from subscriptions.tasks import run_subscription_maintenance_task
                results = run_subscription_maintenance_task()
                messages.success(request, f"تم تشغيل مهام الصيانة: {results}")

        except Exception as e:
            messages.error(request, f"خطأ في إرسال الإشعار: {str(e)}")

    # جلب إحصائيات الإشعارات
    from notifications.models import Notification
    from subscriptions.models import StudentSubscription

    notification_stats = {
        'total_notifications': Notification.objects.count(),
        'subscription_notifications': Notification.objects.filter(
            notification_type__startswith='subscription_'
        ).count(),
        'recent_notifications': Notification.objects.order_by('-created_at')[:10],
        'total_subscriptions': StudentSubscription.objects.count(),
        'active_subscriptions': StudentSubscription.objects.filter(status='active').count(),
        'pending_subscriptions': StudentSubscription.objects.filter(status='pending_approval').count(),
    }

    context = {
        'notification_stats': notification_stats,
    }

    return render(request, 'admin/test_subscription_notifications.html', context)


@login_required
@require_http_methods(["POST"])
def create_test_scheduled_lessons(request):
    """إنشاء حصص مجدولة للاختبار"""
    from subscriptions.models import ScheduledLesson, StudentSubscription, SubscriptionPlan
    from django.utils import timezone
    from datetime import timedelta
    from django.contrib.auth import get_user_model

    User = get_user_model()

    try:
        # البحث عن اشتراك نشط أو إنشاء واحد
        active_subscription = StudentSubscription.objects.filter(status='active').first()

        if not active_subscription:
            # إنشاء باقة تجريبية
            plan, created = SubscriptionPlan.objects.get_or_create(
                name='باقة تجريبية للحصص المجدولة',
                defaults={
                    'description': 'باقة للاختبار',
                    'plan_type': 'individual',
                    'duration_type': 'monthly',
                    'duration_days': 30,
                    'price': 100.00,
                    'currency': 'USD',
                    'lessons_count': 8,
                    'lesson_duration': 45,
                    'lessons_per_week': 2,
                    'preferred_days': [1, 3],  # اثنين وأربعاء
                    'preferred_times': ['16:00', '18:00'],
                    'auto_schedule': True,
                    'is_active': True
                }
            )

            # البحث عن طالب أو إنشاء واحد
            student = User.objects.filter(user_type='student').first()
            if not student:
                student = User.objects.create_user(
                    username='test_student',
                    email='<EMAIL>',
                    password='testpass123',
                    first_name='طالب',
                    last_name='تجريبي',
                    user_type='student'
                )

            # إنشاء اشتراك
            active_subscription = StudentSubscription.objects.create(
                student=student,
                plan=plan,
                start_date=timezone.now().date(),
                end_date=timezone.now().date() + timedelta(days=30),
                status='active',
                remaining_lessons=8
            )

        # البحث عن معلم أو إنشاء واحد
        teacher = User.objects.filter(user_type='teacher').first()
        if not teacher:
            teacher = User.objects.create_user(
                username='test_teacher',
                email='<EMAIL>',
                password='testpass123',
                first_name='معلم',
                last_name='تجريبي',
                user_type='teacher'
            )

        # حذف الحصص المجدولة السابقة للاختبار
        ScheduledLesson.objects.filter(subscription=active_subscription).delete()

        # إنشاء حصص مجدولة للاختبار
        now = timezone.now()
        lessons_created = []

        # حصة خلال 30 دقيقة (ستظهر في القائمة الجانبية)
        lesson1 = ScheduledLesson.objects.create(
            subscription=active_subscription,
            lesson_number=1,
            scheduled_date=now + timedelta(minutes=30),
            duration_minutes=45,
            teacher=teacher,
            status='scheduled'
        )
        lessons_created.append(lesson1)

        # حصة غداً (ستظهر في القائمة الجانبية)
        lesson2 = ScheduledLesson.objects.create(
            subscription=active_subscription,
            lesson_number=2,
            scheduled_date=now + timedelta(days=1, hours=2),
            duration_minutes=45,
            teacher=teacher,
            status='scheduled'
        )
        lessons_created.append(lesson2)

        # حصة بعد غد (ستظهر في القائمة الجانبية)
        lesson3 = ScheduledLesson.objects.create(
            subscription=active_subscription,
            lesson_number=3,
            scheduled_date=now + timedelta(days=2, hours=3),
            duration_minutes=45,
            teacher=teacher,
            status='scheduled'
        )
        lessons_created.append(lesson3)

        # حصة الأسبوع القادم (ستظهر في القائمة الجانبية)
        lesson4 = ScheduledLesson.objects.create(
            subscription=active_subscription,
            lesson_number=4,
            scheduled_date=now + timedelta(days=5, hours=1),
            duration_minutes=45,
            teacher=teacher,
            status='scheduled'
        )
        lessons_created.append(lesson4)

        # حصة بعد أسبوعين (لن تظهر في القائمة الجانبية)
        lesson5 = ScheduledLesson.objects.create(
            subscription=active_subscription,
            lesson_number=5,
            scheduled_date=now + timedelta(days=14),
            duration_minutes=45,
            teacher=teacher,
            status='scheduled'
        )
        lessons_created.append(lesson5)

        return JsonResponse({
            'success': True,
            'message': f'تم إنشاء {len(lessons_created)} حصة مجدولة بنجاح',
            'lessons': [
                {
                    'id': lesson.id,
                    'lesson_number': lesson.lesson_number,
                    'scheduled_date': lesson.scheduled_date.strftime('%Y-%m-%d %H:%M'),
                    'teacher': lesson.teacher.get_full_name(),
                    'student': lesson.subscription.student.get_full_name()
                }
                for lesson in lessons_created
            ]
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        })

