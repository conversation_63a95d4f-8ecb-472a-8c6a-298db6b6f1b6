# Generated by Django 4.2.7 on 2025-05-31 23:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('subscriptions', '0009_add_payment_pending_status'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('lessons', '0007_lessonqualityreport_lessonqualitysettings_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='UniversalLessonMonitoring',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_role', models.CharField(choices=[('teacher', 'معلم'), ('student', 'طالب')], max_length=10, verbose_name='دور المستخدم')),
                ('intended_join_time', models.DateTimeField(blank=True, help_text='وقت الضغط على زر دخول الحصة', null=True, verbose_name='وقت نية الدخول')),
                ('confirmed_join_time', models.DateTimeField(blank=True, help_text='وقت تأكيد الحضور الفعلي في الحصة', null=True, verbose_name='وقت تأكيد الحضور')),
                ('last_heartbeat', models.DateTimeField(blank=True, help_text='آخر وقت تم استلام heartbeat فيه', null=True, verbose_name='آخر إشارة حياة')),
                ('left_at', models.DateTimeField(blank=True, help_text='وقت تسجيل الخروج من الحصة', null=True, verbose_name='وقت المغادرة')),
                ('total_heartbeats_sent', models.PositiveIntegerField(default=0, help_text='العدد المتوقع للنبضات حسب مدة الحصة', verbose_name='إجمالي النبضات المرسلة')),
                ('total_heartbeats_received', models.PositiveIntegerField(default=0, help_text='العدد الفعلي للنبضات المستلمة', verbose_name='إجمالي النبضات المستلمة')),
                ('missed_heartbeats', models.PositiveIntegerField(default=0, help_text='عدد النبضات التي لم تصل', verbose_name='النبضات المفقودة')),
                ('consecutive_missed', models.PositiveIntegerField(default=0, help_text='عدد النبضات المفقودة بشكل متتالي', verbose_name='النبضات المفقودة المتتالية')),
                ('quality_score', models.FloatField(default=100.0, help_text='نقاط الجودة من 0 إلى 100', verbose_name='نقاط الجودة')),
                ('attendance_reliability', models.FloatField(default=100.0, help_text='مؤشر موثوقية الحضور من 0 إلى 100', verbose_name='موثوقية الحضور')),
                ('status', models.CharField(choices=[('intended', 'نية الدخول'), ('confirmed', 'حضور مؤكد'), ('active', 'نشط'), ('warning', 'تحذير'), ('critical', 'حالة حرجة'), ('disconnected', 'منقطع'), ('suspicious', 'مشبوه'), ('completed', 'مكتمل'), ('fraud_detected', 'تم اكتشاف تلاعب')], default='intended', max_length=20, verbose_name='حالة المراقبة')),
                ('warnings_issued', models.PositiveIntegerField(default=0, verbose_name='عدد التحذيرات الصادرة')),
                ('warning_history', models.JSONField(default=list, help_text='سجل تفصيلي بجميع التحذيرات الصادرة', verbose_name='سجل التحذيرات')),
                ('suspicious_activities', models.JSONField(default=list, help_text='سجل بالأنشطة المشبوهة المكتشفة', verbose_name='الأنشطة المشبوهة')),
                ('browser_info', models.JSONField(default=dict, help_text='معلومات تقنية عن متصفح المستخدم', verbose_name='معلومات المتصفح')),
                ('page_visibility_changes', models.PositiveIntegerField(default=0, help_text='عدد مرات إخفاء/إظهار صفحة المراقبة', verbose_name='تغييرات رؤية الصفحة')),
                ('external_session_url', models.URLField(blank=True, help_text='رابط Jitsi أو المنصة الخارجية', null=True, verbose_name='رابط الجلسة الخارجية')),
                ('external_session_token', models.CharField(blank=True, help_text='JWT token أو رمز التحقق للجلسة الخارجية', max_length=500, null=True, verbose_name='رمز الجلسة الخارجية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('live_lesson', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='monitoring_records', to='lessons.livelesson', verbose_name='الحصة المباشرة')),
                ('scheduled_lesson', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='monitoring_records', to='subscriptions.scheduledlesson', verbose_name='الحصة المجدولة')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lesson_monitoring_records', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل المراقبة الموحد',
                'verbose_name_plural': 'سجلات المراقبة الموحدة',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddConstraint(
            model_name='universallessonmonitoring',
            constraint=models.CheckConstraint(check=models.Q(('live_lesson__isnull', False), ('scheduled_lesson__isnull', False), _connector='OR'), name='monitoring_either_live_or_scheduled_lesson'),
        ),
        migrations.AddConstraint(
            model_name='universallessonmonitoring',
            constraint=models.CheckConstraint(check=models.Q(('live_lesson__isnull', False), ('scheduled_lesson__isnull', False), _negated=True), name='monitoring_not_both_lesson_types'),
        ),
        migrations.AlterUniqueTogether(
            name='universallessonmonitoring',
            unique_together={('scheduled_lesson', 'user'), ('live_lesson', 'user')},
        ),
    ]
