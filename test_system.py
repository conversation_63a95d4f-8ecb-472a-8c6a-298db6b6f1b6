#!/usr/bin/env python
"""
اختبار سريع للنظام الجديد
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qurania_lms.settings')
django.setup()

from django.contrib.auth import get_user_model
from lessons.models import LiveLesson, UniversalLessonMonitoring, UnifiedLessonRating
from lessons.universal_manager import UniversalLessonManager

User = get_user_model()

def test_system():
    """اختبار النظام الجديد"""
    
    print("🧪 بدء اختبار النظام الجديد...")
    
    # 1. اختبار النماذج
    print("\n1️⃣ اختبار النماذج...")
    
    try:
        # فحص نموذج المراقبة
        monitoring_count = UniversalLessonMonitoring.objects.count()
        print(f"   ✅ نموذج UniversalLessonMonitoring: {monitoring_count} سجل")
        
        # فحص نموذج التقييم
        rating_count = UnifiedLessonRating.objects.count()
        print(f"   ✅ نموذج UnifiedLessonRating: {rating_count} سجل")
        
        # فحص الحصص المباشرة
        live_lessons_count = LiveLesson.objects.count()
        print(f"   ✅ نموذج LiveLesson: {live_lessons_count} سجل")
        
    except Exception as e:
        print(f"   ❌ خطأ في النماذج: {e}")
        return False
    
    # 2. اختبار UniversalLessonManager
    print("\n2️⃣ اختبار UniversalLessonManager...")
    
    try:
        # فحص إعدادات Heartbeat
        settings = UniversalLessonManager.HEARTBEAT_SETTINGS
        print(f"   ✅ إعدادات Heartbeat: {settings['interval_seconds']} ثانية")
        
        # فحص مستويات التحذير
        warnings = UniversalLessonManager.WARNING_LEVELS
        print(f"   ✅ مستويات التحذير: {len(warnings)} مستوى")
        
    except Exception as e:
        print(f"   ❌ خطأ في UniversalLessonManager: {e}")
        return False
    
    # 3. اختبار المستخدمين
    print("\n3️⃣ اختبار المستخدمين...")
    
    try:
        # فحص المستخدمين
        total_users = User.objects.count()
        students = User.objects.filter(user_type='student').count()
        teachers = User.objects.filter(user_type='teacher').count()
        admins = User.objects.filter(user_type='admin').count()
        
        print(f"   ✅ إجمالي المستخدمين: {total_users}")
        print(f"   ✅ الطلاب: {students}")
        print(f"   ✅ المعلمين: {teachers}")
        print(f"   ✅ المديرين: {admins}")
        
    except Exception as e:
        print(f"   ❌ خطأ في المستخدمين: {e}")
        return False
    
    # 4. اختبار الحصص النشطة
    print("\n4️⃣ اختبار الحصص النشطة...")
    
    try:
        # فحص الحصص المباشرة النشطة
        live_lessons = LiveLesson.objects.filter(status='live')
        print(f"   ✅ الحصص المباشرة النشطة: {live_lessons.count()}")
        
        # فحص المراقبة النشطة
        active_monitoring = UniversalLessonMonitoring.objects.filter(
            status__in=['confirmed', 'active', 'warning', 'critical']
        )
        print(f"   ✅ المراقبة النشطة: {active_monitoring.count()}")
        
    except Exception as e:
        print(f"   ❌ خطأ في الحصص النشطة: {e}")
        return False
    
    # 5. اختبار إحصائيات المراقبة
    print("\n5️⃣ اختبار إحصائيات المراقبة...")
    
    try:
        # إحصائيات التحذيرات
        warnings_count = UniversalLessonMonitoring.objects.filter(
            status__in=['warning', 'critical']
        ).count()
        
        # إحصائيات التلاعب
        fraud_suspected = UniversalLessonMonitoring.objects.filter(
            status='suspicious'
        ).count()
        
        fraud_detected = UniversalLessonMonitoring.objects.filter(
            status='fraud_detected'
        ).count()
        
        print(f"   ✅ التحذيرات: {warnings_count}")
        print(f"   ✅ التلاعب المشبوه: {fraud_suspected}")
        print(f"   ✅ التلاعب المكتشف: {fraud_detected}")
        
    except Exception as e:
        print(f"   ❌ خطأ في إحصائيات المراقبة: {e}")
        return False
    
    # 6. اختبار التقييمات
    print("\n6️⃣ اختبار التقييمات...")
    
    try:
        # إحصائيات التقييمات
        total_ratings = UnifiedLessonRating.objects.count()
        live_ratings = UnifiedLessonRating.objects.filter(live_lesson__isnull=False).count()
        scheduled_ratings = UnifiedLessonRating.objects.filter(scheduled_lesson__isnull=False).count()
        
        print(f"   ✅ إجمالي التقييمات: {total_ratings}")
        print(f"   ✅ تقييمات الحصص المباشرة: {live_ratings}")
        print(f"   ✅ تقييمات الحصص المجدولة: {scheduled_ratings}")
        
        # متوسط التقييمات
        if total_ratings > 0:
            from django.db.models import Avg
            avg_rating = UnifiedLessonRating.objects.aggregate(
                avg=Avg('overall_rating')
            )['avg']
            avg_display = f"{avg_rating:.2f}" if avg_rating else "0"
            print(f"   ✅ متوسط التقييم العام: {avg_display}")
        
    except Exception as e:
        print(f"   ❌ خطأ في التقييمات: {e}")
        return False
    
    # 7. اختبار الأنشطة المشبوهة
    print("\n7️⃣ اختبار الأنشطة المشبوهة...")
    
    try:
        # البحث عن أنشطة مشبوهة
        suspicious_activities = UniversalLessonMonitoring.objects.exclude(
            suspicious_activities=[]
        )
        
        print(f"   ✅ سجلات بأنشطة مشبوهة: {suspicious_activities.count()}")
        
        # فحص التحذيرات
        warnings_issued = UniversalLessonMonitoring.objects.filter(
            warnings_issued__gt=0
        )
        
        print(f"   ✅ سجلات بتحذيرات: {warnings_issued.count()}")
        
    except Exception as e:
        print(f"   ❌ خطأ في الأنشطة المشبوهة: {e}")
        return False
    
    print("\n🎉 تم اختبار النظام بنجاح! جميع المكونات تعمل بشكل صحيح.")
    return True

def create_test_data():
    """إنشاء بيانات اختبار"""
    
    print("\n🔧 إنشاء بيانات اختبار...")
    
    try:
        # إنشاء مستخدمين للاختبار إذا لم يكونوا موجودين
        admin_user, created = User.objects.get_or_create(
            username='test_admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'مدير',
                'last_name': 'اختبار',
                'user_type': 'admin',
                'is_active': True
            }
        )
        if created:
            admin_user.set_password('test123')
            admin_user.save()
            print("   ✅ تم إنشاء مستخدم مدير للاختبار")
        
        teacher_user, created = User.objects.get_or_create(
            username='test_teacher',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'معلم',
                'last_name': 'اختبار',
                'user_type': 'teacher',
                'is_active': True
            }
        )
        if created:
            teacher_user.set_password('test123')
            teacher_user.save()
            print("   ✅ تم إنشاء مستخدم معلم للاختبار")
        
        student_user, created = User.objects.get_or_create(
            username='test_student',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'طالب',
                'last_name': 'اختبار',
                'user_type': 'student',
                'is_active': True
            }
        )
        if created:
            student_user.set_password('test123')
            student_user.save()
            print("   ✅ تم إنشاء مستخدم طالب للاختبار")
        
        print("\n📋 بيانات تسجيل الدخول للاختبار:")
        print("   👨‍💼 مدير: test_admin / test123")
        print("   👨‍🏫 معلم: test_teacher / test123")
        print("   👨‍🎓 طالب: test_student / test123")
        
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء بيانات الاختبار: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 اختبار نظام المراقبة الموحد المتقدم")
    print("=" * 50)
    
    # إنشاء بيانات اختبار
    create_test_data()
    
    # اختبار النظام
    success = test_system()
    
    if success:
        print("\n✅ النظام جاهز للاستخدام!")
        print("\n🌐 للاختبار:")
        print("   1. افتح: http://localhost:8080")
        print("   2. سجل دخول بأحد الحسابات أعلاه")
        print("   3. اختبر الميزات الجديدة")
    else:
        print("\n❌ يوجد مشاكل في النظام - يرجى مراجعة الأخطاء أعلاه")
    
    print("\n" + "=" * 50)
