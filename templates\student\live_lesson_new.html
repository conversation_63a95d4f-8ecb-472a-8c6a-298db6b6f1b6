{% extends 'base.html' %}
{% load static %}

{% block title %}{{ live_lesson.title }} - حصة مباشرة{% endblock %}

{% block extra_css %}
<style>
    .lesson-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .status-indicator {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }
    
    .monitoring-panel {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border: 2px solid #0ea5e9;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    
    .quality-card {
        background: white;
        border-radius: 8px;
        padding: 16px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    
    .quality-card:hover {
        transform: translateY(-2px);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s;
        cursor: pointer;
    }
    
    .btn-primary:hover:not(:disabled) {
        background: linear-gradient(135deg, #047857 0%, #059669 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    .btn-primary:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }
    
    .btn-secondary {
        background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s;
        cursor: pointer;
    }
    
    .btn-danger {
        background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s;
        cursor: pointer;
    }
    
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        animation: slideIn 0.3s ease-out;
    }
    
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .notification.success { background: #10b981; }
    .notification.warning { background: #f59e0b; }
    .notification.error { background: #ef4444; }
    .notification.info { background: #3b82f6; }
</style>
{% endblock %}

{% block content %}
<div class="lesson-container">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
        <div class="flex items-center justify-between mb-4">
            <h1 class="text-2xl font-bold text-gray-800">
                <i class="fas fa-video text-green-600 ml-2"></i>
                {{ live_lesson.title }}
            </h1>
            <span class="px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                {{ live_lesson.get_status_display }}
            </span>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center">
                <i class="fas fa-user-graduate text-blue-500 text-2xl mb-2"></i>
                <p class="text-sm text-gray-600">المعلم</p>
                <p class="font-semibold">{{ live_lesson.teacher.get_full_name }}</p>
            </div>
            <div class="text-center">
                <i class="fas fa-clock text-orange-500 text-2xl mb-2"></i>
                <p class="text-sm text-gray-600">مدة الحصة</p>
                <p class="font-semibold">{{ live_lesson.duration_minutes }} دقيقة</p>
            </div>
            <div class="text-center">
                <i class="fas fa-calendar text-purple-500 text-2xl mb-2"></i>
                <p class="text-sm text-gray-600">موعد الحصة</p>
                <p class="font-semibold">{{ live_lesson.scheduled_date|date:"H:i" }}</p>
            </div>
        </div>
    </div>

    {% if live_lesson.status == 'scheduled' %}
    <!-- حالة الانتظار -->
    <div class="bg-white rounded-lg shadow-lg p-8 text-center">
        <i class="fas fa-clock text-6xl text-yellow-400 mb-4"></i>
        <h3 class="text-xl font-bold text-gray-800 mb-2">في انتظار بدء الحصة</h3>
        <p class="text-gray-600 mb-4">المعلم {{ live_lesson.teacher.get_full_name }} لم يبدأ الحصة بعد</p>
        <p class="text-sm text-gray-500">
            <i class="fas fa-info-circle ml-1"></i>
            سيتم تحديث الصفحة تلقائياً عند بدء الحصة
        </p>
    </div>
    
    <script>
    // تحديث الصفحة كل 30 ثانية للتحقق من بدء الحصة
    setInterval(function() {
        location.reload();
    }, 30000);
    </script>
    
    {% else %}
    <!-- نظام الحضور والمراقبة الجديد -->
    <div class="monitoring-panel p-6 mb-6">
        <h4 class="text-lg font-bold text-gray-800 mb-4">
            <i class="fas fa-shield-alt text-green-600 ml-2"></i>
            نظام الحضور والمراقبة المتقدم
        </h4>
        
        <!-- حالة الحضور الحالية -->
        <div id="attendance-status" class="mb-4">
            <div class="flex items-center justify-between bg-white rounded-lg p-4 shadow-sm">
                <div class="flex items-center">
                    <div id="status-indicator" class="w-3 h-3 rounded-full bg-gray-400 ml-3 status-indicator"></div>
                    <span id="status-text" class="font-medium text-gray-700">جاري التهيئة...</span>
                </div>
                <div id="attendance-timer" class="text-sm text-gray-600">
                    <i class="fas fa-stopwatch ml-1"></i>
                    <span id="timer-display">00:00:00</span>
                </div>
            </div>
        </div>

        <!-- أزرار التحكم -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
            <button id="join-external-lesson" class="btn-primary">
                <i class="fas fa-external-link-alt ml-2"></i>
                دخول الحصة (نافذة جديدة)
            </button>
            
            <button id="mark-attendance" class="btn-secondary" disabled>
                <i class="fas fa-check-circle ml-2"></i>
                تأكيد الحضور
            </button>
            
            <button id="mark-leave" class="btn-danger" disabled>
                <i class="fas fa-sign-out-alt ml-2"></i>
                تسجيل الخروج
            </button>
        </div>

        <!-- مؤشرات الجودة -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="quality-card">
                <i class="fas fa-heartbeat text-red-500 text-xl mb-2"></i>
                <p class="text-sm text-gray-600">إشارات الحياة</p>
                <p id="heartbeat-count" class="font-bold text-lg">0</p>
            </div>
            <div class="quality-card">
                <i class="fas fa-signal text-blue-500 text-xl mb-2"></i>
                <p class="text-sm text-gray-600">جودة الاتصال</p>
                <p id="connection-quality" class="font-bold text-lg">ممتاز</p>
            </div>
            <div class="quality-card">
                <i class="fas fa-star text-yellow-500 text-xl mb-2"></i>
                <p class="text-sm text-gray-600">نقاط الجودة</p>
                <p id="quality-score" class="font-bold text-lg">100</p>
            </div>
        </div>
    </div>

    <!-- تعليمات للطالب -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h5 class="font-bold text-yellow-800 mb-2">
            <i class="fas fa-info-circle ml-2"></i>
            تعليمات الحضور
        </h5>
        <ol class="text-sm text-yellow-700 space-y-1">
            <li>1. اضغط على "دخول الحصة" لفتح Jitsi في نافذة جديدة</li>
            <li>2. بعد دخولك للحصة، اضغط "تأكيد الحضور" هنا</li>
            <li>3. ابق هذه الصفحة مفتوحة طوال الحصة للمراقبة</li>
            <li>4. اضغط "تسجيل الخروج" عند انتهاء الحصة</li>
            <li>5. <strong>تحذير:</strong> إغلاق هذه الصفحة سيؤثر على تقييم جودة حضورك</li>
        </ol>
    </div>
    {% endif %}
</div>

<!-- نافذة التقييم -->
<div id="rating-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <h3 class="text-lg font-medium text-gray-900 mb-4">تقييم الحصة</h3>
            <form id="rating-form">
                {% csrf_token %}
                <!-- نموذج التقييم سيتم إضافته هنا -->
                <div class="mt-4">
                    <button type="submit" class="btn-primary mr-2">إرسال التقييم</button>
                    <button type="button" onclick="skipRating()" class="btn-secondary">تخطي</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/attendance_monitor.js' %}"></script>
{% endblock %}
