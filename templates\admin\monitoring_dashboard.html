{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة مراقبة الحصص المتقدمة{% endblock %}

{% block extra_css %}
<style>
    .monitoring-container {
        max-width: 1600px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        padding: 20px;
        color: white;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    
    .stats-card:hover {
        transform: translateY(-2px);
    }
    
    .stats-card.warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    .stats-card.danger {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    }
    
    .stats-card.success {
        background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    }
    
    .lesson-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #3b82f6;
        margin-bottom: 16px;
        transition: all 0.3s;
    }
    
    .lesson-card:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        transform: translateY(-1px);
    }
    
    .lesson-card.live {
        border-left-color: #10b981;
        background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
    }
    
    .lesson-card.warning {
        border-left-color: #f59e0b;
        background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    }
    
    .lesson-card.danger {
        border-left-color: #ef4444;
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    }
    
    .monitoring-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-left: 8px;
        animation: pulse 2s infinite;
    }
    
    .monitoring-indicator.active { background: #10b981; }
    .monitoring-indicator.warning { background: #f59e0b; }
    .monitoring-indicator.critical { background: #ef4444; }
    .monitoring-indicator.suspicious { background: #8b5cf6; }
    .monitoring-indicator.offline { background: #6b7280; }
    
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }
    
    .user-monitoring {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 8px;
        margin: 8px 0;
        border: 1px solid #e5e7eb;
    }
    
    .quality-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
    }
    
    .quality-excellent { background: #dcfce7; color: #166534; }
    .quality-good { background: #fef3c7; color: #92400e; }
    .quality-poor { background: #fee2e2; color: #991b1b; }
    
    .btn-monitor {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .btn-monitor:hover {
        background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
        transform: translateY(-1px);
    }
    
    .btn-action {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 11px;
        cursor: pointer;
        margin: 2px;
    }
    
    .btn-warning {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }
    
    .btn-danger {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }
    
    .refresh-indicator {
        display: inline-block;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="monitoring-container">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <h1 class="text-3xl font-bold text-gray-800">
            <i class="fas fa-shield-alt text-blue-600 ml-2"></i>
            لوحة مراقبة الحصص المتقدمة
        </h1>
        <div class="flex items-center space-x-4">
            <button id="refresh-dashboard" class="btn-monitor">
                <i id="refresh-icon" class="fas fa-sync-alt ml-1"></i>
                تحديث
            </button>
            <span class="text-sm text-gray-600">
                آخر تحديث: <span id="last-update">الآن</span>
            </span>
        </div>
    </div>

    <!-- إحصائيات المراقبة -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="stats-card success">
            <i class="fas fa-eye text-3xl mb-2"></i>
            <h3 class="text-lg font-semibold">المراقبة النشطة</h3>
            <p class="text-2xl font-bold">{{ monitoring_stats.total_active_monitoring }}</p>
        </div>
        
        <div class="stats-card warning">
            <i class="fas fa-exclamation-triangle text-3xl mb-2"></i>
            <h3 class="text-lg font-semibold">تحذيرات</h3>
            <p class="text-2xl font-bold">{{ monitoring_stats.warning_count }}</p>
        </div>
        
        <div class="stats-card danger">
            <i class="fas fa-search text-3xl mb-2"></i>
            <h3 class="text-lg font-semibold">مشبوه</h3>
            <p class="text-2xl font-bold">{{ monitoring_stats.fraud_suspected }}</p>
        </div>
        
        <div class="stats-card danger">
            <i class="fas fa-ban text-3xl mb-2"></i>
            <h3 class="text-lg font-semibold">تلاعب مكتشف</h3>
            <p class="text-2xl font-bold">{{ monitoring_stats.fraud_detected }}</p>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- الحصص النشطة -->
        <div>
            <h2 class="text-xl font-bold text-gray-800 mb-4">
                <i class="fas fa-video text-green-600 ml-2"></i>
                الحصص النشطة حالياً ({{ active_lessons|length }})
            </h2>
            
            {% if active_lessons %}
                {% for lesson in active_lessons %}
                <div class="lesson-card live">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-semibold text-gray-800">{{ lesson.title }}</h3>
                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                            مباشر الآن
                        </span>
                    </div>
                    
                    <div class="text-sm text-gray-600 mb-3">
                        <p><i class="fas fa-user-tie ml-1"></i> المعلم: {{ lesson.teacher.get_full_name }}</p>
                        <p><i class="fas fa-user-graduate ml-1"></i> الطالب: {{ lesson.student.get_full_name }}</p>
                        <p><i class="fas fa-clock ml-1"></i> بدأت: {{ lesson.started_at|date:"H:i" }}</p>
                    </div>
                    
                    <!-- مراقبة المشاركين -->
                    <div class="space-y-2">
                        {% for monitoring in lesson.monitoring_records %}
                        <div class="user-monitoring">
                            <div class="flex items-center">
                                <span class="monitoring-indicator {{ monitoring.status }}"></span>
                                <span class="font-medium">{{ monitoring.user.get_full_name }}</span>
                                <span class="text-xs text-gray-500 mr-2">({{ monitoring.get_user_role_display }})</span>
                            </div>
                            
                            <div class="flex items-center space-x-2">
                                <span class="quality-badge {% if monitoring.quality_score >= 80 %}quality-excellent{% elif monitoring.quality_score >= 60 %}quality-good{% else %}quality-poor{% endif %}">
                                    {{ monitoring.quality_score|floatformat:0 }}%
                                </span>
                                
                                {% if monitoring.warnings_issued > 0 %}
                                <span class="text-xs text-orange-600">
                                    <i class="fas fa-exclamation-triangle"></i> {{ monitoring.warnings_issued }}
                                </span>
                                {% endif %}
                                
                                {% if monitoring.get_fraud_risk_level != 'none' %}
                                <span class="text-xs text-red-600">
                                    <i class="fas fa-shield-alt"></i> {{ monitoring.get_fraud_risk_level }}
                                </span>
                                {% endif %}
                            </div>
                        </div>
                        {% empty %}
                        <p class="text-gray-500 text-sm">لا توجد بيانات مراقبة</p>
                        {% endfor %}
                    </div>
                    
                    <!-- أزرار الإجراءات -->
                    <div class="flex justify-end mt-3 space-x-2">
                        <button onclick="joinLessonAsAdmin({{ lesson.id }})" class="btn-action">
                            <i class="fas fa-eye"></i> مراقبة
                        </button>
                        <button onclick="viewLessonDetails({{ lesson.id }})" class="btn-action">
                            <i class="fas fa-info-circle"></i> تفاصيل
                        </button>
                        {% if lesson.monitoring_records %}
                        <button onclick="exportMonitoringData({{ lesson.id }})" class="btn-action btn-warning">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="lesson-card">
                    <div class="text-center py-8">
                        <i class="fas fa-video-slash text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-600">لا توجد حصص نشطة حالياً</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- الحصص القادمة والأنشطة المشبوهة -->
        <div>
            <!-- الحصص القادمة -->
            <h2 class="text-xl font-bold text-gray-800 mb-4">
                <i class="fas fa-clock text-blue-600 ml-2"></i>
                الحصص القادمة (ساعتين) ({{ upcoming_lessons|length }})
            </h2>
            
            {% if upcoming_lessons %}
                {% for lesson in upcoming_lessons %}
                <div class="lesson-card">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="font-medium">حصة رقم {{ lesson.lesson_number }}</h4>
                        <span class="text-xs text-gray-500">{{ lesson.scheduled_date|date:"H:i" }}</span>
                    </div>
                    <div class="text-sm text-gray-600">
                        <p>المعلم: {{ lesson.teacher.get_full_name }}</p>
                        <p>الطالب: {{ lesson.student.get_full_name }}</p>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="lesson-card">
                    <p class="text-gray-600 text-center py-4">لا توجد حصص قادمة</p>
                </div>
            {% endif %}

            <!-- الأنشطة المشبوهة -->
            <h2 class="text-xl font-bold text-gray-800 mb-4 mt-8">
                <i class="fas fa-exclamation-triangle text-red-600 ml-2"></i>
                أنشطة مشبوهة حديثة
            </h2>
            
            {% if suspicious_activities %}
                {% for activity in suspicious_activities %}
                <div class="lesson-card danger">
                    <div class="flex items-center justify-between mb-2">
                        <span class="font-medium text-red-800">{{ activity.user.get_full_name }}</span>
                        <span class="text-xs text-red-600">{{ activity.updated_at|date:"H:i" }}</span>
                    </div>
                    <div class="text-sm text-red-700">
                        <p>نقاط الجودة: {{ activity.quality_score|floatformat:0 }}%</p>
                        <p>تحذيرات: {{ activity.warnings_issued }}</p>
                        <p>أنشطة مشبوهة: {{ activity.suspicious_activities|length }}</p>
                    </div>
                    <div class="mt-2">
                        <button onclick="viewSuspiciousDetails({{ activity.id }})" class="btn-action btn-danger">
                            <i class="fas fa-search"></i> تفاصيل
                        </button>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="lesson-card">
                    <p class="text-gray-600 text-center py-4">لا توجد أنشطة مشبوهة</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_js %}
<script>
// تحديث تلقائي كل 30 ثانية
setInterval(function() {
    refreshDashboard();
}, 30000);

// تحديث يدوي
document.getElementById('refresh-dashboard').addEventListener('click', function() {
    refreshDashboard();
});

function refreshDashboard() {
    const refreshIcon = document.getElementById('refresh-icon');
    refreshIcon.classList.add('refresh-indicator');
    
    // تحديث الصفحة
    location.reload();
}

function joinLessonAsAdmin(lessonId) {
    // فتح الحصة كمراقب
    const url = `/live-lesson-room/${lessonId}/`;
    window.open(url, 'admin_monitoring', 'width=1200,height=800,scrollbars=yes,resizable=yes');
}

function viewLessonDetails(lessonId) {
    // عرض تفاصيل الحصة
    window.location.href = `/admin/lessons/${lessonId}/`;
}

function exportMonitoringData(lessonId) {
    // تصدير بيانات المراقبة
    window.location.href = `/admin/monitoring/export/${lessonId}/`;
}

function viewSuspiciousDetails(monitoringId) {
    // عرض تفاصيل النشاط المشبوه
    window.location.href = `/admin/monitoring/suspicious/${monitoringId}/`;
}

// تحديث وقت آخر تحديث
document.getElementById('last-update').textContent = new Date().toLocaleTimeString('ar-SA');
</script>
{% endblock %}
