from django.urls import path
from . import views
from .api_views import UniversalMonitoringAPI, get_lesson_monitoring_data, admin_monitoring_dashboard, admin_monitoring_action

app_name = 'lessons'

urlpatterns = [
    # API للنظام الموحد للمراقبة
    path('api/monitoring/', UniversalMonitoringAPI.as_view(), name='universal_monitoring_api'),
    path('api/monitoring/<str:lesson_type>/<int:lesson_id>/', get_lesson_monitoring_data, name='get_monitoring_data'),
    path('api/admin/monitoring/dashboard/', admin_monitoring_dashboard, name='admin_monitoring_dashboard'),
    path('api/admin/monitoring/action/', admin_monitoring_action, name='admin_monitoring_action'),

    # Lesson Management
    path('', views.lesson_list, name='list'),
    path('create/', views.lesson_create, name='create'),
    path('<int:lesson_id>/', views.lesson_detail, name='detail'),
    path('<int:lesson_id>/edit/', views.lesson_edit, name='edit'),
    path('<int:lesson_id>/delete/', views.lesson_delete, name='delete'),
    
    # Lesson Actions
    path('<int:lesson_id>/join/', views.join_lesson, name='join'),
    path('<int:lesson_id>/start/', views.start_lesson, name='start'),
    path('<int:lesson_id>/end/', views.end_lesson, name='end'),
    path('<int:lesson_id>/cancel/', views.cancel_lesson, name='cancel'),
    
    # Lesson Content
    path('<int:lesson_id>/content/', views.lesson_content, name='content'),
    path('<int:lesson_id>/content/add/', views.add_lesson_content, name='add_content'),
    
    # Lesson Rating
    path('<int:lesson_id>/rate/', views.rate_lesson, name='rate'),
    
    # Calendar
    path('calendar/', views.lesson_calendar, name='calendar'),
    path('calendar/data/', views.calendar_data, name='calendar_data'),
    
    # Teacher specific
    path('teacher/schedule/', views.teacher_schedule, name='teacher_schedule'),
    path('teacher/students/', views.teacher_students, name='teacher_students'),
    path('teacher/earnings/', views.teacher_earnings, name='teacher_earnings'),
    path('teacher/ratings/', views.teacher_ratings, name='teacher_ratings'),
    
    # Student specific
    path('student/lessons/', views.student_lessons, name='student_lessons'),
    path('student/progress/', views.student_progress, name='student_progress'),
    path('student/archive/', views.student_archive, name='student_archive'),
]
