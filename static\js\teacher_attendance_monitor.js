/**
 * نظام مراقبة الحضور للمعلم مع مراقبة الطلاب
 */

class TeacherAttendanceMonitor {
    constructor(lessonId, lessonType) {
        this.lessonId = lessonId;
        this.lessonType = lessonType;
        this.monitoringId = null;
        this.heartbeatInterval = null;
        this.timerInterval = null;
        this.studentMonitoringInterval = null;
        this.isActive = false;
        this.heartbeatCount = 0;
        this.externalWindow = null;
        this.startTime = null;
        this.heartbeatIntervalSeconds = 20;
        
        this.init();
    }
    
    async init() {
        try {
            console.log('🚀 بدء تهيئة نظام مراقبة المعلم...');
            
            // بدء المراقبة
            const response = await fetch('/lessons/api/monitoring/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    action: 'start_monitoring',
                    lesson_type: this.lessonType,
                    lesson_id: this.lessonId,
                    user_role: 'teacher'
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.monitoringId = data.monitoring_id;
                this.externalUrl = data.external_url;
                this.heartbeatIntervalSeconds = data.heartbeat_interval || 20;
                
                this.setupUI();
                this.setupEventListeners();
                this.startStudentMonitoring();
                this.updateStatus('intended', 'تم تهيئة النظام - جاهز للبدء');
                
                console.log('✅ تم بدء مراقبة المعلم بنجاح');
                this.showNotification('تم تهيئة نظام المراقبة بنجاح', 'success');
            } else {
                console.error('❌ فشل في بدء المراقبة:', data.error);
                this.showError('فشل في بدء نظام المراقبة: ' + data.error);
            }
        } catch (error) {
            console.error('❌ خطأ في تهيئة المراقبة:', error);
            this.showError('خطأ في الاتصال بالخادم');
        }
    }
    
    setupUI() {
        // تحديث رابط دخول الحصة
        const joinButton = document.getElementById('teacher-join-lesson');
        if (joinButton && this.externalUrl) {
            joinButton.onclick = () => this.joinExternalLesson();
        }
        
        // تحديث أزرار التحكم
        const confirmButton = document.getElementById('teacher-confirm-attendance');
        
        if (confirmButton) {
            confirmButton.onclick = () => this.confirmAttendance();
        }
        
        // أزرار إدارة الحصة
        const startButton = document.getElementById('start-lesson-btn');
        const endButton = document.getElementById('end-lesson-btn');
        
        if (startButton) {
            startButton.onclick = () => this.startLesson();
        }
        
        if (endButton) {
            endButton.onclick = () => this.endLesson();
        }
    }
    
    setupEventListeners() {
        // مراقبة إغلاق الصفحة
        window.addEventListener('beforeunload', (e) => {
            if (this.isActive) {
                e.preventDefault();
                e.returnValue = 'هل أنت متأكد من مغادرة الحصة؟';
                this.markLeave(false);
            }
        });
        
        // مراقبة تغيير visibility
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });
    }
    
    async startLesson() {
        try {
            const response = await fetch(`/api/live-lessons/${this.lessonId}/start/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showNotification('تم بدء الحصة بنجاح', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                this.showError('فشل في بدء الحصة: ' + data.message);
            }
        } catch (error) {
            console.error('❌ خطأ في بدء الحصة:', error);
            this.showError('خطأ في بدء الحصة');
        }
    }
    
    async endLesson() {
        if (!confirm('هل أنت متأكد من إنهاء الحصة؟')) {
            return;
        }
        
        try {
            const response = await fetch(`/api/live-lessons/${this.lessonId}/end/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showNotification('تم إنهاء الحصة بنجاح', 'success');
                this.stopMonitoring();
                
                setTimeout(() => {
                    window.location.href = '/dashboard/teacher/';
                }, 2000);
            } else {
                this.showError('فشل في إنهاء الحصة: ' + data.message);
            }
        } catch (error) {
            console.error('❌ خطأ في إنهاء الحصة:', error);
            this.showError('خطأ في إنهاء الحصة');
        }
    }
    
    async joinExternalLesson() {
        if (!this.externalUrl) {
            this.showError('رابط الحصة غير متوفر');
            return;
        }
        
        try {
            // فتح الحصة في نافذة جديدة
            this.externalWindow = window.open(
                this.externalUrl, 
                'jitsi_lesson', 
                'width=1200,height=800,scrollbars=yes,resizable=yes,toolbar=no,menubar=no'
            );
            
            if (this.externalWindow) {
                // تحديث حالة الواجهة
                this.updateStatus('intended', 'تم فتح الحصة - يرجى تأكيد الحضور');
                
                // تفعيل زر تأكيد الحضور
                const confirmButton = document.getElementById('teacher-confirm-attendance');
                if (confirmButton) {
                    confirmButton.disabled = false;
                    confirmButton.classList.remove('opacity-50');
                }
                
                this.showNotification('تم فتح الحصة في نافذة جديدة', 'success');
            } else {
                this.showError('فشل في فتح نافذة الحصة - يرجى السماح بالنوافذ المنبثقة');
            }
        } catch (error) {
            console.error('❌ خطأ في فتح الحصة:', error);
            this.showError('خطأ في فتح الحصة');
        }
    }
    
    async confirmAttendance() {
        if (!this.monitoringId) {
            this.showError('معرف المراقبة غير متوفر');
            return;
        }
        
        try {
            const response = await fetch('/lessons/api/monitoring/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    action: 'confirm_attendance',
                    monitoring_id: this.monitoringId
                })
            });
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.isActive = true;
                this.startTime = new Date();
                this.startHeartbeat();
                this.startTimer();
                this.updateStatus('confirmed', 'تم تأكيد الحضور - المراقبة نشطة');
                
                // تعطيل زر تأكيد الحضور
                const confirmButton = document.getElementById('teacher-confirm-attendance');
                if (confirmButton) {
                    confirmButton.disabled = true;
                    confirmButton.classList.add('opacity-50');
                    confirmButton.innerHTML = '<i class="fas fa-check ml-2"></i>تم التأكيد';
                }
                
                this.showNotification('تم تأكيد حضورك بنجاح - بدأت المراقبة', 'success');
            } else {
                this.showError('فشل في تأكيد الحضور: ' + data.message);
            }
        } catch (error) {
            console.error('❌ خطأ في تأكيد الحضور:', error);
            this.showError('خطأ في الاتصال بالخادم');
        }
    }
    
    startHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }
        
        this.heartbeatInterval = setInterval(() => {
            this.sendHeartbeat();
        }, this.heartbeatIntervalSeconds * 1000);
        
        console.log(`🔄 بدء إرسال heartbeat كل ${this.heartbeatIntervalSeconds} ثانية`);
    }
    
    startTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
        }
        
        this.timerInterval = setInterval(() => {
            this.updateTimer();
        }, 1000);
    }
    
    startStudentMonitoring() {
        // مراقبة حالة الطالب كل 10 ثوان
        this.studentMonitoringInterval = setInterval(() => {
            this.checkStudentStatus();
        }, 10000);
    }
    
    updateTimer() {
        if (!this.startTime) return;
        
        const now = new Date();
        const elapsed = Math.floor((now - this.startTime) / 1000);
        
        const hours = Math.floor(elapsed / 3600);
        const minutes = Math.floor((elapsed % 3600) / 60);
        const seconds = elapsed % 60;
        
        const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        // تحديث مؤقت المعلم
        const teacherTimer = document.getElementById('teacher-timer-display');
        if (teacherTimer) {
            teacherTimer.textContent = timeString;
        }
        
        // تحديث مؤقت الحصة العام
        const lessonTimer = document.getElementById('lesson-timer');
        if (lessonTimer) {
            lessonTimer.textContent = timeString;
        }
    }
    
    async sendHeartbeat() {
        if (!this.isActive || !this.monitoringId) return;
        
        try {
            const heartbeatData = {
                action: 'heartbeat',
                monitoring_id: this.monitoringId,
                page_visible: !document.hidden,
                timestamp: new Date().toISOString(),
                browser_info: this.getBrowserInfo(),
                screen_resolution: `${screen.width}x${screen.height}`,
                browser_language: navigator.language,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                connection_quality: navigator.onLine ? 'good' : 'poor',
                activity_level: 'normal'
            };
            
            const response = await fetch('/lessons/api/monitoring/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify(heartbeatData)
            });
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.heartbeatCount++;
                this.updateTeacherMonitoringUI(data);
            } else {
                console.error('❌ فشل heartbeat:', data.message);
            }
        } catch (error) {
            console.error('❌ خطأ في إرسال heartbeat:', error);
        }
    }
    
    async checkStudentStatus() {
        try {
            const response = await fetch(`/lessons/api/monitoring/live/${this.lessonId}/`, {
                method: 'GET',
                headers: {
                    'X-CSRFToken': this.getCSRFToken()
                }
            });
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.updateStudentMonitoringUI(data);
            }
        } catch (error) {
            console.error('❌ خطأ في فحص حالة الطالب:', error);
        }
    }
    
    updateTeacherMonitoringUI(data) {
        // تحديث عداد النبضات
        const heartbeatElement = document.getElementById('teacher-heartbeat-count');
        if (heartbeatElement) {
            heartbeatElement.textContent = data.heartbeat_count || this.heartbeatCount;
        }
        
        // تحديث نقاط الجودة
        const qualityElement = document.getElementById('teacher-quality-score');
        if (qualityElement) {
            const score = Math.round(data.quality_score || 100);
            qualityElement.textContent = score;
        }
    }
    
    updateStudentMonitoringUI(data) {
        // تحديث مؤشرات الطالب
        const studentHeartbeat = document.getElementById('student-heartbeat-count');
        const studentQuality = document.getElementById('student-quality-score');
        const studentConnection = document.getElementById('student-connection-quality');
        const studentStatus = document.getElementById('student-status-text');
        const studentIndicator = document.getElementById('student-status-indicator');
        
        if (data.student_monitoring) {
            const student = data.student_monitoring;
            
            if (studentHeartbeat) {
                studentHeartbeat.textContent = student.heartbeat_count || 0;
            }
            
            if (studentQuality) {
                const score = Math.round(student.quality_score || 100);
                studentQuality.textContent = score;
                
                // تغيير اللون حسب النقاط
                studentQuality.className = 'font-bold';
                if (score >= 80) {
                    studentQuality.classList.add('text-green-600');
                } else if (score >= 60) {
                    studentQuality.classList.add('text-yellow-600');
                } else {
                    studentQuality.classList.add('text-red-600');
                }
            }
            
            if (studentConnection) {
                const reliability = student.attendance_reliability || 100;
                if (reliability >= 90) {
                    studentConnection.textContent = 'ممتاز';
                    studentConnection.className = 'font-bold text-green-600';
                } else if (reliability >= 70) {
                    studentConnection.textContent = 'جيد';
                    studentConnection.className = 'font-bold text-yellow-600';
                } else {
                    studentConnection.textContent = 'ضعيف';
                    studentConnection.className = 'font-bold text-red-600';
                }
            }
            
            if (studentStatus) {
                studentStatus.textContent = student.status_display || 'غير متصل';
            }
            
            if (studentIndicator) {
                studentIndicator.className = 'w-3 h-3 rounded-full ml-3 status-indicator';
                switch(student.status) {
                    case 'confirmed':
                    case 'active':
                        studentIndicator.classList.add('bg-green-400');
                        break;
                    case 'warning':
                        studentIndicator.classList.add('bg-yellow-400');
                        break;
                    case 'critical':
                    case 'suspicious':
                        studentIndicator.classList.add('bg-red-400');
                        break;
                    default:
                        studentIndicator.classList.add('bg-gray-400');
                }
            }
            
            // إظهار تحذيرات إذا لزم الأمر
            if (student.fraud_risk_level === 'high') {
                this.showNotification('تحذير: تم اكتشاف نشاط مشبوه من الطالب', 'warning');
            }
        }
    }
    
    getBrowserInfo() {
        return {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine
        };
    }
    
    handleVisibilityChange() {
        if (document.hidden) {
            console.log('⚠️ تم إخفاء الصفحة');
        } else {
            console.log('✅ تم إظهار الصفحة');
        }
    }
    
    stopMonitoring() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
        
        if (this.studentMonitoringInterval) {
            clearInterval(this.studentMonitoringInterval);
            this.studentMonitoringInterval = null;
        }
        
        this.isActive = false;
    }
    
    updateStatus(status, message) {
        const statusIndicator = document.getElementById('teacher-status-indicator');
        const statusText = document.getElementById('teacher-status-text');
        
        if (statusIndicator && statusText) {
            statusText.textContent = message;
            
            // تحديث لون المؤشر
            statusIndicator.className = 'w-3 h-3 rounded-full ml-3 status-indicator';
            switch(status) {
                case 'intended':
                    statusIndicator.classList.add('bg-yellow-400');
                    break;
                case 'confirmed':
                case 'active':
                    statusIndicator.classList.add('bg-green-400');
                    break;
                case 'warning':
                    statusIndicator.classList.add('bg-orange-400');
                    break;
                case 'critical':
                    statusIndicator.classList.add('bg-red-400');
                    break;
                default:
                    statusIndicator.classList.add('bg-gray-400');
            }
        }
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        const colors = {
            success: 'bg-green-500',
            warning: 'bg-yellow-500',
            error: 'bg-red-500',
            info: 'bg-blue-500'
        };
        
        notification.className = `fixed top-20 right-4 ${colors[type]} text-white px-4 py-2 rounded-lg shadow-lg z-50`;
        notification.innerHTML = `<i class="fas fa-info-circle ml-2"></i>${message}`;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
    
    showError(message) {
        this.showNotification(message, 'error');
        console.error('❌', message);
    }
    
    getCSRFToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // الحصول على معرف الحصة من الصفحة
    const lessonId = window.lessonId || document.querySelector('[data-lesson-id]')?.dataset.lessonId;
    
    if (lessonId) {
        const monitor = new TeacherAttendanceMonitor(lessonId, 'live');
        window.teacherAttendanceMonitor = monitor;
    } else {
        console.error('❌ لم يتم العثور على معرف الحصة');
    }
});
