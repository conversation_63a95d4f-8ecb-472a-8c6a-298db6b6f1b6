"""
نظام إدارة الحصص الموحد مع منع التلاعب
"""

import secrets
import string
from datetime import timedelta
from django.utils import timezone
from django.conf import settings
from django.contrib.auth import get_user_model
from .models import LiveLesson, UniversalLessonMonitoring

User = get_user_model()


class UniversalLessonManager:
    """مدير موحد لجميع أنواع الحصص مع نظام مراقبة متقدم"""
    
    # إعدادات Heartbeat
    HEARTBEAT_SETTINGS = {
        'interval_seconds': 20,  # كل 20 ثانية
        'max_missed_beats': 3,   # 3 نبضات مفقودة = تحذير (60 ثانية)
        'critical_missed_beats': 6,  # 6 نبضات = حالة حرجة (120 ثانية)
        'auto_disconnect_beats': 10,  # 10 نبضات = قطع تلقائي (200 ثانية)
        'fraud_detection_beats': 15,  # 15 نبضة = اكتشاف تلاعب (300 ثانية)
    }
    
    # مستويات التحذير
    WARNING_LEVELS = {
        'info': {
            'threshold': 3,
            'message': 'تحذير: يرجى التأكد من استقرار الاتصال',
            'action': 'notification_only',
            'color': 'yellow'
        },
        'warning': {
            'threshold': 6,
            'message': 'تحذير هام: قد يتم اعتبار حضورك غير صحيح',
            'action': 'popup_warning',
            'color': 'orange'
        },
        'critical': {
            'threshold': 10,
            'message': 'تحذير نهائي: سيتم قطع الحضور خلال 30 ثانية',
            'action': 'countdown_warning',
            'color': 'red'
        },
        'disconnect': {
            'threshold': 15,
            'message': 'تم قطع الحضور لعدم الاستجابة - تم اكتشاف تلاعب محتمل',
            'action': 'force_disconnect',
            'color': 'red'
        }
    }

    @staticmethod
    def start_lesson_monitoring(lesson_type, lesson_id, user_id, user_role):
        """بدء مراقبة أي نوع من الحصص"""
        
        try:
            user = User.objects.get(id=user_id)
            
            # إنشاء سجل مراقبة موحد
            monitoring_data = {
                'user': user,
                'user_role': user_role,
                'intended_join_time': timezone.now(),
                'status': 'intended'
            }
            
            # ربط مع نوع الحصة المناسب
            if lesson_type == 'live':
                lesson = LiveLesson.objects.get(id=lesson_id)
                monitoring_data['live_lesson'] = lesson
            elif lesson_type == 'scheduled':
                from subscriptions.models import ScheduledLesson
                lesson = ScheduledLesson.objects.get(id=lesson_id)
                monitoring_data['scheduled_lesson'] = lesson
            else:
                raise ValueError(f"نوع حصة غير مدعوم: {lesson_type}")
            
            # التحقق من عدم وجود مراقبة سابقة
            existing_monitoring = UniversalLessonMonitoring.objects.filter(
                user=user,
                **{f'{lesson_type}_lesson': lesson}
            ).first()
            
            if existing_monitoring:
                # تحديث المراقبة الموجودة
                existing_monitoring.intended_join_time = timezone.now()
                existing_monitoring.status = 'intended'
                existing_monitoring.save()
                monitoring = existing_monitoring
            else:
                # إنشاء سجل مراقبة جديد
                monitoring = UniversalLessonMonitoring.objects.create(**monitoring_data)
            
            # إنشاء رابط خارجي للحصة
            external_url = UniversalLessonManager.generate_external_lesson_url(
                lesson_type, lesson, user_id, user_role
            )
            
            # حفظ معلومات الجلسة الخارجية
            monitoring.external_session_url = external_url
            monitoring.save()
            
            return {
                'success': True,
                'monitoring_id': monitoring.id,
                'external_url': external_url,
                'lesson_info': UniversalLessonManager.get_lesson_info(lesson),
                'heartbeat_interval': UniversalLessonManager.HEARTBEAT_SETTINGS['interval_seconds'],
                'monitoring_settings': UniversalLessonManager.HEARTBEAT_SETTINGS
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    @staticmethod
    def generate_external_lesson_url(lesson_type, lesson, user_id, user_role):
        """إنشاء رابط خارجي موحد للحصة"""
        
        # تحديد room_id
        if hasattr(lesson, 'jitsi_room_id') and lesson.jitsi_room_id:
            room_id = lesson.jitsi_room_id
        else:
            # إنشاء room_id جديد
            timestamp = timezone.now().strftime('%Y%m%d%H%M')
            random_part = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(6))
            room_id = f"qurania_{lesson_type}_{lesson.id}_{timestamp}_{random_part}"
            
            # حفظ room_id في الحصة إذا كان ممكناً
            if hasattr(lesson, 'jitsi_room_id'):
                lesson.jitsi_room_id = room_id
                lesson.save()
        
        # إنشاء JWT token للأمان (اختياري)
        jwt_token = UniversalLessonManager.generate_jwt_token(
            room_id=room_id,
            user_id=user_id,
            user_role=user_role,
            lesson_id=lesson.id
        )
        
        # رابط Jitsi خارجي
        jitsi_server = getattr(settings, 'JITSI_DOMAIN', 'meet.jit.si')
        
        # إضافة معاملات للرابط
        params = []
        if jwt_token:
            params.append(f"jwt={jwt_token}")
        
        # إضافة اسم المستخدم
        user = User.objects.get(id=user_id)
        display_name = user.get_full_name()
        if user_role == 'teacher':
            display_name += " (معلم)"
        elif user_role == 'student':
            display_name += " (طالب)"
        
        params.append(f"displayName={display_name}")
        
        # بناء الرابط النهائي
        base_url = f"https://{jitsi_server}/{room_id}"
        if params:
            base_url += "?" + "&".join(params)
        
        return base_url

    @staticmethod
    def generate_jwt_token(room_id, user_id, user_role, lesson_id):
        """إنشاء JWT token للأمان (اختياري)"""
        # يمكن تطبيق JWT لاحقاً إذا لزم الأمر
        # هذا يتطلب مكتبة PyJWT وإعدادات خاصة
        return None

    @staticmethod
    def get_lesson_info(lesson):
        """الحصول على معلومات الحصة"""
        if hasattr(lesson, 'title'):
            # حصة مباشرة
            return {
                'title': lesson.title,
                'teacher': lesson.teacher.get_full_name() if lesson.teacher else 'غير محدد',
                'student': lesson.student.get_full_name() if lesson.student else 'غير محدد',
                'scheduled_date': lesson.scheduled_date,
                'duration_minutes': lesson.duration_minutes
            }
        elif hasattr(lesson, 'lesson_number'):
            # حصة مجدولة
            return {
                'title': f"حصة رقم {lesson.lesson_number}",
                'teacher': lesson.teacher.get_full_name() if lesson.teacher else 'غير محدد',
                'student': lesson.student.get_full_name() if lesson.student else 'غير محدد',
                'scheduled_date': lesson.scheduled_date,
                'duration_minutes': lesson.duration_minutes
            }
        else:
            return {
                'title': 'حصة غير محددة',
                'teacher': 'غير محدد',
                'student': 'غير محدد',
                'scheduled_date': None,
                'duration_minutes': 0
            }

    @staticmethod
    def process_heartbeat(monitoring_id, heartbeat_data):
        """معالجة heartbeat موحدة لجميع أنواع الحصص"""
        
        try:
            monitoring = UniversalLessonMonitoring.objects.get(id=monitoring_id)
            
            # تحديث آخر heartbeat
            monitoring.last_heartbeat = timezone.now()
            monitoring.total_heartbeats_received += 1
            monitoring.consecutive_missed = 0
            
            # تحديث بيانات المراقبة
            if heartbeat_data.get('page_visible') == False:
                monitoring.page_visibility_changes += 1
                monitoring.add_suspicious_activity(
                    'page_hidden',
                    'تم إخفاء صفحة المراقبة',
                    'low'
                )
            
            # تحديث معلومات المتصفح
            if heartbeat_data.get('browser_info'):
                monitoring.browser_info.update(heartbeat_data['browser_info'])
            
            # تحليل جودة الحضور
            quality_score = monitoring.calculate_quality_score()
            monitoring.quality_score = quality_score
            monitoring.attendance_reliability = monitoring.calculate_heartbeat_reliability()
            
            # فحص التحذيرات
            warning_level, warning_config = UniversalLessonManager.evaluate_attendance(monitoring)
            
            if warning_level != 'normal':
                monitoring.add_warning(
                    warning_level,
                    warning_config['message'],
                    warning_config.get('action')
                )
                monitoring.status = warning_level
            else:
                monitoring.status = 'active'
            
            # فحص التلاعب
            if monitoring.is_fraud_suspected():
                monitoring.add_suspicious_activity(
                    'fraud_suspected',
                    'تم اكتشاف نمط مشبوه في الحضور',
                    'high'
                )
            
            monitoring.save()
            
            return {
                'status': 'success',
                'monitoring_status': monitoring.status,
                'quality_score': monitoring.quality_score,
                'attendance_reliability': monitoring.attendance_reliability,
                'warning_level': warning_level,
                'warning_message': warning_config.get('message', ''),
                'heartbeat_count': monitoring.total_heartbeats_received,
                'fraud_risk_level': monitoring.get_fraud_risk_level(),
                'page_visibility_changes': monitoring.page_visibility_changes
            }
            
        except UniversalLessonMonitoring.DoesNotExist:
            return {'status': 'error', 'message': 'سجل المراقبة غير موجود'}
        except Exception as e:
            return {'status': 'error', 'message': str(e)}

    @staticmethod
    def evaluate_attendance(monitoring):
        """تقييم حالة الحضور وتحديد مستوى التحذير"""
        missed = monitoring.consecutive_missed
        
        for level, config in UniversalLessonManager.WARNING_LEVELS.items():
            if missed >= config['threshold']:
                return level, config
        
        return 'normal', {'action': 'none', 'color': 'green', 'message': ''}

    @staticmethod
    def confirm_attendance(monitoring_id):
        """تأكيد الحضور الفعلي"""
        try:
            monitoring = UniversalLessonMonitoring.objects.get(id=monitoring_id)
            monitoring.confirmed_join_time = timezone.now()
            monitoring.status = 'confirmed'
            monitoring.save()
            
            return {'status': 'success', 'message': 'تم تأكيد الحضور بنجاح'}
        except UniversalLessonMonitoring.DoesNotExist:
            return {'status': 'error', 'message': 'سجل المراقبة غير موجود'}

    @staticmethod
    def mark_attendance_leave(monitoring_id):
        """تسجيل مغادرة الحصة"""
        try:
            monitoring = UniversalLessonMonitoring.objects.get(id=monitoring_id)
            monitoring.left_at = timezone.now()
            monitoring.status = 'completed'
            monitoring.save()
            
            return {'status': 'success', 'message': 'تم تسجيل المغادرة بنجاح'}
        except UniversalLessonMonitoring.DoesNotExist:
            return {'status': 'error', 'message': 'سجل المراقبة غير موجود'}
